"""
Stock scanner module for the MassiveScan trading bot.
Scans the entire stock market for trading opportunities.
"""

import pandas as pd
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from config import config
from data_provider import data_provider
from technical_analysis import analyzer
from models import TradingSignal, MarketData
from logger import log_info, log_debug, log_warning, log_error, log_scanner_hit
from database import db

class StockScanner:
    """Stock market scanner for finding trading opportunities"""

    def __init__(self):
        self.is_scanning = False
        self.scan_count = 0
        self.signals_found = 0
        self.last_scan_time = None
        self.scanned_symbols = set()
        self.blacklisted_symbols = set()
        self.demo_mode = False  # For testing when market is closed

        # Performance tracking
        self.scan_times = []
        self.max_scan_history = 100
        
    def get_scannable_universe(self) -> List[str]:
        """Get list of stocks to scan"""
        try:
            log_info("Building scannable universe...")
            
            # Get stocks from multiple sources
            all_stocks = []
            
            # 1. Stock screener with our criteria
            screener_stocks = data_provider.get_stock_screener(
                min_market_cap=config.trading.min_market_cap,
                min_volume=config.trading.min_volume,
                min_price=config.trading.min_price,
                max_price=config.trading.max_price,
                limit=2000
            )
            
            if screener_stocks:
                all_stocks.extend([stock['symbol'] for stock in screener_stocks])
                log_info(f"Added {len(screener_stocks)} stocks from screener")
            
            # 2. Most active stocks
            active_stocks = data_provider.get_active_stocks()
            if active_stocks:
                active_symbols = [stock['symbol'] for stock in active_stocks[:100]]
                all_stocks.extend(active_symbols)
                log_info(f"Added {len(active_symbols)} active stocks")
            
            # 3. Top gainers (might have momentum)
            gainers = data_provider.get_gainers()
            if gainers:
                gainer_symbols = [stock['symbol'] for stock in gainers[:50]]
                all_stocks.extend(gainer_symbols)
                log_info(f"Added {len(gainer_symbols)} gaining stocks")
            
            # 4. Top losers (might bounce)
            losers = data_provider.get_losers()
            if losers:
                loser_symbols = [stock['symbol'] for stock in losers[:50]]
                all_stocks.extend(loser_symbols)
                log_info(f"Added {len(loser_symbols)} losing stocks")
            
            # Remove duplicates and blacklisted symbols
            unique_symbols = list(set(all_stocks))
            filtered_symbols = [s for s in unique_symbols if s not in self.blacklisted_symbols]
            
            log_info(f"Scannable universe: {len(filtered_symbols)} symbols")
            return filtered_symbols
            
        except Exception as e:
            log_error(f"Failed to build scannable universe: {e}")
            return []
    
    def scan_symbol(self, symbol: str) -> List[TradingSignal]:
        """Scan a single symbol for trading signals"""
        try:
            # Get market data
            market_data = data_provider.get_market_data(symbol)
            if not market_data:
                return []
            
            # Basic filters
            if not self._passes_basic_filters(market_data):
                return []
            
            # Get intraday data for technical analysis
            intraday_data = data_provider.get_intraday_data(symbol, "1min")
            if not intraday_data or len(intraday_data) < 50:
                return []
            
            # Convert to DataFrame
            df = pd.DataFrame(intraday_data)
            
            # Ensure we have the required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_columns):
                return []
            
            # Convert data types
            for col in required_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Remove any rows with NaN values
            df = df.dropna()
            
            if len(df) < 50:
                return []
            
            # Sort by date to ensure chronological order
            if 'date' in df.columns:
                df = df.sort_values('date')
            
            # Run technical analysis
            signals = analyzer.analyze_stock(symbol, df, market_data)
            
            # AI ENHANCEMENT - Process signals through AI engine
            try:
                from ai_engine import ai_engine

                # Get market data for AI analysis
                market_data = {
                    'timestamp': datetime.now(),
                    'total_signals': len(signals),
                    'scan_duration': time.time() - scan_start_time
                }

                # Enhance signals with AI
                ai_signals = ai_engine.process_signals(signals, market_data)

                # Filter by AI-enhanced criteria
                filtered_signals = []
                for ai_signal in ai_signals:
                    should_trade, reason = ai_engine.should_trade_signal(ai_signal)
                    if should_trade:
                        # Convert back to regular signal with AI enhancements
                        enhanced_signal = next(s for s in signals if s.symbol == ai_signal.symbol)
                        enhanced_signal.confidence = ai_signal.enhanced_confidence
                        enhanced_signal.expected_profit = ai_signal.profit_prediction
                        enhanced_signal.ai_enhanced = True
                        enhanced_signal.market_regime = ai_signal.market_regime
                        enhanced_signal.optimal_hold_time = ai_signal.optimal_hold_time
                        filtered_signals.append(enhanced_signal)

                        log_info(f"AI APPROVED: {ai_signal.symbol} - {reason}")
                    else:
                        log_debug(f"AI REJECTED: {ai_signal.symbol} - {reason}")

                log_info(f"AI filtered {len(signals)} → {len(filtered_signals)} signals")

            except Exception as e:
                log_error(f"AI enhancement failed, using fallback: {e}")
                # Fallback to original filtering
                filtered_signals = []
                for signal in signals:
                    if (signal.confidence >= config.trading.min_signal_confidence and
                        signal.risk_reward_ratio >= 2.0 and
                        signal.expected_profit >= 1.0):
                        filtered_signals.append(signal)
                    
                    # Log the signal
                    log_scanner_hit(
                        symbol, 
                        signal.signal_type.value, 
                        signal.confidence,
                        {
                            'entry_price': signal.entry_price,
                            'target_price': signal.target_price,
                            'expected_profit': signal.expected_profit,
                            'risk_reward': signal.risk_reward_ratio
                        }
                    )
                    
                    # Save signal to database
                    db.save_signal(signal)
            
            return filtered_signals
            
        except Exception as e:
            log_debug(f"Error scanning {symbol}: {e}")
            return []
    
    def _passes_basic_filters(self, market_data: MarketData) -> bool:
        """Check if stock passes basic filters"""
        try:
            # Price filters
            if market_data.price < config.trading.min_price or market_data.price > config.trading.max_price:
                return False
            
            # Volume filter
            if market_data.volume < config.trading.min_volume:
                return False
            
            # Market cap filter (if available)
            if market_data.market_cap and market_data.market_cap < config.trading.min_market_cap:
                return False
            
            # Avoid penny stocks and very expensive stocks
            if market_data.price < 1.0 or market_data.price > 1000.0:
                return False
            
            # Check for reasonable spread (high-low range)
            if market_data.high > 0 and market_data.low > 0:
                spread_pct = (market_data.high - market_data.low) / market_data.low
                if spread_pct > 0.20:  # Avoid stocks with >20% intraday range
                    return False
            
            return True
            
        except Exception as e:
            log_debug(f"Error in basic filters for {market_data.symbol}: {e}")
            return False
    
    def scan_batch(self, symbols: List[str], batch_size: int = 10) -> List[TradingSignal]:
        """Scan a batch of symbols concurrently"""
        all_signals = []
        
        try:
            with ThreadPoolExecutor(max_workers=config.system.max_worker_threads) as executor:
                # Submit scanning tasks
                future_to_symbol = {
                    executor.submit(self.scan_symbol, symbol): symbol 
                    for symbol in symbols[:batch_size]
                }
                
                # Collect results
                for future in as_completed(future_to_symbol, timeout=60):
                    symbol = future_to_symbol[future]
                    try:
                        signals = future.result()
                        if signals:
                            all_signals.extend(signals)
                            log_debug(f"Found {len(signals)} signals for {symbol}")
                    except Exception as e:
                        log_debug(f"Error scanning {symbol}: {e}")
                        # Add to blacklist if consistently failing
                        self.blacklisted_symbols.add(symbol)
        
        except Exception as e:
            log_error(f"Error in batch scanning: {e}")
        
        return all_signals
    
    def run_full_scan(self) -> List[TradingSignal]:
        """Run a full market scan"""
        if self.is_scanning:
            log_warning("Scan already in progress")
            return []
        
        self.is_scanning = True
        start_time = time.time()
        all_signals = []
        
        try:
            scan_start_time = time.time()
            log_info("Starting full market scan...")

            # Get scannable universe
            symbols = self.get_scannable_universe()
            if not symbols:
                log_warning("No symbols to scan")
                return []
            
            # Scan in batches to manage resources
            batch_size = 20
            total_batches = (len(symbols) + batch_size - 1) // batch_size
            
            for i in range(0, len(symbols), batch_size):
                batch_num = (i // batch_size) + 1
                batch_symbols = symbols[i:i + batch_size]
                
                log_debug(f"Scanning batch {batch_num}/{total_batches} ({len(batch_symbols)} symbols)")
                
                batch_signals = self.scan_batch(batch_symbols, batch_size)
                all_signals.extend(batch_signals)
                
                # Small delay between batches to avoid overwhelming APIs
                time.sleep(1)
                
                # Check if we should stop (e.g., market closed) - unless in demo mode
                if not self.demo_mode and not data_provider.is_market_open():
                    log_info("Market closed, stopping scan")
                    break
            
            # Update statistics
            self.scan_count += 1
            self.signals_found += len(all_signals)
            self.last_scan_time = datetime.now()
            self.scanned_symbols.update(symbols)
            
            scan_duration = time.time() - start_time
            self.scan_times.append(scan_duration)
            if len(self.scan_times) > self.max_scan_history:
                self.scan_times.pop(0)
            
            log_info(f"Scan completed: {len(all_signals)} signals found in {scan_duration:.1f}s")
            
            # Sort signals by confidence
            all_signals.sort(key=lambda x: x.confidence, reverse=True)
            
            return all_signals
            
        except Exception as e:
            log_error(f"Error in full scan: {e}")
            return []
        
        finally:
            self.is_scanning = False
    
    def get_scan_statistics(self) -> Dict[str, Any]:
        """Get scanning statistics"""
        avg_scan_time = sum(self.scan_times) / len(self.scan_times) if self.scan_times else 0
        
        return {
            'total_scans': self.scan_count,
            'signals_found': self.signals_found,
            'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'avg_scan_time_seconds': avg_scan_time,
            'scanned_symbols_count': len(self.scanned_symbols),
            'blacklisted_symbols_count': len(self.blacklisted_symbols),
            'is_currently_scanning': self.is_scanning
        }
    
    def reset_blacklist(self):
        """Reset the blacklisted symbols (useful for daily reset)"""
        self.blacklisted_symbols.clear()
        log_info("Blacklisted symbols reset")

    def enable_demo_mode(self):
        """Enable demo mode for testing when market is closed"""
        self.demo_mode = True
        log_info("Demo mode enabled - will scan even when market is closed")

    def disable_demo_mode(self):
        """Disable demo mode"""
        self.demo_mode = False
        log_info("Demo mode disabled - will only scan when market is open")
    
    async def run_continuous_scan(self, interval_seconds: int = None):
        """Run continuous scanning at specified intervals"""
        if interval_seconds is None:
            interval_seconds = config.system.scan_interval_seconds
        
        log_info(f"Starting continuous scan with {interval_seconds}s intervals")
        
        while True:
            try:
                # Check if market is open (unless in demo mode)
                if not self.demo_mode and not data_provider.is_market_open():
                    log_info("Market closed, waiting... (Use demo mode to test)")
                    await asyncio.sleep(300)  # Wait 5 minutes when market is closed
                    continue
                
                # Run scan
                signals = self.run_full_scan()
                
                if signals:
                    log_info(f"Continuous scan found {len(signals)} signals")
                    # Here you would typically send signals to the trading engine
                    # For now, we just log the top signals
                    for signal in signals[:5]:  # Log top 5 signals
                        log_info(f"Top signal: {signal.symbol} - {signal.signal_type.value} "
                               f"(confidence: {signal.confidence:.2%})")
                
                # Wait for next scan
                await asyncio.sleep(interval_seconds)
                
            except KeyboardInterrupt:
                log_info("Continuous scan stopped by user")
                break
            except Exception as e:
                log_error(f"Error in continuous scan: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

# Global scanner instance
scanner = StockScanner()

if __name__ == "__main__":
    # Test scanner
    try:
        # Test single symbol scan
        test_symbols = ["AAPL", "TSLA", "MSFT"]
        
        for symbol in test_symbols:
            print(f"\nScanning {symbol}...")
            signals = scanner.scan_symbol(symbol)
            print(f"Found {len(signals)} signals for {symbol}")
            
            for signal in signals:
                print(f"  - {signal.signal_type.value}: {signal.confidence:.2%} confidence")
        
        # Test batch scan
        print(f"\nBatch scanning {len(test_symbols)} symbols...")
        batch_signals = scanner.scan_batch(test_symbols)
        print(f"Batch scan found {len(batch_signals)} total signals")
        
        # Print statistics
        stats = scanner.get_scan_statistics()
        print(f"\nScan statistics: {stats}")
        
    except Exception as e:
        print(f"Scanner test failed: {e}")
        print("Make sure to set your API keys in the .env file")
