"""
Simple GUI test to make sure the window appears and is clickable.
"""

import tkinter as tk
from tkinter import messagebox
import time

def test_button():
    """Test button click"""
    print("But<PERSON> clicked!")
    messagebox.showinfo("Success", "GUI is working! The button was clicked.")

def main():
    """Create a simple test window"""
    root = tk.Tk()
    root.title("MassiveScan - GUI Test")
    root.geometry("400x300")
    root.configure(bg='white')
    
    # Make sure window appears on top
    root.lift()
    root.attributes('-topmost', True)
    root.after_idle(root.attributes, '-topmost', False)
    
    # Title
    title_label = tk.Label(root, text="MassiveScan GUI Test", 
                          font=('Arial', 16, 'bold'), bg='white')
    title_label.pack(pady=20)
    
    # Status
    status_label = tk.Label(root, text="If you can see this window, the GUI is working!", 
                           font=('Arial', 12), bg='white')
    status_label.pack(pady=10)
    
    # Test button
    test_button_widget = tk.Button(root, text="Click Me to Test", 
                                  command=test_button,
                                  bg='#4CAF50', fg='white', 
                                  font=('Arial', 12, 'bold'),
                                  width=20, height=2)
    test_button_widget.pack(pady=20)
    
    # Instructions
    instructions = tk.Label(root, 
                           text="If this window appeared, the main GUI should work too.\nClose this window and run: python gui.py", 
                           font=('Arial', 10), bg='white', justify='center')
    instructions.pack(pady=20)
    
    print("Test GUI window should be visible now...")
    print("If you can see the window, the main GUI will work too.")
    
    root.mainloop()

if __name__ == "__main__":
    print("Starting GUI test...")
    main()
    print("GUI test completed.")
