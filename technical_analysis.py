"""
Technical analysis module for the MassiveScan trading bot.
Implements various technical indicators and signal generation logic.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import pandas_ta as ta

from models import TechnicalIndicators, TradingSignal, SignalType, MarketData
from config import config
from logger import log_debug, log_info, log_warning

class TechnicalAnalyzer:
    """Technical analysis engine for generating trading signals"""
    
    def __init__(self):
        self.min_data_points = 50  # Minimum data points for reliable indicators
        
    def calculate_indicators(self, df: pd.DataFrame) -> TechnicalIndicators:
        """Calculate technical indicators from price data"""
        if len(df) < self.min_data_points:
            log_warning(f"Insufficient data points: {len(df)} < {self.min_data_points}")
            return None
        
        try:
            # Ensure required columns exist
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            for col in required_cols:
                if col not in df.columns:
                    log_warning(f"Missing required column: {col}")
                    return None
            
            # Calculate RSI
            rsi = ta.rsi(df['close'], length=14)
            
            # Calculate MACD
            macd_data = ta.macd(df['close'])
            macd = macd_data['MACD_12_26_9'] if 'MACD_12_26_9' in macd_data.columns else None
            macd_signal = macd_data['MACDs_12_26_9'] if 'MACDs_12_26_9' in macd_data.columns else None
            
            # Calculate Bollinger Bands
            bb_data = ta.bbands(df['close'], length=20)
            bb_upper = bb_data['BBU_20_2.0'] if 'BBU_20_2.0' in bb_data.columns else None
            bb_middle = bb_data['BBM_20_2.0'] if 'BBM_20_2.0' in bb_data.columns else None
            bb_lower = bb_data['BBL_20_2.0'] if 'BBL_20_2.0' in bb_data.columns else None
            
            # Calculate Moving Averages
            sma_20 = ta.sma(df['close'], length=20)
            sma_50 = ta.sma(df['close'], length=50)
            ema_12 = ta.ema(df['close'], length=12)
            ema_26 = ta.ema(df['close'], length=26)
            
            # Calculate Volume SMA
            volume_sma = ta.sma(df['volume'], length=20)
            
            # Calculate ATR (Average True Range)
            atr = ta.atr(df['high'], df['low'], df['close'], length=14)
            
            # Get latest values
            latest_idx = -1
            
            indicators = TechnicalIndicators(
                symbol=df.get('symbol', [''])[0] if 'symbol' in df.columns else '',
                timestamp=datetime.now(),
                rsi=float(rsi.iloc[latest_idx]) if rsi is not None and not pd.isna(rsi.iloc[latest_idx]) else None,
                macd=float(macd.iloc[latest_idx]) if macd is not None and not pd.isna(macd.iloc[latest_idx]) else None,
                macd_signal=float(macd_signal.iloc[latest_idx]) if macd_signal is not None and not pd.isna(macd_signal.iloc[latest_idx]) else None,
                bb_upper=float(bb_upper.iloc[latest_idx]) if bb_upper is not None and not pd.isna(bb_upper.iloc[latest_idx]) else None,
                bb_middle=float(bb_middle.iloc[latest_idx]) if bb_middle is not None and not pd.isna(bb_middle.iloc[latest_idx]) else None,
                bb_lower=float(bb_lower.iloc[latest_idx]) if bb_lower is not None and not pd.isna(bb_lower.iloc[latest_idx]) else None,
                sma_20=float(sma_20.iloc[latest_idx]) if sma_20 is not None and not pd.isna(sma_20.iloc[latest_idx]) else None,
                sma_50=float(sma_50.iloc[latest_idx]) if sma_50 is not None and not pd.isna(sma_50.iloc[latest_idx]) else None,
                ema_12=float(ema_12.iloc[latest_idx]) if ema_12 is not None and not pd.isna(ema_12.iloc[latest_idx]) else None,
                ema_26=float(ema_26.iloc[latest_idx]) if ema_26 is not None and not pd.isna(ema_26.iloc[latest_idx]) else None,
                volume_sma=float(volume_sma.iloc[latest_idx]) if volume_sma is not None and not pd.isna(volume_sma.iloc[latest_idx]) else None,
                atr=float(atr.iloc[latest_idx]) if atr is not None and not pd.isna(atr.iloc[latest_idx]) else None
            )
            
            return indicators
            
        except Exception as e:
            log_warning(f"Error calculating indicators: {e}")
            return None
    
    def detect_momentum_breakout(self, df: pd.DataFrame, market_data: MarketData) -> Optional[TradingSignal]:
        """Detect momentum breakout signals"""
        if len(df) < 20:
            return None
        
        try:
            # Calculate price change and volume ratio
            current_price = market_data.price
            prev_close = df['close'].iloc[-2]
            price_change_pct = (current_price - prev_close) / prev_close
            
            volume_ratio = market_data.volume / market_data.avg_volume if market_data.avg_volume > 0 else 1
            
            # Check for momentum breakout conditions
            momentum_threshold = config.trading.momentum_threshold
            volume_threshold = config.trading.volume_surge_multiplier
            
            if (abs(price_change_pct) >= momentum_threshold and 
                volume_ratio >= volume_threshold):
                
                # Determine direction
                is_bullish = price_change_pct > 0
                
                # Calculate targets
                atr = self._calculate_atr(df)
                if not atr:
                    return None
                
                if is_bullish:
                    target_price = current_price + config.trading.target_profit_dollars / 100  # Rough estimate
                    stop_loss_price = current_price - config.trading.stop_loss_dollars / 100
                else:
                    target_price = current_price - config.trading.target_profit_dollars / 100
                    stop_loss_price = current_price + config.trading.stop_loss_dollars / 100
                
                # Calculate confidence based on momentum and volume
                confidence = min(0.95, 0.5 + (abs(price_change_pct) * 10) + (volume_ratio * 0.1))
                
                signal = TradingSignal(
                    symbol=market_data.symbol,
                    signal_type=SignalType.MOMENTUM_BREAKOUT,
                    timestamp=datetime.now(),
                    confidence=confidence,
                    entry_price=current_price,
                    target_price=target_price,
                    stop_loss_price=stop_loss_price,
                    expected_profit=config.trading.target_profit_dollars,
                    risk_reward_ratio=abs(target_price - current_price) / abs(current_price - stop_loss_price),
                    volume_ratio=volume_ratio,
                    technical_data={
                        'price_change_pct': price_change_pct,
                        'volume_ratio': volume_ratio,
                        'atr': atr,
                        'is_bullish': is_bullish
                    }
                )
                
                return signal
                
        except Exception as e:
            log_warning(f"Error detecting momentum breakout: {e}")
        
        return None
    
    def detect_rsi_signals(self, df: pd.DataFrame, market_data: MarketData) -> Optional[TradingSignal]:
        """Detect RSI oversold/overbought signals"""
        indicators = self.calculate_indicators(df)
        if not indicators or indicators.rsi is None:
            return None
        
        try:
            current_price = market_data.price
            rsi = indicators.rsi
            
            signal_type = None
            is_bullish = False
            
            # Check for oversold condition (potential buy signal)
            if rsi <= config.trading.rsi_oversold:
                signal_type = SignalType.RSI_OVERSOLD
                is_bullish = True
            
            # Check for overbought condition (potential sell signal)
            elif rsi >= config.trading.rsi_overbought:
                signal_type = SignalType.RSI_OVERBOUGHT
                is_bullish = False
            
            if signal_type:
                # Calculate targets
                atr = indicators.atr or (current_price * 0.02)  # Fallback to 2% of price
                
                if is_bullish:
                    target_price = current_price + config.trading.target_profit_dollars / 100
                    stop_loss_price = current_price - config.trading.stop_loss_dollars / 100
                else:
                    target_price = current_price - config.trading.target_profit_dollars / 100
                    stop_loss_price = current_price + config.trading.stop_loss_dollars / 100
                
                # Calculate confidence based on RSI extremity
                if is_bullish:
                    confidence = (config.trading.rsi_oversold - rsi) / config.trading.rsi_oversold
                else:
                    confidence = (rsi - config.trading.rsi_overbought) / (100 - config.trading.rsi_overbought)
                
                confidence = max(0.1, min(0.9, confidence))
                
                signal = TradingSignal(
                    symbol=market_data.symbol,
                    signal_type=signal_type,
                    timestamp=datetime.now(),
                    confidence=confidence,
                    entry_price=current_price,
                    target_price=target_price,
                    stop_loss_price=stop_loss_price,
                    expected_profit=config.trading.target_profit_dollars,
                    risk_reward_ratio=abs(target_price - current_price) / abs(current_price - stop_loss_price),
                    technical_data={
                        'rsi': rsi,
                        'atr': atr,
                        'is_bullish': is_bullish
                    }
                )
                
                return signal
                
        except Exception as e:
            log_warning(f"Error detecting RSI signals: {e}")
        
        return None
    
    def detect_volume_surge(self, df: pd.DataFrame, market_data: MarketData) -> Optional[TradingSignal]:
        """Detect volume surge with price movement"""
        if len(df) < 20:
            return None
        
        try:
            current_volume = market_data.volume
            avg_volume = market_data.avg_volume or df['volume'].tail(20).mean()
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # Check for significant volume surge
            if volume_ratio >= config.trading.volume_surge_multiplier:
                current_price = market_data.price
                prev_close = df['close'].iloc[-1]
                price_change_pct = (current_price - prev_close) / prev_close
                
                # Only signal if there's also price movement
                if abs(price_change_pct) >= 0.005:  # 0.5% minimum price movement
                    is_bullish = price_change_pct > 0
                    
                    # Calculate targets
                    if is_bullish:
                        target_price = current_price + config.trading.target_profit_dollars / 100
                        stop_loss_price = current_price - config.trading.stop_loss_dollars / 100
                    else:
                        target_price = current_price - config.trading.target_profit_dollars / 100
                        stop_loss_price = current_price + config.trading.stop_loss_dollars / 100
                    
                    # Confidence based on volume ratio and price movement
                    confidence = min(0.9, 0.3 + (volume_ratio * 0.1) + (abs(price_change_pct) * 20))
                    
                    signal = TradingSignal(
                        symbol=market_data.symbol,
                        signal_type=SignalType.VOLUME_SURGE,
                        timestamp=datetime.now(),
                        confidence=confidence,
                        entry_price=current_price,
                        target_price=target_price,
                        stop_loss_price=stop_loss_price,
                        expected_profit=config.trading.target_profit_dollars,
                        risk_reward_ratio=abs(target_price - current_price) / abs(current_price - stop_loss_price),
                        volume_ratio=volume_ratio,
                        technical_data={
                            'volume_ratio': volume_ratio,
                            'price_change_pct': price_change_pct,
                            'avg_volume': avg_volume,
                            'is_bullish': is_bullish
                        }
                    )
                    
                    return signal
                    
        except Exception as e:
            log_warning(f"Error detecting volume surge: {e}")
        
        return None
    
    def detect_vwap_bounce(self, df: pd.DataFrame, market_data: MarketData) -> Optional[TradingSignal]:
        """Detect VWAP bounce signals"""
        if len(df) < 20:
            return None
        
        try:
            # Calculate VWAP
            vwap = self._calculate_vwap(df)
            if vwap is None:
                return None
            
            current_price = market_data.price
            price_distance_pct = abs(current_price - vwap) / vwap
            
            # Look for price near VWAP (within 0.5%)
            if price_distance_pct <= 0.005:
                # Check recent price action to determine bounce direction
                recent_prices = df['close'].tail(5)
                is_bouncing_up = recent_prices.iloc[-1] > recent_prices.iloc[-3]
                
                if is_bouncing_up:
                    target_price = current_price + config.trading.target_profit_dollars / 100
                    stop_loss_price = vwap - (config.trading.stop_loss_dollars / 100)
                else:
                    target_price = current_price - config.trading.target_profit_dollars / 100
                    stop_loss_price = vwap + (config.trading.stop_loss_dollars / 100)
                
                # Confidence based on proximity to VWAP and volume
                volume_ratio = market_data.volume / market_data.avg_volume if market_data.avg_volume > 0 else 1
                confidence = min(0.8, 0.4 + (1 - price_distance_pct) * 2 + (volume_ratio * 0.1))
                
                signal = TradingSignal(
                    symbol=market_data.symbol,
                    signal_type=SignalType.VWAP_BOUNCE,
                    timestamp=datetime.now(),
                    confidence=confidence,
                    entry_price=current_price,
                    target_price=target_price,
                    stop_loss_price=stop_loss_price,
                    expected_profit=config.trading.target_profit_dollars,
                    risk_reward_ratio=abs(target_price - current_price) / abs(current_price - stop_loss_price),
                    volume_ratio=volume_ratio,
                    technical_data={
                        'vwap': vwap,
                        'price_distance_pct': price_distance_pct,
                        'is_bouncing_up': is_bouncing_up,
                        'volume_ratio': volume_ratio
                    }
                )
                
                return signal
                
        except Exception as e:
            log_warning(f"Error detecting VWAP bounce: {e}")
        
        return None
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> Optional[float]:
        """Calculate Average True Range"""
        try:
            atr = ta.atr(df['high'], df['low'], df['close'], length=period)
            return float(atr.iloc[-1]) if atr is not None and not pd.isna(atr.iloc[-1]) else None
        except:
            return None
    
    def _calculate_vwap(self, df: pd.DataFrame) -> Optional[float]:
        """Calculate Volume Weighted Average Price"""
        try:
            # VWAP = Sum(Price * Volume) / Sum(Volume)
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            vwap = (typical_price * df['volume']).sum() / df['volume'].sum()
            return float(vwap)
        except:
            return None
    
    def analyze_stock(self, symbol: str, df: pd.DataFrame, market_data: MarketData) -> List[TradingSignal]:
        """Analyze a stock and return all detected signals"""
        signals = []
        
        try:
            # Run all signal detection methods
            signal_methods = [
                self.detect_momentum_breakout,
                self.detect_rsi_signals,
                self.detect_volume_surge,
                self.detect_vwap_bounce
            ]
            
            for method in signal_methods:
                try:
                    signal = method(df, market_data)
                    if signal and signal.confidence >= 0.3:  # Minimum confidence threshold
                        signals.append(signal)
                        log_debug(f"Signal detected: {symbol} - {signal.signal_type.value} (confidence: {signal.confidence:.2f})")
                except Exception as e:
                    log_warning(f"Error in signal method {method.__name__} for {symbol}: {e}")
            
            # Sort signals by confidence
            signals.sort(key=lambda x: x.confidence, reverse=True)
            
        except Exception as e:
            log_warning(f"Error analyzing stock {symbol}: {e}")
        
        return signals

# Global technical analyzer instance
analyzer = TechnicalAnalyzer()

if __name__ == "__main__":
    # Test technical analysis
    import yfinance as yf
    
    # Get sample data
    ticker = yf.Ticker("AAPL")
    df = ticker.history(period="1d", interval="1m")
    
    if not df.empty:
        # Rename columns to match our format
        df.columns = [col.lower() for col in df.columns]
        
        # Create sample market data
        market_data = MarketData(
            symbol="AAPL",
            timestamp=datetime.now(),
            price=df['close'].iloc[-1],
            volume=int(df['volume'].iloc[-1]),
            open_price=df['open'].iloc[-1],
            high=df['high'].iloc[-1],
            low=df['low'].iloc[-1],
            close=df['close'].iloc[-1],
            avg_volume=int(df['volume'].mean())
        )
        
        # Analyze stock
        signals = analyzer.analyze_stock("AAPL", df, market_data)
        print(f"Found {len(signals)} signals for AAPL")
        
        for signal in signals:
            print(f"Signal: {signal.signal_type.value} - Confidence: {signal.confidence:.2%}")
    else:
        print("No data available for testing")
