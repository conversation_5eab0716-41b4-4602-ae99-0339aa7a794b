"""
Check Alpaca account status and provide setup instructions
"""

from alpaca.trading.client import Trading<PERSON>lient

def check_account_status():
    print("🔍 ALPACA ACCOUNT STATUS CHECK")
    print("=" * 60)
    
    api_key = "PKYIC5KKD7V7MADSGO9G"
    secret_key = "2gRdp0rKDVQNmsIvYsn5Cd4Lp4RxwppYqXkpPwOj"
    
    print("📊 PAPER TRADING ACCOUNT:")
    try:
        paper_client = TradingClient(api_key=api_key, secret_key=secret_key, paper=True)
        paper_account = paper_client.get_account()
        
        print("✅ Paper account connected successfully!")
        print(f"  Account: {paper_account.account_number}")
        print(f"  Status: {paper_account.status}")
        print(f"  Buying Power: ${float(paper_account.buying_power):,.2f}")
        print(f"  Portfolio: ${float(paper_account.portfolio_value):,.2f}")
        print(f"  Trading Blocked: {paper_account.trading_blocked}")
        print(f"  Pattern Day Trader: {paper_account.pattern_day_trader}")
        
    except Exception as e:
        print(f"❌ Paper account error: {e}")
    
    print("\n💰 LIVE TRADING ACCOUNT:")
    try:
        live_client = TradingClient(api_key=api_key, secret_key=secret_key, paper=False)
        live_account = live_client.get_account()
        
        print("✅ Live account connected successfully!")
        print(f"  Account: {live_account.account_number}")
        print(f"  Status: {live_account.status}")
        print(f"  Buying Power: ${float(live_account.buying_power):,.2f}")
        print(f"  Portfolio: ${float(live_account.portfolio_value):,.2f}")
        print(f"  Cash: ${float(live_account.cash):,.2f}")
        print(f"  Trading Blocked: {live_account.trading_blocked}")
        print(f"  Pattern Day Trader: {live_account.pattern_day_trader}")
        
        return True
        
    except Exception as e:
        print(f"❌ Live account error: {e}")
        
        print("\n🔧 POSSIBLE SOLUTIONS:")
        print("1. 📋 Account Setup Required:")
        print("   • Log into https://app.alpaca.markets/")
        print("   • Complete account verification")
        print("   • Fund your account with $30K")
        print("   • Enable live trading permissions")
        
        print("\n2. 🔑 API Key Issues:")
        print("   • Generate new API keys for live trading")
        print("   • Ensure keys have live trading permissions")
        print("   • Check key expiration dates")
        
        print("\n3. 📞 Contact Alpaca Support:")
        print("   • Email: <EMAIL>")
        print("   • Mention authorization error ********")
        print("   • Request live trading activation")
        
        return False

def recommend_next_steps():
    print("\n" + "=" * 60)
    print("📋 RECOMMENDED NEXT STEPS:")
    print("=" * 60)
    
    print("\n🎯 OPTION 1: START WITH PAPER TRADING")
    print("  • Test the bot with paper money first")
    print("  • Verify all strategies work correctly")
    print("  • No risk while testing")
    print("  • Switch to live when account is ready")
    
    print("\n💰 OPTION 2: ACTIVATE LIVE TRADING")
    print("  • Complete Alpaca account verification")
    print("  • Fund account with your $30K")
    print("  • Generate live trading API keys")
    print("  • Update bot configuration")
    
    print("\n🚀 OPTION 3: HYBRID APPROACH")
    print("  • Start with paper trading today")
    print("  • Work on live account activation")
    print("  • Switch to live trading once ready")
    print("  • Keep testing strategies in paper")
    
    print("\n⚡ IMMEDIATE ACTION:")
    print("  • I can configure the bot for paper trading now")
    print("  • You can test all strategies with $60K paper money")
    print("  • Switch to live trading when account is ready")
    print("  • All profits will be simulated but strategies proven")

if __name__ == "__main__":
    live_ready = check_account_status()
    recommend_next_steps()
    
    if not live_ready:
        print("\n🤖 BOT CONFIGURATION RECOMMENDATION:")
        print("  Configure for paper trading while setting up live account")
        print("  This allows immediate testing and strategy validation")
    else:
        print("\n🎉 READY FOR LIVE TRADING!")
        print("  Your account is ready for $100 daily profit target!")
