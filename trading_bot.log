2025-06-11 08:22:04 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:22:05 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:22:05 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:22:05 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,926.62
2025-06-11 08:22:05 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:22:12 | INFO     | MassiveScan | info:70 | Stock screener returned 5 stocks
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,850.34
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:29:48 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:29:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | Bot started from GUI
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:29:56 | WARNING  | MassiveScan | warning:78 | Market appears to be closed
2025-06-11 08:29:56 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:29:56 | ERROR    | MassiveScan | error:82 | Failed to connect to broker
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | Cleaning up resources...
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | Final Performance Summary:
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 |   Total trades: 0
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 |   Win rate: 0.0%
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 |   Total P&L: $0.00
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 |   Average P&L: $0.00
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | MassiveScan Trading Bot stopped
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,850.93
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:29:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:04 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:04 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:30:09 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:14 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:19 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:24 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:29 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:34 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:39 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:44 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:49 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:04 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:10 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:15 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:20 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:25 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:30 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:35 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:40 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:45 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:50 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:55 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:00 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:05 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:11 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:11 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:32:16 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Demo mode enabled - will scan even when market is closed
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Stock screener returned 483 stocks
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Added 483 stocks from screener
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:32:17 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:32:17 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:32:17 | INFO     | MassiveScan | info:70 | Scannable universe: 610 symbols
2025-06-11 08:32:21 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:26 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:33:50 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:33:50 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Stock screener returned 513 stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Added 513 stocks from screener
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Scannable universe: 616 symbols
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Stock screener returned 516 stocks
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Added 516 stocks from screener
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:34:01 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:34:01 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:34:01 | INFO     | MassiveScan | info:70 | Scannable universe: 619 symbols
2025-06-11 08:34:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JOBY | rsi_overbought | Confidence: 64.96% | entry_price: 9.915 | target_price: 9.905 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:34:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:34:05 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:08 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GILD | rsi_oversold | Confidence: 59.98% | entry_price: 109.98 | target_price: 109.99000000000001 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:09 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EEM | rsi_overbought | Confidence: 50.34% | entry_price: 47.83 | target_price: 47.82 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:34:15 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SBUX | rsi_overbought | Confidence: 70.95% | entry_price: 93.4568 | target_price: 93.4468 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:17 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:34:23 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:34:24 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:24 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:31 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SLV | rsi_oversold | Confidence: 63.16% | entry_price: 32.9399 | target_price: 32.9499 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:34:31 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:34:33 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BIL | vwap_bounce | Confidence: 80.00% | entry_price: 91.5307 | target_price: 91.52069999999999 | expected_profit: 1.0 | risk_reward: 2.511842290125823
2025-06-11 08:34:33 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | momentum_breakout | Confidence: 95.00% | entry_price: 17.98 | target_price: 17.97 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:36 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | volume_surge | Confidence: 90.00% | entry_price: 17.98 | target_price: 17.97 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:36 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MP | rsi_oversold | Confidence: 78.61% | entry_price: 23.91 | target_price: 23.92 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDD | rsi_overbought | Confidence: 57.64% | entry_price: 103.92 | target_price: 103.91 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:39 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:34:39 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | FCX | rsi_oversold | Confidence: 64.42% | entry_price: 40.93 | target_price: 40.94 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:34:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:40 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BDX | rsi_overbought | Confidence: 56.05% | entry_price: 175.56 | target_price: 175.55 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 08:34:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PFE | rsi_overbought | Confidence: 60.57% | entry_price: 24.635 | target_price: 24.625 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:44 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SAIL | rsi_overbought | Confidence: 82.02% | entry_price: 22.875 | target_price: 22.865 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:45 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BILI | rsi_overbought | Confidence: 79.20% | entry_price: 21.2 | target_price: 21.189999999999998 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:49 | ERROR    | MassiveScan | error:82 | Error parsing market data for OAS: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:34:51 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMSC | momentum_breakout | Confidence: 86.51% | entry_price: 28.03 | target_price: 28.02 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:51 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:51 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMSC | rsi_oversold | Confidence: 80.45% | entry_price: 28.03 | target_price: 28.040000000000003 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:52 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:34:53 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TIGR | rsi_overbought | Confidence: 74.01% | entry_price: 8.805 | target_price: 8.795 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:34:53 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:34:53 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:58 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CLF | rsi_oversold | Confidence: 69.93% | entry_price: 7.335 | target_price: 7.345 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 08:35:05 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:35:08 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:35:11 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:35:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:35:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:35:23 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:35:36 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:35:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CSCO | rsi_oversold | Confidence: 56.36% | entry_price: 64.29 | target_price: 64.************** | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:35:39 | INFO     | MassiveScan | info:70 | Scan completed: 25 signals found in 99.1s
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,820.02
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:36:27 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:32 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,845.90
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:36:37 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Market is open
2025-06-11 08:36:37 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:37 | ERROR    | MassiveScan | error:82 | Failed to connect to broker
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Cleaning up resources...
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Final Performance Summary:
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 |   Total trades: 0
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 |   Win rate: 0.0%
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 |   Total P&L: $0.00
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 |   Average P&L: $0.00
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | MassiveScan Trading Bot stopped
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Stopping MassiveScan Trading Bot...
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Trade monitoring stopped
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Closing all positions: Bot shutdown
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | All positions closed
2025-06-11 08:36:42 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:47 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:52 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:57 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:02 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:07 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:13 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:23 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,863.30
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:37:26 | INFO     | MassiveScan | info:70 | Market is open
2025-06-11 08:37:26 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:26 | ERROR    | MassiveScan | error:82 | Failed to connect to broker
2025-06-11 08:37:28 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:33 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:38 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:43 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:48 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:53 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:58 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:58 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:37:59 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:37:59 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:37:59 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,865.86
2025-06-11 08:38:03 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,886.68
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 | Market is open
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 | Broker connected - Buying Power: $296,867.68
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 | Daily risk statistics reset
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 | Configuration loaded:
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Target profit per trade: $1.0
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Stop loss per trade: $0.5
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Max daily trades: 100
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Max daily loss limit: $25.0
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Max concurrent positions: 10
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Scan interval: 30s
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Expected trades for $50: 50 trades
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 | MassiveScan Trading Bot initialized successfully
2025-06-11 08:38:08 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:13 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:22 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,923.52
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Market is open
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Broker connected - Buying Power: $296,867.68
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Daily risk statistics reset
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | TRADING STRATEGY - Target: $50 Daily Profit
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Configuration loaded:
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Target profit per trade: $1.0
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Stop loss per trade: $0.5
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Max daily trades: 100
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Max daily loss limit: $25.0
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Max concurrent positions: 10
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Scan interval: 30s
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Expected trades for $50: 50 trades
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | MassiveScan Trading Bot initialized successfully
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Starting MassiveScan Trading Bot...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Starting market scanning...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Stock screener returned 567 stocks
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Added 567 stocks from screener
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:38:24 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:24 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:38:24 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:38:24 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:38:24 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:38:24 | INFO     | MassiveScan | info:70 | Scannable universe: 668 symbols
2025-06-11 08:38:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | IREN | vwap_bounce | Confidence: 80.00% | entry_price: 10.245 | target_price: 10.*************** | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:38:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:38:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BILI | rsi_overbought | Confidence: 62.42% | entry_price: 21.17 | target_price: 21.16 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:38:29 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:32 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:38:32 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:38:34 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | HBAN | vwap_bounce | Confidence: 80.00% | entry_price: 16.285 | target_price: 16.295 | expected_profit: 1.0 | risk_reward: 4.***************
2025-06-11 08:38:35 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:38:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AAL | vwap_bounce | Confidence: 80.00% | entry_price: 11.8287 | target_price: 11.8187 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:38:35 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:38:37 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:38:38 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CLF | rsi_oversold | Confidence: 62.68% | entry_price: 7.27 | target_price: 7.*************** | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 08:38:38 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:38:39 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:40 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:38:44 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VALE | vwap_bounce | Confidence: 80.00% | entry_price: 9.565 | target_price: 9.555 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:38:47 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:38:49 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:50 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:38:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:59 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:38:59 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:39:04 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:09 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:13 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:39:13 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:39:14 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:19 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:24 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:39:24 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:39:24 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:26 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:39:29 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:35 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:38 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | XPEV | rsi_overbought | Confidence: 51.42% | entry_price: 20.955 | target_price: 20.*************** | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Portfolio Value: $101,001.59
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:39:40 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:42 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:39:42 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:39:43 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:39:43 | INFO     | MassiveScan | info:70 | Stock screener returned 583 stocks
2025-06-11 08:39:43 | INFO     | MassiveScan | info:70 | Added 583 stocks from screener
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Scannable universe: 685 symbols
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Stock screener returned 584 stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Added 584 stocks from screener
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:39:45 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:39:45 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:39:45 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:45 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:39:45 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:39:45 | INFO     | MassiveScan | info:70 | Scannable universe: 686 symbols
2025-06-11 08:39:48 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:39:48 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:39:49 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BILI | rsi_overbought | Confidence: 57.71% | entry_price: 21.09 | target_price: 21.08 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:39:50 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:51 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:39:52 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:39:52 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BDXB?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NR?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for BDXB
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for NR
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/UMPQ?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OAS?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for OAS
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ZOOZ?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for ZOOZ
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/DEH?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CNHI?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OSTK?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for CNHI
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for OSTK
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AGNC?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for AGNC
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/STER?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/SNPO?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LGF-B?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for LGF-B
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BECN?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for BECN
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/SOAC?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FPAC?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/CLI?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for FPAC
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WMT?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for WMT
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/LQD?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/ENLC?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/HTLF?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IBDP?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NYCB?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JWN?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for IBDP
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ZOOZ?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LWAC?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for NYCB
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MNTV?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for JWN
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SIL.TO?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for ZOOZ
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for LWAC
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for MNTV
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for SIL.TO
2025-06-11 08:39:55 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:55 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Scan completed: 11 signals found in 92.0s
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Found 11 trading signals
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Starting signal processing...
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Processing signal: IREN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:39:55 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Scan completed: 2 signals found in 11.3s
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for IREN (risk per share: $0.0059)
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for IREN (risk per share: $0.0059)
2025-06-11 08:39:56 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Market order placed: IREN buy 19
2025-06-11 08:39:56 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:39:56 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | IREN | BUY 19 @ $10.2450 | Strategy: vwap_bounce
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Trade executed: e1972db6-6cdf-4ea0-be9a-ffc7ce41cae2 - IREN buy 19
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Trade executed: IREN - Total trades today: 1
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Processing signal: PDCO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:39:57 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:39:58 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:39:58 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:39:58 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:39:58 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PDCO
2025-06-11 08:39:58 | INFO     | MassiveScan | info:70 | Processing signal: HBAN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:39:58 | INFO     | MassiveScan | info:70 | Position size calculated: 12 shares for HBAN (risk per share: $0.0022)
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Position size calculated: 12 shares for HBAN (risk per share: $0.0022)
2025-06-11 08:39:59 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Market order placed: HBAN buy 12
2025-06-11 08:39:59 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:39:59 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | HBAN | BUY 12 @ $16.2850 | Strategy: vwap_bounce
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Trade executed: 4c7d2f26-9579-4ec7-aff5-dc7d381b67eb - HBAN buy 12
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Trade executed: HBAN - Total trades today: 2
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Processing signal: AAL - vwap_bounce (confidence: 80.00%)
2025-06-11 08:40:00 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:00 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for AAL (risk per share: $0.0044)
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for AAL (risk per share: $0.0044)
2025-06-11 08:40:01 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Market order placed: AAL buy 16
2025-06-11 08:40:01 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:40:01 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AAL | BUY 16 @ $11.8287 | Strategy: vwap_bounce
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Trade executed: 1c724dad-5db4-4430-af23-d8b6a36d91dc - AAL buy 16
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Trade executed: AAL - Total trades today: 3
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Processing signal: VALE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for VALE (risk per share: $0.0060)
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for VALE (risk per share: $0.0060)
2025-06-11 08:40:02 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Market order placed: VALE buy 20
2025-06-11 08:40:02 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:40:02 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | VALE | BUY 20 @ $9.5650 | Strategy: vwap_bounce
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Trade executed: d8c4bdb5-1242-4014-8677-f24de9febc15 - VALE buy 20
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Trade executed: VALE - Total trades today: 4
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Processing signal: JWN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:40:03 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:40:04 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:40:04 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:40:04 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:40:04 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for JWN
2025-06-11 08:40:04 | INFO     | MassiveScan | info:70 | Processing signal: PTVE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:40:04 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:40:05 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:05 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:40:05 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:40:05 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:40:05 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PTVE
2025-06-11 08:40:05 | INFO     | MassiveScan | info:70 | Processing signal: ACCD - vwap_bounce (confidence: 80.00%)
2025-06-11 08:40:06 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:40:07 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:40:07 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 08:40:07 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 08:40:07 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for ACCD
2025-06-11 08:40:07 | INFO     | MassiveScan | info:70 | Processing signal: CLF - rsi_oversold (confidence: 62.68%)
2025-06-11 08:40:07 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CLF (risk per share: $0.0050)
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CLF (risk per share: $0.0050)
2025-06-11 08:40:08 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Market order placed: CLF buy 27
2025-06-11 08:40:08 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CLF | BUY 27 @ $7.2700 | Strategy: rsi_oversold
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Trade executed: 890f00ff-db12-4042-b11e-1f5a7fe66f3b - CLF buy 27
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Trade executed: CLF - Total trades today: 5
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Processing signal: BILI - rsi_overbought (confidence: 62.42%)
2025-06-11 08:40:09 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for BILI (risk per share: $0.0050)
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for BILI (risk per share: $0.0050)
2025-06-11 08:40:10 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Market order placed: BILI sell 9
2025-06-11 08:40:10 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | BILI | SELL 9 @ $21.1700 | Strategy: rsi_overbought
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Trade executed: 58586d0e-5c34-44c4-a682-bceb0a15e2f0 - BILI sell 9
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Trade executed: BILI - Total trades today: 6
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Processing signal: XPEV - rsi_overbought (confidence: 51.42%)
2025-06-11 08:40:10 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for XPEV (risk per share: $0.0050)
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for XPEV (risk per share: $0.0050)
2025-06-11 08:40:11 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Market order placed: XPEV sell 9
2025-06-11 08:40:11 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | XPEV | SELL 9 @ $20.9550 | Strategy: rsi_overbought
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Trade executed: ae7c92da-ae73-4825-a513-101c800cebdc - XPEV sell 9
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Trade executed: XPEV - Total trades today: 7
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Starting trade monitoring...
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Trade monitoring started
2025-06-11 08:40:12 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:12 | INFO     | MassiveScan | info:70 | Market order placed: IREN sell 19
2025-06-11 08:40:12 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:40:12 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:40:12 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | IREN | SELL 19 @ $10.1500 | PnL: -$1.80 | Reason: Stop loss triggered
2025-06-11 08:40:12 | INFO     | MassiveScan | info:70 | Trade closed: e1972db6-6cdf-4ea0-be9a-ffc7ce41cae2 - IREN - P&L: $-1.80
2025-06-11 08:40:12 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:40:15 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:18 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:18 | INFO     | MassiveScan | info:70 | Market order placed: BILI buy 9
2025-06-11 08:40:18 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:40:18 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | BILI | BUY 9 @ $20.9550 | PnL: +$1.94 | Reason: Profit target reached
2025-06-11 08:40:18 | INFO     | MassiveScan | info:70 | Trade closed: 58586d0e-5c34-44c4-a682-bceb0a15e2f0 - BILI - P&L: $1.94
2025-06-11 08:40:18 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:40:20 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:25 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:40:25 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:40:25 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:40:25 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:25 | INFO     | MassiveScan | info:70 | Stock screener returned 585 stocks
2025-06-11 08:40:25 | INFO     | MassiveScan | info:70 | Added 585 stocks from screener
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Scannable universe: 687 symbols
2025-06-11 08:40:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.79 | target_price: 46.78 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:40:27 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:40:30 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:35 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:39 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:40:39 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:40:40 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:41 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BAC | vwap_bounce | Confidence: 80.00% | entry_price: 45.1018 | target_price: 45.*************** | expected_profit: 1.0 | risk_reward: 41.**************
2025-06-11 08:40:41 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:40:41 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:40:43 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:40:44 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:40:46 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:51 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:55 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:40:56 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:01 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:01 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MRNA | vwap_bounce | Confidence: 80.00% | entry_price: 28.085 | target_price: 28.075 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:01 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:06 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:07 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SGOV | vwap_bounce | Confidence: 80.00% | entry_price: 100.47 | target_price: 100.46 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:41:07 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:11 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:12 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | WBD | rsi_overbought | Confidence: 56.47% | entry_price: 10.465 | target_price: 10.455 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:16 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:41:17 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:21 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:23 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RKT | rsi_overbought | Confidence: 54.77% | entry_price: 13.53 | target_price: 13.52 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:26 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:28 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:41:28 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:41:31 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:41:31 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AG | vwap_bounce | Confidence: 80.00% | entry_price: 8.4 | target_price: 8.39 | expected_profit: 1.0 | risk_reward: 5.***************
2025-06-11 08:41:35 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:36 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:41 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:46 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:41:47 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:51 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:53 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:41:56 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:59 | ERROR    | MassiveScan | error:82 | Error parsing market data for OAS: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:42:00 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:42:00 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:42:00 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Stock screener returned 607 stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Added 607 stocks from screener
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Scannable universe: 709 symbols
2025-06-11 08:42:02 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:02 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:42:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:42:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:42:07 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:09 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:42:12 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:17 | INFO     | MassiveScan | info:70 | Scan completed: 12 signals found in 111.5s
2025-06-11 08:42:17 | INFO     | MassiveScan | info:70 | Found 12 trading signals
2025-06-11 08:42:17 | INFO     | MassiveScan | info:70 | Processing signal: SWTX - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:17 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:17 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0045)
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0045)
2025-06-11 08:42:18 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Market order placed: SWTX buy 4
2025-06-11 08:42:18 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:18 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SWTX | BUY 4 @ $46.7900 | Strategy: vwap_bounce
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Trade executed: ********-d29e-43e0-afb8-fe4655b9aba6 - SWTX buy 4
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Trade executed: SWTX - Total trades today: 8
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Processing signal: PDCO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:19 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:42:19 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:42:19 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:42:19 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:42:19 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PDCO
2025-06-11 08:42:19 | INFO     | MassiveScan | info:70 | Processing signal: BAC - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:20 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for BAC (risk per share: $0.0002)
2025-06-11 08:42:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:42:20 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:42:20 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for BAC (risk per share: $0.0002)
2025-06-11 08:42:21 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Market order placed: BAC buy 4
2025-06-11 08:42:21 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:21 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | BAC | BUY 4 @ $45.1018 | Strategy: vwap_bounce
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Trade executed: e038bceb-7257-4c4d-8dc9-11f38d28ba6e - BAC buy 4
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Trade executed: BAC - Total trades today: 9
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Processing signal: MRNA - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for MRNA (risk per share: $0.0057)
2025-06-11 08:42:22 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for MRNA (risk per share: $0.0057)
2025-06-11 08:42:22 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Market order placed: MRNA buy 7
2025-06-11 08:42:22 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:22 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | MRNA | BUY 7 @ $28.0850 | Strategy: vwap_bounce
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Trade executed: 624f7de8-a1b0-43f4-82c8-f6a1bae9fd87 - MRNA buy 7
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Trade executed: MRNA - Total trades today: 10
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Processing signal: JWN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:23 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:42:24 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:42:24 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:42:24 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:42:24 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for JWN
2025-06-11 08:42:24 | INFO     | MassiveScan | info:70 | Processing signal: SGOV - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for SGOV (risk per share: $0.0044)
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for SGOV (risk per share: $0.0044)
2025-06-11 08:42:25 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Market order placed: SGOV buy 1
2025-06-11 08:42:25 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:25 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SGOV | BUY 1 @ $100.4700 | Strategy: vwap_bounce
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Trade executed: c30a0e92-da7e-404a-b9e4-73cdd08e598e - SGOV buy 1
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Trade executed: SGOV - Total trades today: 11
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Processing signal: PTVE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:26 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:42:26 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:42:27 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:42:27 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:27 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:42:27 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:42:27 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PTVE
2025-06-11 08:42:27 | INFO     | MassiveScan | info:70 | Processing signal: AG - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RKT | rsi_overbought | Confidence: 60.16% | entry_price: 13.56 | target_price: 13.55 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Position size calculated: 23 shares for AG (risk per share: $0.0017)
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Position size calculated: 23 shares for AG (risk per share: $0.0017)
2025-06-11 08:42:28 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Market order placed: AG buy 23
2025-06-11 08:42:28 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:28 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AG | BUY 23 @ $8.4000 | Strategy: vwap_bounce
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Trade executed: 052ff125-2ed7-477d-b91e-41e4ec40e545 - AG buy 23
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Trade executed: AG - Total trades today: 12
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Processing signal: ACCD - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:29 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:42:30 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:42:30 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 08:42:30 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 08:42:30 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for ACCD
2025-06-11 08:42:30 | INFO     | MassiveScan | info:70 | Processing signal: LGTY - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:30 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:42:31 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:42:31 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:42:31 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 08:42:31 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 08:42:31 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for LGTY
2025-06-11 08:42:31 | INFO     | MassiveScan | info:70 | Processing signal: WBD - rsi_overbought (confidence: 56.47%)
2025-06-11 08:42:32 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:32 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for WBD (risk per share: $0.0050)
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for WBD (risk per share: $0.0050)
2025-06-11 08:42:33 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Market order placed: WBD sell 19
2025-06-11 08:42:33 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | WBD | SELL 19 @ $10.4650 | Strategy: rsi_overbought
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Trade executed: 825074ef-cebf-4c57-81a1-243fdec69b6b - WBD sell 19
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Trade executed: WBD - Total trades today: 13
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Processing signal: RKT - rsi_overbought (confidence: 54.77%)
2025-06-11 08:42:34 | INFO     | MassiveScan | info:70 | Position size calculated: 14 shares for RKT (risk per share: $0.0050)
2025-06-11 08:42:34 | INFO     | MassiveScan | info:70 | Position size calculated: 14 shares for RKT (risk per share: $0.0050)
2025-06-11 08:42:34 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":42210000,"message":"asset \"RKT\" cannot be sold short"}
2025-06-11 08:42:34 | ERROR    | MassiveScan | error:82 | Failed to place entry order for RKT
2025-06-11 08:42:34 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for RKT
2025-06-11 08:42:35 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:35 | INFO     | MassiveScan | info:70 | Market order placed: HBAN sell 12
2025-06-11 08:42:35 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:35 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:42:35 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | HBAN | SELL 12 @ $16.3850 | PnL: +$1.20 | Reason: Profit target reached
2025-06-11 08:42:35 | INFO     | MassiveScan | info:70 | Trade closed: 4c7d2f26-9579-4ec7-aff5-dc7d381b67eb - HBAN - P&L: $1.20
2025-06-11 08:42:35 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:42:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | volume_surge | Confidence: 73.65% | entry_price: 18.59 | target_price: 18.6 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:42:37 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:42:37 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:42 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:43 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:43 | INFO     | MassiveScan | info:70 | Market order placed: WBD buy 19
2025-06-11 08:42:43 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:42:43 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | WBD | BUY 19 @ $10.5300 | PnL: -$1.23 | Reason: Stop loss triggered
2025-06-11 08:42:43 | INFO     | MassiveScan | info:70 | Trade closed: 825074ef-cebf-4c57-81a1-243fdec69b6b - WBD - P&L: $-1.23
2025-06-11 08:42:43 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:42:45 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:42:46 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:42:46 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Stock screener returned 610 stocks
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Added 610 stocks from screener
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:42:47 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:42:48 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:42:48 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:42:48 | INFO     | MassiveScan | info:70 | Scannable universe: 711 symbols
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/BPMC?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HRT?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for HRT
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HSC?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GME?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XLV?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WETF?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SMCI?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OSG?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CEQP?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MWYN?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MSTY?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for HSC
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for GME
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for XLV
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for WETF
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for SMCI
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for OSG
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for CEQP
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for MWYN
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for MSTY
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/AS?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KHC?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SWTX?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for KHC
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for SWTX
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BAFN?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/AIMC?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for BAFN
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AMAM?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for AMAM
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MIT?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for MIT
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NVDL?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AYA.TO?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for NVDL
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for AYA.TO
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FUSN?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for FUSN
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JDZG?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for JDZG
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JILL?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for JILL
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SWM?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for SWM
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SJR?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for SJR
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BIGZ?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for BIGZ
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/WRK?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CX?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FUSE?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IREN?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for CX
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for FUSE
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for IREN
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RXDX?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for RXDX
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GTI?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for GTI
2025-06-11 08:42:49 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:42:49 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:42:49 | INFO     | MassiveScan | info:70 | Scan completed: 4 signals found in 49.1s
2025-06-11 08:42:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:42:50 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:42:50 | INFO     | MassiveScan | info:70 | Scan completed: 0 signals found in 2.8s
2025-06-11 08:42:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CLF,AG,AAL,SWTX,VALE,BAC,SGOV,XPEV,MRNA?apikey=********************************
2025-06-11 08:42:52 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:55 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CLF,AG,AAL,SWTX,VALE,BAC,SGOV,XPEV,MRNA?apikey=********************************
2025-06-11 08:42:57 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CLF,AG,AAL,SWTX,VALE,BAC,SGOV,XPEV,MRNA?apikey=********************************
2025-06-11 08:43:02 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:43:07 | INFO     | MassiveScan | info:70 | Market order placed: MRNA sell 7
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:43:07 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:43:07 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | MRNA | SELL 7 @ $27.9975 | PnL: -$0.61 | Reason: Stop loss triggered
2025-06-11 08:43:07 | INFO     | MassiveScan | info:70 | Trade closed: 624f7de8-a1b0-43f4-82c8-f6a1bae9fd87 - MRNA - P&L: $-0.61
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:12 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:21 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:43:21 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:43:21 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:43:22 | INFO     | MassiveScan | info:70 | Stock screener returned 613 stocks
2025-06-11 08:43:22 | INFO     | MassiveScan | info:70 | Added 613 stocks from screener
2025-06-11 08:43:22 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:43:22 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:43:23 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:23 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:43:23 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:43:23 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:43:23 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:43:23 | INFO     | MassiveScan | info:70 | Scannable universe: 715 symbols
2025-06-11 08:43:24 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.78 | target_price: 46.79 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:43:24 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:43:28 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:30 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KGC | vwap_bounce | Confidence: 80.00% | entry_price: 14.99 | target_price: 14.98 | expected_profit: 1.0 | risk_reward: 7.***************
2025-06-11 08:43:30 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:43:33 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:43:33 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:43:33 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:38 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:38 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:43:38 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:43:39 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:43:43 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:44 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AVDX | vwap_bounce | Confidence: 80.00% | entry_price: 9.785 | target_price: 9.795 | expected_profit: 1.0 | risk_reward: 5.***************
2025-06-11 08:43:44 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:43:48 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:51 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:43:53 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:54 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMCR | vwap_bounce | Confidence: 80.00% | entry_price: 9.22 | target_price: 9.23 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:43:54 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Bot started from GUI
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Market is open
2025-06-11 08:43:57 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:57 | ERROR    | MassiveScan | error:82 | Failed to connect to broker
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Cleaning up resources...
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Final Performance Summary:
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 |   Total trades: 2
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 |   Win rate: 50.0%
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 |   Total P&L: $0.70
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 |   Average P&L: $0.35
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | MassiveScan Trading Bot stopped
2025-06-11 08:43:58 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:01 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:44:01 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:44:03 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:07 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | WBD | rsi_overbought | Confidence: 61.40% | entry_price: 10.505 | target_price: 10.*************** | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:44:08 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:13 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:44:13 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:44:13 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:23 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:25 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:44:25 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:44:25 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:44:28 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:33 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MSTU | vwap_bounce | Confidence: 80.00% | entry_price: 8.275 | target_price: 8.285 | expected_profit: 1.0 | risk_reward: 4.***************
2025-06-11 08:44:37 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:44:39 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:42 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:44:42 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:44:44 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:48 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:44:49 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:54 | ERROR    | MassiveScan | error:82 | Error parsing market data for OAS: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:44:57 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:44:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:45:01 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:45:01 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:45:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | NCLH | vwap_bounce | Confidence: 80.00% | entry_price: 19.7099 | target_price: 19.*************** | expected_profit: 1.0 | risk_reward: 6.****************
2025-06-11 08:45:03 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:45:05 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:45:12 | INFO     | MassiveScan | info:70 | Scan completed: 12 signals found in 110.8s
2025-06-11 08:45:12 | INFO     | MassiveScan | info:70 | Found 12 trading signals
2025-06-11 08:45:12 | INFO     | MassiveScan | info:70 | Processing signal: SWTX - vwap_bounce (confidence: 80.00%)
2025-06-11 08:45:12 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0046)
2025-06-11 08:45:13 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0046)
2025-06-11 08:45:13 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:13 | INFO     | MassiveScan | info:70 | Market order placed: SWTX buy 4
2025-06-11 08:45:13 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:13 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SWTX | BUY 4 @ $46.7800 | Strategy: vwap_bounce
2025-06-11 08:45:13 | INFO     | MassiveScan | info:70 | Trade executed: 6de2eb8b-b8ce-464b-9816-9daae14c9439 - SWTX buy 4
2025-06-11 08:45:13 | INFO     | MassiveScan | info:70 | Trade executed: SWTX - Total trades today: 14
2025-06-11 08:45:13 | INFO     | MassiveScan | info:70 | Processing signal: KGC - vwap_bounce (confidence: 80.00%)
2025-06-11 08:45:14 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for KGC (risk per share: $0.0014)
2025-06-11 08:45:15 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for KGC (risk per share: $0.0014)
2025-06-11 08:45:15 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:15 | INFO     | MassiveScan | info:70 | Market order placed: KGC buy 13
2025-06-11 08:45:15 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:15 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | KGC | BUY 13 @ $14.9900 | Strategy: vwap_bounce
2025-06-11 08:45:15 | INFO     | MassiveScan | info:70 | Trade executed: b29305f4-83b4-46cb-bbaf-23af49b347f3 - KGC buy 13
2025-06-11 08:45:15 | INFO     | MassiveScan | info:70 | Trade executed: KGC - Total trades today: 15
2025-06-11 08:45:15 | INFO     | MassiveScan | info:70 | Processing signal: PDCO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:45:15 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:45:16 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:45:16 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:45:16 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:45:16 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PDCO
2025-06-11 08:45:16 | INFO     | MassiveScan | info:70 | Processing signal: AVDX - vwap_bounce (confidence: 80.00%)
2025-06-11 08:45:17 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for AVDX (risk per share: $0.0019)
2025-06-11 08:45:18 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for AVDX (risk per share: $0.0019)
2025-06-11 08:45:18 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:18 | INFO     | MassiveScan | info:70 | Market order placed: AVDX buy 20
2025-06-11 08:45:18 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:18 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AVDX | BUY 20 @ $9.7850 | Strategy: vwap_bounce
2025-06-11 08:45:18 | INFO     | MassiveScan | info:70 | Trade executed: 6556f842-a814-45a0-9db3-fab9218890f7 - AVDX buy 20
2025-06-11 08:45:18 | INFO     | MassiveScan | info:70 | Trade executed: AVDX - Total trades today: 16
2025-06-11 08:45:18 | INFO     | MassiveScan | info:70 | Processing signal: AMCR - vwap_bounce (confidence: 80.00%)
2025-06-11 08:45:18 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0049)
2025-06-11 08:45:19 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0049)
2025-06-11 08:45:19 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:19 | INFO     | MassiveScan | info:70 | Market order placed: AMCR buy 21
2025-06-11 08:45:19 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:19 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AMCR | BUY 21 @ $9.2200 | Strategy: vwap_bounce
2025-06-11 08:45:19 | INFO     | MassiveScan | info:70 | Trade executed: 64d7f1b3-0667-4960-bcd5-5f68bed4c275 - AMCR buy 21
2025-06-11 08:45:19 | INFO     | MassiveScan | info:70 | Trade executed: AMCR - Total trades today: 17
2025-06-11 08:45:19 | INFO     | MassiveScan | info:70 | Processing signal: JWN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:45:20 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:45:21 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:45:21 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:45:21 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:45:21 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for JWN
2025-06-11 08:45:21 | INFO     | MassiveScan | info:70 | Processing signal: PTVE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:45:21 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:45:22 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:45:22 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:45:22 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:45:22 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PTVE
2025-06-11 08:45:22 | INFO     | MassiveScan | info:70 | Processing signal: MSTU - vwap_bounce (confidence: 80.00%)
2025-06-11 08:45:23 | INFO     | MassiveScan | info:70 | Position size calculated: 24 shares for MSTU (risk per share: $0.0023)
2025-06-11 08:45:24 | INFO     | MassiveScan | info:70 | Position size calculated: 24 shares for MSTU (risk per share: $0.0023)
2025-06-11 08:45:24 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:24 | INFO     | MassiveScan | info:70 | Market order placed: MSTU buy 24
2025-06-11 08:45:24 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:24 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | MSTU | BUY 24 @ $8.2750 | Strategy: vwap_bounce
2025-06-11 08:45:24 | INFO     | MassiveScan | info:70 | Trade executed: ce6ae5ac-282d-42f0-ac77-7d818dd3491f - MSTU buy 24
2025-06-11 08:45:24 | INFO     | MassiveScan | info:70 | Trade executed: MSTU - Total trades today: 18
2025-06-11 08:45:24 | INFO     | MassiveScan | info:70 | Processing signal: ACCD - vwap_bounce (confidence: 80.00%)
2025-06-11 08:45:24 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:45:25 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:45:25 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 08:45:25 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 08:45:25 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for ACCD
2025-06-11 08:45:25 | INFO     | MassiveScan | info:70 | Processing signal: LGTY - vwap_bounce (confidence: 80.00%)
2025-06-11 08:45:26 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:45:27 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:45:27 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 08:45:27 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 08:45:27 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for LGTY
2025-06-11 08:45:27 | INFO     | MassiveScan | info:70 | Processing signal: NCLH - vwap_bounce (confidence: 80.00%)
2025-06-11 08:45:27 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for NCLH (risk per share: $0.0016)
2025-06-11 08:45:28 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for NCLH (risk per share: $0.0016)
2025-06-11 08:45:28 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:28 | INFO     | MassiveScan | info:70 | Market order placed: NCLH buy 10
2025-06-11 08:45:28 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:28 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | NCLH | BUY 10 @ $19.7099 | Strategy: vwap_bounce
2025-06-11 08:45:28 | INFO     | MassiveScan | info:70 | Trade executed: 497f571b-92d2-4542-b260-f283a23a08a4 - NCLH buy 10
2025-06-11 08:45:28 | INFO     | MassiveScan | info:70 | Trade executed: NCLH - Total trades today: 19
2025-06-11 08:45:28 | INFO     | MassiveScan | info:70 | Processing signal: WBD - rsi_overbought (confidence: 61.40%)
2025-06-11 08:45:29 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for WBD (risk per share: $0.0050)
2025-06-11 08:45:30 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for WBD (risk per share: $0.0050)
2025-06-11 08:45:30 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:30 | INFO     | MassiveScan | info:70 | Market order placed: WBD sell 19
2025-06-11 08:45:30 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | WBD | SELL 19 @ $10.5050 | Strategy: rsi_overbought
2025-06-11 08:45:30 | INFO     | MassiveScan | info:70 | Trade executed: 6c2e08d0-6750-4b6d-82b9-7fb6577413f9 - WBD sell 19
2025-06-11 08:45:30 | INFO     | MassiveScan | info:70 | Trade executed: WBD - Total trades today: 20
2025-06-11 08:45:31 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 | Market order placed: CLF sell 27
2025-06-11 08:45:31 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 3 consecutive losses
2025-06-11 08:45:31 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:45:31 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CLF | SELL 27 @ $7.2500 | PnL: -$0.54 | Reason: Stop loss triggered
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 | Trade closed: 890f00ff-db12-4042-b11e-1f5a7fe66f3b - CLF - P&L: $-0.54
2025-06-11 08:45:31 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:45:31 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 20 | PnL: $-1.06 | Win Rate: 33.3% | Avg Profit: $-0.05
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 | Status Report:
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 |   Runtime: 0.1 hours
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 |   Signals processed: 35
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 |   Trades executed: 20
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 |   Active trades: 14
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 |   Daily P&L: $-1.06
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 |   Win rate: 33.3%
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 |   Scans completed: 4
2025-06-11 08:45:31 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-0.05
2025-06-11 08:45:31 | WARNING  | MassiveScan | warning:78 | Low win rate: 33.3%
2025-06-11 08:45:37 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:37 | INFO     | MassiveScan | info:70 | Market order placed: XPEV buy 9
2025-06-11 08:45:37 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:45:37 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | XPEV | BUY 9 @ $20.7750 | PnL: +$1.62 | Reason: Profit target reached
2025-06-11 08:45:37 | INFO     | MassiveScan | info:70 | Trade closed: ae7c92da-ae73-4825-a513-101c800cebdc - XPEV - P&L: $1.62
2025-06-11 08:45:37 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:45:43 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:43 | INFO     | MassiveScan | info:70 | Market order placed: AG sell 23
2025-06-11 08:45:43 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:43 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:45:43 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AG | SELL 23 @ $8.3500 | PnL: -$1.15 | Reason: Stop loss triggered
2025-06-11 08:45:43 | INFO     | MassiveScan | info:70 | Trade closed: 052ff125-2ed7-477d-b91e-41e4ec40e545 - AG - P&L: $-1.15
2025-06-11 08:45:43 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:45:43 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:45:43 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:45:43 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:45:44 | INFO     | MassiveScan | info:70 | Stock screener returned 638 stocks
2025-06-11 08:45:44 | INFO     | MassiveScan | info:70 | Added 638 stocks from screener
2025-06-11 08:45:44 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:45:44 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:45:44 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:45:44 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:45:44 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:45:44 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:45:44 | INFO     | MassiveScan | info:70 | Scannable universe: 740 symbols
2025-06-11 08:45:45 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.78 | target_price: 46.77 | expected_profit: 1.0 | risk_reward: 1.9152790844122942
2025-06-11 08:45:45 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:45:52 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KGC | vwap_bounce | Confidence: 80.00% | entry_price: 14.93 | target_price: 14.92 | expected_profit: 1.0 | risk_reward: 2.047027352892517
2025-06-11 08:45:52 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:45:54 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:45:54 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:45:57 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AAL | vwap_bounce | Confidence: 80.00% | entry_price: 11.83 | target_price: 11.82 | expected_profit: 1.0 | risk_reward: 3.179894284366229
2025-06-11 08:45:57 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:46:00 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:46:00 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:46:00 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CMG | rsi_overbought | Confidence: 50.44% | entry_price: 51.51 | target_price: 51.5 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:46:03 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:46:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AFRM | rsi_overbought | Confidence: 51.22% | entry_price: 63.33 | target_price: 63.32 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:46:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VALE | vwap_bounce | Confidence: 80.00% | entry_price: 9.575 | target_price: 9.565 | expected_profit: 1.0 | risk_reward: 2.528326772050059
2025-06-11 08:46:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:46:14 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:46:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMCR | vwap_bounce | Confidence: 80.00% | entry_price: 9.235 | target_price: 9.225 | expected_profit: 1.0 | risk_reward: 106.95252729566566
2025-06-11 08:46:16 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:46:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | INTR | vwap_bounce | Confidence: 80.00% | entry_price: 6.8201 | target_price: 6.8101 | expected_profit: 1.0 | risk_reward: 2.361959068484969
2025-06-11 08:46:20 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:46:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACHC | vwap_bounce | Confidence: 80.00% | entry_price: 22.37 | target_price: 22.380000000000003 | expected_profit: 1.0 | risk_reward: 1.5041484552749396
2025-06-11 08:46:22 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:46:23 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:46:23 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:46:34 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EMLC | vwap_bounce | Confidence: 80.00% | entry_price: 24.785 | target_price: 24.775 | expected_profit: 1.0 | risk_reward: 4.142198211794595
2025-06-11 08:46:34 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KEY.TO | vwap_bounce | Confidence: 80.00% | entry_price: 42.0 | target_price: 42.01 | expected_profit: 1.0 | risk_reward: 1.50508557802368
2025-06-11 08:46:34 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:46:34 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:46:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:46:35 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:46:47 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:46:47 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:46:49 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:46:53 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GTLB | volume_surge | Confidence: 64.53% | entry_price: 43.43 | target_price: 43.42 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:46:53 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:46:59 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CORZ | vwap_bounce | Confidence: 80.00% | entry_price: 12.5864 | target_price: 12.5764 | expected_profit: 1.0 | risk_reward: 1.5502760979693015
2025-06-11 08:46:59 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:47:07 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:47:07 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:47:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RCAT | vwap_bounce | Confidence: 80.00% | entry_price: 8.7046 | target_price: 8.6946 | expected_profit: 1.0 | risk_reward: 1.7691840895337714
2025-06-11 08:47:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:47:13 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:47:20 | ERROR    | MassiveScan | error:82 | Error parsing market data for OAS: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:47:22 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:47:26 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:47:26 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:47:30 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:47:36 | INFO     | MassiveScan | info:70 | Scan completed: 19 signals found in 113.0s
2025-06-11 08:47:36 | INFO     | MassiveScan | info:70 | Found 19 trading signals
2025-06-11 08:47:36 | INFO     | MassiveScan | info:70 | Processing signal: SWTX - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:38 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0052)
2025-06-11 08:47:39 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0052)
2025-06-11 08:47:39 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:39 | INFO     | MassiveScan | info:70 | Market order placed: SWTX buy 4
2025-06-11 08:47:39 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:39 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SWTX | BUY 4 @ $46.7800 | Strategy: vwap_bounce
2025-06-11 08:47:39 | INFO     | MassiveScan | info:70 | Trade executed: 7bd7307b-02c5-4977-b29e-28911231d629 - SWTX buy 4
2025-06-11 08:47:39 | INFO     | MassiveScan | info:70 | Trade executed: SWTX - Total trades today: 21
2025-06-11 08:47:39 | INFO     | MassiveScan | info:70 | Processing signal: KGC - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:40 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for KGC (risk per share: $0.0049)
2025-06-11 08:47:40 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for KGC (risk per share: $0.0049)
2025-06-11 08:47:40 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:40 | INFO     | MassiveScan | info:70 | Market order placed: KGC buy 13
2025-06-11 08:47:40 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:40 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | KGC | BUY 13 @ $14.9300 | Strategy: vwap_bounce
2025-06-11 08:47:40 | INFO     | MassiveScan | info:70 | Trade executed: 943bdd48-52aa-40fb-aac4-6bed2cc1f3a3 - KGC buy 13
2025-06-11 08:47:40 | INFO     | MassiveScan | info:70 | Trade executed: KGC - Total trades today: 22
2025-06-11 08:47:40 | INFO     | MassiveScan | info:70 | Processing signal: PDCO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:41 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:47:42 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:47:42 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:47:42 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:47:42 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PDCO
2025-06-11 08:47:42 | INFO     | MassiveScan | info:70 | Processing signal: AAL - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:43 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for AAL (risk per share: $0.0031)
2025-06-11 08:47:43 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for AAL (risk per share: $0.0031)
2025-06-11 08:47:43 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:43 | INFO     | MassiveScan | info:70 | Market order placed: AAL buy 16
2025-06-11 08:47:43 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:43 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AAL | BUY 16 @ $11.8300 | Strategy: vwap_bounce
2025-06-11 08:47:43 | INFO     | MassiveScan | info:70 | Trade executed: 5fc5e926-67cc-4fa0-bbb4-c2316e44ab84 - AAL buy 16
2025-06-11 08:47:43 | INFO     | MassiveScan | info:70 | Trade executed: AAL - Total trades today: 23
2025-06-11 08:47:43 | INFO     | MassiveScan | info:70 | Processing signal: VALE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:44 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for VALE (risk per share: $0.0040)
2025-06-11 08:47:45 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for VALE (risk per share: $0.0040)
2025-06-11 08:47:45 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:45 | INFO     | MassiveScan | info:70 | Market order placed: VALE buy 20
2025-06-11 08:47:45 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:45 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | VALE | BUY 20 @ $9.5750 | Strategy: vwap_bounce
2025-06-11 08:47:45 | INFO     | MassiveScan | info:70 | Trade executed: 44e58658-5381-43ce-9526-711c964f50cb - VALE buy 20
2025-06-11 08:47:45 | INFO     | MassiveScan | info:70 | Trade executed: VALE - Total trades today: 24
2025-06-11 08:47:45 | INFO     | MassiveScan | info:70 | Processing signal: AMCR - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:46 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0001)
2025-06-11 08:47:46 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0001)
2025-06-11 08:47:47 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:47 | INFO     | MassiveScan | info:70 | Market order placed: AMCR buy 21
2025-06-11 08:47:47 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:47 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AMCR | BUY 21 @ $9.2350 | Strategy: vwap_bounce
2025-06-11 08:47:47 | INFO     | MassiveScan | info:70 | Trade executed: de5ed427-1bf0-4430-8ff6-03afd35266d9 - AMCR buy 21
2025-06-11 08:47:47 | INFO     | MassiveScan | info:70 | Trade executed: AMCR - Total trades today: 25
2025-06-11 08:47:47 | INFO     | MassiveScan | info:70 | Processing signal: INTR - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:47 | INFO     | MassiveScan | info:70 | Position size calculated: 29 shares for INTR (risk per share: $0.0042)
2025-06-11 08:47:48 | INFO     | MassiveScan | info:70 | Position size calculated: 29 shares for INTR (risk per share: $0.0042)
2025-06-11 08:47:48 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:48 | INFO     | MassiveScan | info:70 | Market order placed: INTR buy 29
2025-06-11 08:47:48 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:48 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | INTR | BUY 29 @ $6.8201 | Strategy: vwap_bounce
2025-06-11 08:47:48 | INFO     | MassiveScan | info:70 | Trade executed: 4bfa4117-b22b-4488-8258-88bbb82dd11b - INTR buy 29
2025-06-11 08:47:48 | INFO     | MassiveScan | info:70 | Trade executed: INTR - Total trades today: 26
2025-06-11 08:47:48 | INFO     | MassiveScan | info:70 | Processing signal: ACHC - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:49 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for ACHC (risk per share: $0.0066)
2025-06-11 08:47:49 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for ACHC (risk per share: $0.0066)
2025-06-11 08:47:49 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:50 | INFO     | MassiveScan | info:70 | Market order placed: ACHC buy 8
2025-06-11 08:47:50 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:50 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | ACHC | BUY 8 @ $22.3700 | Strategy: vwap_bounce
2025-06-11 08:47:50 | INFO     | MassiveScan | info:70 | Trade executed: ffd86684-acc6-4e7a-b815-53300f28a14c - ACHC buy 8
2025-06-11 08:47:50 | INFO     | MassiveScan | info:70 | Trade executed: ACHC - Total trades today: 27
2025-06-11 08:47:50 | INFO     | MassiveScan | info:70 | Processing signal: JWN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:50 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:47:51 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:47:51 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:47:51 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:47:51 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for JWN
2025-06-11 08:47:51 | INFO     | MassiveScan | info:70 | Processing signal: EMLC - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:52 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for EMLC (risk per share: $0.0024)
2025-06-11 08:47:52 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for EMLC (risk per share: $0.0024)
2025-06-11 08:47:52 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:53 | INFO     | MassiveScan | info:70 | Market order placed: EMLC buy 8
2025-06-11 08:47:53 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:53 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | EMLC | BUY 8 @ $24.7850 | Strategy: vwap_bounce
2025-06-11 08:47:53 | INFO     | MassiveScan | info:70 | Trade executed: 2b9aaba9-516f-4149-82b7-ccd0209f2a72 - EMLC buy 8
2025-06-11 08:47:53 | INFO     | MassiveScan | info:70 | Trade executed: EMLC - Total trades today: 28
2025-06-11 08:47:53 | INFO     | MassiveScan | info:70 | Processing signal: KEY.TO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:53 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for KEY.TO (risk per share: $0.0066)
2025-06-11 08:47:54 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for KEY.TO (risk per share: $0.0066)
2025-06-11 08:47:54 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":42210000,"message":"asset \"KEY.TO\" not found"}
2025-06-11 08:47:54 | ERROR    | MassiveScan | error:82 | Failed to place entry order for KEY.TO
2025-06-11 08:47:54 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for KEY.TO
2025-06-11 08:47:54 | INFO     | MassiveScan | info:70 | Processing signal: PTVE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:55 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:47:55 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:47:56 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:47:56 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:47:56 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PTVE
2025-06-11 08:47:56 | INFO     | MassiveScan | info:70 | Processing signal: CORZ - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:56 | INFO     | MassiveScan | info:70 | Position size calculated: 15 shares for CORZ (risk per share: $0.0065)
2025-06-11 08:47:57 | INFO     | MassiveScan | info:70 | Position size calculated: 15 shares for CORZ (risk per share: $0.0065)
2025-06-11 08:47:57 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:57 | INFO     | MassiveScan | info:70 | Market order placed: CORZ buy 15
2025-06-11 08:47:57 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:57 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CORZ | BUY 15 @ $12.5864 | Strategy: vwap_bounce
2025-06-11 08:47:57 | INFO     | MassiveScan | info:70 | Trade executed: 5824761c-35b1-4d82-bb10-c021d73bb84a - CORZ buy 15
2025-06-11 08:47:57 | INFO     | MassiveScan | info:70 | Trade executed: CORZ - Total trades today: 29
2025-06-11 08:47:57 | INFO     | MassiveScan | info:70 | Processing signal: ACCD - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:58 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:47:58 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:47:59 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 08:47:59 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 08:47:59 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for ACCD
2025-06-11 08:47:59 | INFO     | MassiveScan | info:70 | Processing signal: RCAT - vwap_bounce (confidence: 80.00%)
2025-06-11 08:47:59 | INFO     | MassiveScan | info:70 | Position size calculated: 22 shares for RCAT (risk per share: $0.0057)
2025-06-11 08:48:00 | INFO     | MassiveScan | info:70 | Position size calculated: 22 shares for RCAT (risk per share: $0.0057)
2025-06-11 08:48:00 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:48:00 | INFO     | MassiveScan | info:70 | Market order placed: RCAT buy 22
2025-06-11 08:48:00 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:48:00 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | RCAT | BUY 22 @ $8.7046 | Strategy: vwap_bounce
2025-06-11 08:48:00 | INFO     | MassiveScan | info:70 | Trade executed: f2d18c39-0507-4dfe-8210-ca7bf809d3ff - RCAT buy 22
2025-06-11 08:48:00 | INFO     | MassiveScan | info:70 | Trade executed: RCAT - Total trades today: 30
2025-06-11 08:48:00 | INFO     | MassiveScan | info:70 | Processing signal: LGTY - vwap_bounce (confidence: 80.00%)
2025-06-11 08:48:01 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:48:01 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:48:02 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 08:48:02 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 08:48:02 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for LGTY
2025-06-11 08:48:02 | INFO     | MassiveScan | info:70 | Processing signal: GTLB - volume_surge (confidence: 64.53%)
2025-06-11 08:48:02 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:48:03 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:48:03 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:48:03 | INFO     | MassiveScan | info:70 | Market order placed: GTLB buy 4
2025-06-11 08:48:03 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:48:03 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | GTLB | BUY 4 @ $43.4300 | Strategy: volume_surge
2025-06-11 08:48:03 | INFO     | MassiveScan | info:70 | Trade executed: 02475a17-3b4b-434a-83f4-7ad484314e28 - GTLB buy 4
2025-06-11 08:48:03 | INFO     | MassiveScan | info:70 | Trade executed: GTLB - Total trades today: 31
2025-06-11 08:48:03 | INFO     | MassiveScan | info:70 | Processing signal: AFRM - rsi_overbought (confidence: 51.22%)
2025-06-11 08:48:04 | INFO     | MassiveScan | info:70 | Position size calculated: 3 shares for AFRM (risk per share: $0.0050)
2025-06-11 08:48:04 | INFO     | MassiveScan | info:70 | Position size calculated: 3 shares for AFRM (risk per share: $0.0050)
2025-06-11 08:48:05 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:48:05 | INFO     | MassiveScan | info:70 | Market order placed: AFRM sell 3
2025-06-11 08:48:05 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AFRM | SELL 3 @ $63.3300 | Strategy: rsi_overbought
2025-06-11 08:48:05 | INFO     | MassiveScan | info:70 | Trade executed: b77cf085-985e-45e9-9f40-3274dede9ca5 - AFRM sell 3
2025-06-11 08:48:05 | INFO     | MassiveScan | info:70 | Trade executed: AFRM - Total trades today: 32
2025-06-11 08:48:05 | INFO     | MassiveScan | info:70 | Processing signal: CMG - rsi_overbought (confidence: 50.44%)
2025-06-11 08:48:05 | INFO     | MassiveScan | info:70 | Position size calculated: 3 shares for CMG (risk per share: $0.0050)
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Position size calculated: 3 shares for CMG (risk per share: $0.0050)
2025-06-11 08:48:06 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Market order placed: CMG sell 3
2025-06-11 08:48:06 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CMG | SELL 3 @ $51.5100 | Strategy: rsi_overbought
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Trade executed: a11bcda1-13ad-44f0-9c62-6e062e1650d7 - CMG sell 3
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Trade executed: CMG - Total trades today: 33
2025-06-11 08:48:06 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Market order placed: AAL sell 16
2025-06-11 08:48:06 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:48:06 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:48:06 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AAL | SELL 16 @ $11.7900 | PnL: -$0.62 | Reason: Stop loss triggered
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Trade closed: 1c724dad-5db4-4430-af23-d8b6a36d91dc - AAL - P&L: $-0.62
2025-06-11 08:48:06 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:48:07 | INFO     | MassiveScan | info:70 | Stock screener returned 653 stocks
2025-06-11 08:48:07 | INFO     | MassiveScan | info:70 | Added 653 stocks from screener
2025-06-11 08:48:07 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:48:07 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:48:07 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:48:07 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:48:07 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:48:07 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:48:07 | INFO     | MassiveScan | info:70 | Scannable universe: 754 symbols
2025-06-11 08:48:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:48:17 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:48:22 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:48:22 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:48:25 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:48:37 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:48:46 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:48:46 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:48:46 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CVE.TO | vwap_bounce | Confidence: 80.00% | entry_price: 19.06 | target_price: 19.049999999999997 | expected_profit: 1.0 | risk_reward: 6.040511336954411
2025-06-11 08:48:46 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:48:55 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EMLC | vwap_bounce | Confidence: 80.00% | entry_price: 24.785 | target_price: 24.775 | expected_profit: 1.0 | risk_reward: 4.193201950158175
2025-06-11 08:48:55 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:48:57 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:48:57 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:48:58 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | B | vwap_bounce | Confidence: 80.00% | entry_price: 20.145 | target_price: 20.155 | expected_profit: 1.0 | risk_reward: 1.6908064614923086
2025-06-11 08:48:58 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:48:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GELS?apikey=********************************
2025-06-11 08:48:58 | WARNING  | MassiveScan | warning:78 | No quote data for GELS
2025-06-11 08:48:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/U?apikey=********************************
2025-06-11 08:48:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/VMW?apikey=********************************
2025-06-11 08:48:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/SPLK?apikey=********************************
2025-06-11 08:49:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:49:00 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:49:00 | INFO     | MassiveScan | info:70 | Scan completed: 6 signals found in 53.4s
2025-06-11 08:49:00 | INFO     | MassiveScan | info:70 | Found 6 trading signals
2025-06-11 08:49:00 | INFO     | MassiveScan | info:70 | Processing signal: PDCO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:49:00 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:49:01 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:49:01 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:49:01 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:49:01 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PDCO
2025-06-11 08:49:01 | INFO     | MassiveScan | info:70 | Processing signal: JWN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:49:02 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:49:02 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:49:03 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:49:03 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:49:03 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for JWN
2025-06-11 08:49:03 | INFO     | MassiveScan | info:70 | Processing signal: CVE.TO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:49:03 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for CVE.TO (risk per share: $0.0017)
2025-06-11 08:49:04 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for CVE.TO (risk per share: $0.0017)
2025-06-11 08:49:04 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":42210000,"message":"asset \"CVE.TO\" not found"}
2025-06-11 08:49:04 | ERROR    | MassiveScan | error:82 | Failed to place entry order for CVE.TO
2025-06-11 08:49:04 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for CVE.TO
2025-06-11 08:49:04 | INFO     | MassiveScan | info:70 | Processing signal: EMLC - vwap_bounce (confidence: 80.00%)
2025-06-11 08:49:05 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for EMLC (risk per share: $0.0024)
2025-06-11 08:49:05 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for EMLC (risk per share: $0.0024)
2025-06-11 08:49:06 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:49:06 | INFO     | MassiveScan | info:70 | Market order placed: EMLC buy 8
2025-06-11 08:49:06 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:49:06 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | EMLC | BUY 8 @ $24.7850 | Strategy: vwap_bounce
2025-06-11 08:49:06 | INFO     | MassiveScan | info:70 | Trade executed: 5af8bb3d-66b7-431c-904f-79972725e871 - EMLC buy 8
2025-06-11 08:49:06 | INFO     | MassiveScan | info:70 | Trade executed: EMLC - Total trades today: 34
2025-06-11 08:49:06 | INFO     | MassiveScan | info:70 | Processing signal: PTVE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:49:06 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:49:07 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:49:07 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:49:07 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:49:07 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PTVE
2025-06-11 08:49:07 | INFO     | MassiveScan | info:70 | Processing signal: B - vwap_bounce (confidence: 80.00%)
2025-06-11 08:49:08 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for B (risk per share: $0.0059)
2025-06-11 08:49:08 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for B (risk per share: $0.0059)
2025-06-11 08:49:09 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:49:09 | INFO     | MassiveScan | info:70 | Market order placed: B buy 9
2025-06-11 08:49:09 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:49:09 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | B | BUY 9 @ $20.1450 | Strategy: vwap_bounce
2025-06-11 08:49:09 | INFO     | MassiveScan | info:70 | Trade executed: 6d0c57e6-bc4f-494e-831b-6d946a84123b - B buy 9
2025-06-11 08:49:09 | INFO     | MassiveScan | info:70 | Trade executed: B - Total trades today: 35
2025-06-11 08:49:11 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:49:11 | INFO     | MassiveScan | info:70 | Market order placed: MSTU sell 24
2025-06-11 08:49:11 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:49:11 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 3 consecutive losses
2025-06-11 08:49:11 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:49:11 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | MSTU | SELL 24 @ $8.2000 | PnL: -$1.80 | Reason: Stop loss triggered
2025-06-11 08:49:11 | INFO     | MassiveScan | info:70 | Trade closed: ce6ae5ac-282d-42f0-ac77-7d818dd3491f - MSTU - P&L: $-1.80
2025-06-11 08:49:11 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:49:19 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:49:19 | INFO     | MassiveScan | info:70 | Market order placed: NCLH sell 10
2025-06-11 08:49:19 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:49:19 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 4 consecutive losses
2025-06-11 08:49:19 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:49:19 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | NCLH | SELL 10 @ $19.6384 | PnL: -$0.72 | Reason: Stop loss triggered
2025-06-11 08:49:19 | INFO     | MassiveScan | info:70 | Trade closed: 497f571b-92d2-4542-b260-f283a23a08a4 - NCLH - P&L: $-0.72
2025-06-11 08:49:19 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:49:27 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:49:27 | INFO     | MassiveScan | info:70 | Market order placed: AAL sell 16
2025-06-11 08:49:27 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:49:27 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 5 consecutive losses
2025-06-11 08:49:27 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:49:27 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AAL | SELL 16 @ $11.7886 | PnL: -$0.66 | Reason: Stop loss triggered
2025-06-11 08:49:27 | INFO     | MassiveScan | info:70 | Trade closed: 5fc5e926-67cc-4fa0-bbb4-c2316e44ab84 - AAL - P&L: $-0.66
2025-06-11 08:49:27 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:49:30 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:49:30 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:49:30 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:49:30 | INFO     | MassiveScan | info:70 | Stock screener returned 663 stocks
2025-06-11 08:49:30 | INFO     | MassiveScan | info:70 | Added 663 stocks from screener
2025-06-11 08:49:30 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:49:30 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:49:31 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:49:31 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:49:31 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:49:31 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:49:31 | INFO     | MassiveScan | info:70 | Scannable universe: 767 symbols
2025-06-11 08:49:32 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.78 | target_price: 46.79 | expected_profit: 1.0 | risk_reward: 2.111678215550129
2025-06-11 08:49:32 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:49:42 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:49:42 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:49:44 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | volume_surge | Confidence: 80.25% | entry_price: 18.405 | target_price: 18.415000000000003 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:49:44 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:49:46 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:49:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CMG | rsi_overbought | Confidence: 53.87% | entry_price: 51.525 | target_price: 51.515 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:49:47 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:49:50 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:50:02 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:50:14 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:50:14 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:50:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CVE.TO | vwap_bounce | Confidence: 80.00% | entry_price: 19.05 | target_price: 19.060000000000002 | expected_profit: 1.0 | risk_reward: 6.223809274559502
2025-06-11 08:50:16 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:50:23 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EMLC | vwap_bounce | Confidence: 80.00% | entry_price: 24.785 | target_price: 24.775 | expected_profit: 1.0 | risk_reward: 4.217766894956156
2025-06-11 08:50:23 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:50:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:50:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:50:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CDE | vwap_bounce | Confidence: 80.00% | entry_price: 9.34 | target_price: 9.35 | expected_profit: 1.0 | risk_reward: 9.588609997171313
2025-06-11 08:50:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:50:39 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:50:39 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:50:41 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:50:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TSLL?apikey=********************************
2025-06-11 08:50:58 | WARNING  | MassiveScan | warning:78 | No quote data for TSLL
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/MORF?apikey=********************************
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/ACCD?apikey=********************************
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ALPN?apikey=********************************
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/BLU?apikey=********************************
2025-06-11 08:50:59 | WARNING  | MassiveScan | warning:78 | No quote data for ALPN
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/CONY?apikey=********************************
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OSG?apikey=********************************
2025-06-11 08:50:59 | WARNING  | MassiveScan | warning:78 | No quote data for OSG
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/CLI?apikey=********************************
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SVRE?apikey=********************************
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/F?apikey=********************************
2025-06-11 08:50:59 | WARNING  | MassiveScan | warning:78 | No quote data for SVRE
2025-06-11 08:50:59 | WARNING  | MassiveScan | warning:78 | No quote data for F
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/MSTY?apikey=********************************
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CCL?apikey=********************************
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RF?apikey=********************************
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RBLX?apikey=********************************
2025-06-11 08:50:59 | WARNING  | MassiveScan | warning:78 | No quote data for CCL
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JAGG?apikey=********************************
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NVO?apikey=********************************
2025-06-11 08:50:59 | WARNING  | MassiveScan | warning:78 | No quote data for RF
2025-06-11 08:50:59 | WARNING  | MassiveScan | warning:78 | No quote data for RBLX
2025-06-11 08:50:59 | WARNING  | MassiveScan | warning:78 | No quote data for JAGG
2025-06-11 08:50:59 | WARNING  | MassiveScan | warning:78 | No quote data for NVO
2025-06-11 08:50:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DPRO?apikey=********************************
2025-06-11 08:50:59 | WARNING  | MassiveScan | warning:78 | No quote data for DPRO
2025-06-11 08:51:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Scan completed: 9 signals found in 90.0s
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Found 9 trading signals
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Processing signal: OUST - volume_surge (confidence: 80.25%)
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for OUST: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for OUST
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Processing signal: SWTX - vwap_bounce (confidence: 80.00%)
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for SWTX: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for SWTX
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Processing signal: PDCO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for PDCO: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PDCO
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Processing signal: JWN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for JWN: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for JWN
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Processing signal: CVE.TO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for CVE.TO: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for CVE.TO
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Processing signal: EMLC - vwap_bounce (confidence: 80.00%)
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for EMLC: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for EMLC
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Processing signal: PTVE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for PTVE: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PTVE
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Processing signal: CDE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for CDE: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for CDE
2025-06-11 08:51:00 | INFO     | MassiveScan | info:70 | Processing signal: CMG - rsi_overbought (confidence: 53.87%)
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for CMG: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for CMG
2025-06-11 08:51:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/ACHC,CMG,B,KGC,SWTX,WBD,AMCR,VALE,AVDX,GTLB,BAC,SGOV,INTR,AFRM,RCAT,CORZ,EMLC?apikey=********************************
2025-06-11 08:51:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/VALE?apikey=********************************
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for VALE
2025-06-11 08:51:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/SWTX?apikey=********************************
2025-06-11 08:51:01 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for SWTX
2025-06-11 08:51:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/BAC?apikey=********************************
2025-06-11 08:51:01 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for BAC
2025-06-11 08:51:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/SGOV?apikey=********************************
2025-06-11 08:51:01 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for SGOV
2025-06-11 08:51:04 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 35 | PnL: $-4.38 | Win Rate: 25.0% | Avg Profit: $-0.13
2025-06-11 08:51:04 | INFO     | MassiveScan | info:70 | Status Report:
2025-06-11 08:51:04 | INFO     | MassiveScan | info:70 |   Runtime: 0.2 hours
2025-06-11 08:51:04 | INFO     | MassiveScan | info:70 |   Signals processed: 69
2025-06-11 08:51:04 | INFO     | MassiveScan | info:70 |   Trades executed: 35
2025-06-11 08:51:04 | INFO     | MassiveScan | info:70 |   Active trades: 23
2025-06-11 08:51:04 | INFO     | MassiveScan | info:70 |   Daily P&L: $-4.38
2025-06-11 08:51:04 | INFO     | MassiveScan | info:70 |   Win rate: 25.0%
2025-06-11 08:51:04 | INFO     | MassiveScan | info:70 |   Scans completed: 7
2025-06-11 08:51:04 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-0.13
2025-06-11 08:51:04 | WARNING  | MassiveScan | warning:78 | Low win rate: 25.0%
2025-06-11 08:51:08 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:51:08 | INFO     | MassiveScan | info:70 | Market order placed: WBD buy 19
2025-06-11 08:51:08 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:51:08 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | WBD | BUY 19 @ $10.4500 | PnL: +$1.05 | Reason: Profit target reached
2025-06-11 08:51:08 | INFO     | MassiveScan | info:70 | Trade closed: 6c2e08d0-6750-4b6d-82b9-7fb6577413f9 - WBD - P&L: $1.05
2025-06-11 08:51:08 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:51:18 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:51:18 | INFO     | MassiveScan | info:70 | Market order placed: GTLB sell 4
2025-06-11 08:51:18 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:51:18 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:51:18 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | GTLB | SELL 4 @ $42.6250 | PnL: -$3.22 | Reason: Stop loss triggered
2025-06-11 08:51:18 | INFO     | MassiveScan | info:70 | Trade closed: 02475a17-3b4b-434a-83f4-7ad484314e28 - GTLB - P&L: $-3.22
2025-06-11 08:51:18 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:51:30 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:51:30 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:51:30 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:51:30 | INFO     | MassiveScan | info:70 | Stock screener returned 691 stocks
2025-06-11 08:51:30 | INFO     | MassiveScan | info:70 | Added 691 stocks from screener
2025-06-11 08:51:30 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:51:30 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:51:31 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:51:31 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:51:31 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:51:31 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:51:31 | INFO     | MassiveScan | info:70 | Scannable universe: 794 symbols
2025-06-11 08:51:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PINS | vwap_bounce | Confidence: 80.00% | entry_price: 34.73 | target_price: 34.739999999999995 | expected_profit: 1.0 | risk_reward: 2.132202298829007
2025-06-11 08:51:35 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:51:41 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:51:41 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:51:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CONL | vwap_bounce | Confidence: 80.00% | entry_price: 26.155 | target_price: 26.145 | expected_profit: 1.0 | risk_reward: 4.040853066371055
2025-06-11 08:51:47 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:51:47 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:51:48 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:51:49 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:51:55 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AVDX | vwap_bounce | Confidence: 80.00% | entry_price: 9.785 | target_price: 9.795 | expected_profit: 1.0 | risk_reward: 5.361181703698685
2025-06-11 08:51:55 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:52:05 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:52:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:52:17 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:52:18 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SGOV | vwap_bounce | Confidence: 80.00% | entry_price: 100.47 | target_price: 100.46 | expected_profit: 1.0 | risk_reward: 2.3852803159165576
2025-06-11 08:52:18 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:52:31 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:52:31 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:52:32 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | B | vwap_bounce | Confidence: 80.00% | entry_price: 20.165 | target_price: 20.154999999999998 | expected_profit: 1.0 | risk_reward: 2.4261027683130356
2025-06-11 08:52:32 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:52:43 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:52:43 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:52:45 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:52:48 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GTLB | volume_surge | Confidence: 71.79% | entry_price: 42.875 | target_price: 42.885 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:52:48 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:52:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XOM?apikey=********************************
2025-06-11 08:52:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HLXA?apikey=********************************
2025-06-11 08:52:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AA?apikey=********************************
2025-06-11 08:52:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SJR-B.TO?apikey=********************************
2025-06-11 08:52:59 | WARNING  | MassiveScan | warning:78 | No quote data for XOM
2025-06-11 08:52:59 | WARNING  | MassiveScan | warning:78 | No quote data for HLXA
2025-06-11 08:52:59 | WARNING  | MassiveScan | warning:78 | No quote data for AA
2025-06-11 08:52:59 | WARNING  | MassiveScan | warning:78 | No quote data for SJR-B.TO
2025-06-11 08:52:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CD?apikey=********************************
2025-06-11 08:52:59 | WARNING  | MassiveScan | warning:78 | No quote data for CD
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VRTV?apikey=********************************
2025-06-11 08:53:00 | WARNING  | MassiveScan | warning:78 | No quote data for VRTV
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/WRD?apikey=********************************
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TSLQ?apikey=********************************
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/HSC?apikey=********************************
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PYCR?apikey=********************************
2025-06-11 08:53:00 | WARNING  | MassiveScan | warning:78 | No quote data for TSLQ
2025-06-11 08:53:00 | WARNING  | MassiveScan | warning:78 | No quote data for PYCR
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RADI?apikey=********************************
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/NU?apikey=********************************
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/COLAR?apikey=********************************
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CEQP?apikey=********************************
2025-06-11 08:53:00 | WARNING  | MassiveScan | warning:78 | No quote data for RADI
2025-06-11 08:53:00 | WARNING  | MassiveScan | warning:78 | No quote data for COLAR
2025-06-11 08:53:00 | WARNING  | MassiveScan | warning:78 | No quote data for CEQP
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/FLMN?apikey=********************************
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TEVA?apikey=********************************
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ON?apikey=********************************
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MGI?apikey=********************************
2025-06-11 08:53:00 | WARNING  | MassiveScan | warning:78 | No quote data for TEVA
2025-06-11 08:53:00 | WARNING  | MassiveScan | warning:78 | No quote data for ON
2025-06-11 08:53:00 | WARNING  | MassiveScan | warning:78 | No quote data for MGI
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NCM.TO?apikey=********************************
2025-06-11 08:53:00 | WARNING  | MassiveScan | warning:78 | No quote data for NCM.TO
2025-06-11 08:53:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ROCC?apikey=********************************
2025-06-11 08:53:00 | WARNING  | MassiveScan | warning:78 | No quote data for ROCC
2025-06-11 08:53:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:53:01 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:53:01 | INFO     | MassiveScan | info:70 | Scan completed: 9 signals found in 91.0s
2025-06-11 08:53:01 | INFO     | MassiveScan | info:70 | Found 9 trading signals
2025-06-11 08:53:01 | INFO     | MassiveScan | info:70 | Processing signal: PINS - vwap_bounce (confidence: 80.00%)
2025-06-11 08:53:01 | INFO     | MassiveScan | info:70 | Position size calculated: 5 shares for PINS (risk per share: $0.0047)
2025-06-11 08:53:02 | INFO     | MassiveScan | info:70 | Position size calculated: 5 shares for PINS (risk per share: $0.0047)
2025-06-11 08:53:02 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:02 | INFO     | MassiveScan | info:70 | Market order placed: PINS buy 5
2025-06-11 08:53:02 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:02 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | PINS | BUY 5 @ $34.7300 | Strategy: vwap_bounce
2025-06-11 08:53:02 | INFO     | MassiveScan | info:70 | Trade executed: c698c9aa-f38e-4a02-ba3d-d3380b165313 - PINS buy 5
2025-06-11 08:53:02 | INFO     | MassiveScan | info:70 | Trade executed: PINS - Total trades today: 36
2025-06-11 08:53:02 | INFO     | MassiveScan | info:70 | Processing signal: PDCO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:53:03 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:53:03 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:53:04 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:53:04 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:53:04 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PDCO
2025-06-11 08:53:04 | INFO     | MassiveScan | info:70 | Processing signal: CONL - vwap_bounce (confidence: 80.00%)
2025-06-11 08:53:04 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for CONL (risk per share: $0.0025)
2025-06-11 08:53:05 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for CONL (risk per share: $0.0025)
2025-06-11 08:53:05 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:05 | INFO     | MassiveScan | info:70 | Market order placed: CONL buy 7
2025-06-11 08:53:05 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:05 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CONL | BUY 7 @ $26.1550 | Strategy: vwap_bounce
2025-06-11 08:53:05 | INFO     | MassiveScan | info:70 | Trade executed: 1989a6d2-f91d-435f-954b-6ee13ed766f9 - CONL buy 7
2025-06-11 08:53:05 | INFO     | MassiveScan | info:70 | Trade executed: CONL - Total trades today: 37
2025-06-11 08:53:05 | INFO     | MassiveScan | info:70 | Processing signal: AVDX - vwap_bounce (confidence: 80.00%)
2025-06-11 08:53:06 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for AVDX (risk per share: $0.0019)
2025-06-11 08:53:06 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for AVDX (risk per share: $0.0019)
2025-06-11 08:53:07 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:07 | INFO     | MassiveScan | info:70 | Market order placed: AVDX buy 20
2025-06-11 08:53:07 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:07 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AVDX | BUY 20 @ $9.7850 | Strategy: vwap_bounce
2025-06-11 08:53:07 | INFO     | MassiveScan | info:70 | Trade executed: dd4a969e-c067-4a8b-a253-eecc629e96d1 - AVDX buy 20
2025-06-11 08:53:07 | INFO     | MassiveScan | info:70 | Trade executed: AVDX - Total trades today: 38
2025-06-11 08:53:07 | INFO     | MassiveScan | info:70 | Processing signal: JWN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:53:07 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:53:08 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:53:08 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:53:08 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:53:08 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for JWN
2025-06-11 08:53:08 | INFO     | MassiveScan | info:70 | Processing signal: SGOV - vwap_bounce (confidence: 80.00%)
2025-06-11 08:53:09 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for SGOV (risk per share: $0.0042)
2025-06-11 08:53:09 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for SGOV (risk per share: $0.0042)
2025-06-11 08:53:10 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:10 | INFO     | MassiveScan | info:70 | Market order placed: SGOV buy 1
2025-06-11 08:53:10 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:10 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SGOV | BUY 1 @ $100.4700 | Strategy: vwap_bounce
2025-06-11 08:53:10 | INFO     | MassiveScan | info:70 | Trade executed: 04dc9b83-6772-4a08-ac2e-2465b2b77323 - SGOV buy 1
2025-06-11 08:53:10 | INFO     | MassiveScan | info:70 | Trade executed: SGOV - Total trades today: 39
2025-06-11 08:53:10 | INFO     | MassiveScan | info:70 | Processing signal: PTVE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:53:10 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:53:11 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:53:11 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:53:11 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:53:11 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PTVE
2025-06-11 08:53:11 | INFO     | MassiveScan | info:70 | Processing signal: B - vwap_bounce (confidence: 80.00%)
2025-06-11 08:53:12 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for B (risk per share: $0.0041)
2025-06-11 08:53:12 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for B (risk per share: $0.0041)
2025-06-11 08:53:13 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:13 | INFO     | MassiveScan | info:70 | Market order placed: B buy 9
2025-06-11 08:53:13 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:13 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | B | BUY 9 @ $20.1650 | Strategy: vwap_bounce
2025-06-11 08:53:13 | INFO     | MassiveScan | info:70 | Trade executed: 2e288114-2d3e-4945-a685-12643fa8e4f5 - B buy 9
2025-06-11 08:53:13 | INFO     | MassiveScan | info:70 | Trade executed: B - Total trades today: 40
2025-06-11 08:53:13 | INFO     | MassiveScan | info:70 | Processing signal: GTLB - volume_surge (confidence: 71.79%)
2025-06-11 08:53:13 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:53:14 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:53:14 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:14 | INFO     | MassiveScan | info:70 | Market order placed: GTLB buy 4
2025-06-11 08:53:14 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:14 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | GTLB | BUY 4 @ $42.8750 | Strategy: volume_surge
2025-06-11 08:53:14 | INFO     | MassiveScan | info:70 | Trade executed: e06007b7-d2aa-42cb-9749-9577838c2dce - GTLB buy 4
2025-06-11 08:53:14 | INFO     | MassiveScan | info:70 | Trade executed: GTLB - Total trades today: 41
2025-06-11 08:53:15 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:15 | INFO     | MassiveScan | info:70 | Market order placed: VALE sell 20
2025-06-11 08:53:15 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:15 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:53:15 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | VALE | SELL 20 @ $9.6150 | PnL: +$1.00 | Reason: Profit target reached
2025-06-11 08:53:15 | INFO     | MassiveScan | info:70 | Trade closed: d8c4bdb5-1242-4014-8677-f24de9febc15 - VALE - P&L: $1.00
2025-06-11 08:53:15 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:53:23 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:23 | INFO     | MassiveScan | info:70 | Market order placed: INTR sell 29
2025-06-11 08:53:23 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:23 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:53:23 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | INTR | SELL 29 @ $6.8550 | PnL: +$1.01 | Reason: Profit target reached
2025-06-11 08:53:23 | INFO     | MassiveScan | info:70 | Trade closed: 4bfa4117-b22b-4488-8258-88bbb82dd11b - INTR - P&L: $1.01
2025-06-11 08:53:23 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:53:32 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:32 | INFO     | MassiveScan | info:70 | Market order placed: RCAT sell 22
2025-06-11 08:53:32 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:32 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:53:32 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | RCAT | SELL 22 @ $8.8100 | PnL: +$2.32 | Reason: Profit target reached
2025-06-11 08:53:32 | INFO     | MassiveScan | info:70 | Trade closed: f2d18c39-0507-4dfe-8210-ca7bf809d3ff - RCAT - P&L: $2.32
2025-06-11 08:53:32 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:53:32 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:53:32 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:53:32 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:53:33 | INFO     | MassiveScan | info:70 | Stock screener returned 711 stocks
2025-06-11 08:53:33 | INFO     | MassiveScan | info:70 | Added 711 stocks from screener
2025-06-11 08:53:33 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:53:33 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:53:33 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:53:33 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:53:33 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:53:33 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:53:33 | INFO     | MassiveScan | info:70 | Scannable universe: 812 symbols
2025-06-11 08:53:34 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.78 | target_price: 46.77 | expected_profit: 1.0 | risk_reward: 1.9157739751647964
2025-06-11 08:53:34 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:53:43 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:53:43 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:53:49 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:53:51 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:53:53 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:54:08 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:54:18 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:54:18 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:54:29 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:54:29 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:54:43 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:54:43 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:54:43 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:54:51 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:54:51 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:54:51 | INFO     | MassiveScan | info:70 | Buying Power: $292,496.86
2025-06-11 08:54:51 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,896.87
2025-06-11 08:54:51 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $2.83, Trades: 7, Open: 2
2025-06-11 08:54:51 | INFO     | MassiveScan | info:70 | Loaded 2 open trades
2025-06-11 08:54:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/PETQ?apikey=********************************
2025-06-11 08:54:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/XBI?apikey=********************************
2025-06-11 08:54:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/OXY?apikey=********************************
2025-06-11 08:54:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HSC?apikey=********************************
2025-06-11 08:54:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XPEV?apikey=********************************
2025-06-11 08:54:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ALBO?apikey=********************************
2025-06-11 08:54:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CD?apikey=********************************
2025-06-11 08:54:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VRTV?apikey=********************************
2025-06-11 08:54:58 | WARNING  | MassiveScan | warning:78 | No quote data for HSC
2025-06-11 08:54:58 | WARNING  | MassiveScan | warning:78 | No quote data for XPEV
2025-06-11 08:54:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/UCBI?apikey=********************************
2025-06-11 08:54:58 | WARNING  | MassiveScan | warning:78 | No quote data for ALBO
2025-06-11 08:54:58 | WARNING  | MassiveScan | warning:78 | No quote data for CD
2025-06-11 08:54:58 | WARNING  | MassiveScan | warning:78 | No quote data for VRTV
2025-06-11 08:54:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AA?apikey=********************************
2025-06-11 08:54:58 | WARNING  | MassiveScan | warning:78 | No quote data for AA
2025-06-11 08:54:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:54:59 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:54:59 | INFO     | MassiveScan | info:70 | Scan completed: 4 signals found in 86.4s
2025-06-11 08:54:59 | INFO     | MassiveScan | info:70 | Found 4 trading signals
2025-06-11 08:54:59 | INFO     | MassiveScan | info:70 | Processing signal: SWTX - vwap_bounce (confidence: 80.00%)
2025-06-11 08:54:59 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0052)
2025-06-11 08:55:00 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0052)
2025-06-11 08:55:00 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:55:00 | INFO     | MassiveScan | info:70 | Market order placed: SWTX buy 4
2025-06-11 08:55:00 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:55:00 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SWTX | BUY 4 @ $46.7800 | Strategy: vwap_bounce
2025-06-11 08:55:00 | INFO     | MassiveScan | info:70 | Trade executed: c373d943-fdfa-41f6-8708-af8196afacbd - SWTX buy 4
2025-06-11 08:55:00 | INFO     | MassiveScan | info:70 | Trade executed: SWTX - Total trades today: 42
2025-06-11 08:55:00 | INFO     | MassiveScan | info:70 | Processing signal: PDCO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:55:01 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:55:01 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:55:02 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:55:02 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:55:02 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PDCO
2025-06-11 08:55:02 | INFO     | MassiveScan | info:70 | Processing signal: JWN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:55:02 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:55:03 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:55:03 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:55:03 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:55:03 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for JWN
2025-06-11 08:55:03 | INFO     | MassiveScan | info:70 | Processing signal: PTVE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:55:04 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:55:04 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:55:05 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:55:05 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:55:05 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PTVE
2025-06-11 08:55:08 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:55:08 | INFO     | MassiveScan | info:70 | Market order placed: ACHC sell 8
2025-06-11 08:55:08 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:55:08 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:55:08 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | ACHC | SELL 8 @ $22.2900 | PnL: -$0.64 | Reason: Stop loss triggered
2025-06-11 08:55:08 | INFO     | MassiveScan | info:70 | Trade closed: ffd86684-acc6-4e7a-b815-53300f28a14c - ACHC - P&L: $-0.64
2025-06-11 08:55:08 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:55:16 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:55:16 | INFO     | MassiveScan | info:70 | Market order placed: AMCR sell 21
2025-06-11 08:55:16 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:55:16 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:55:16 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AMCR | SELL 21 @ $9.2100 | PnL: -$0.52 | Reason: Stop loss triggered
2025-06-11 08:55:16 | INFO     | MassiveScan | info:70 | Trade closed: de5ed427-1bf0-4430-8ff6-03afd35266d9 - AMCR - P&L: $-0.52
2025-06-11 08:55:16 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:55:25 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:55:25 | INFO     | MassiveScan | info:70 | Market order placed: CORZ sell 15
2025-06-11 08:55:25 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:55:25 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 3 consecutive losses
2025-06-11 08:55:25 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:55:25 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CORZ | SELL 15 @ $12.5400 | PnL: -$0.70 | Reason: Stop loss triggered
2025-06-11 08:55:25 | INFO     | MassiveScan | info:70 | Trade closed: 5824761c-35b1-4d82-bb10-c021d73bb84a - CORZ - P&L: $-0.70
2025-06-11 08:55:25 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:55:29 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:55:29 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:55:29 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:55:29 | INFO     | MassiveScan | info:70 | Stock screener returned 726 stocks
2025-06-11 08:55:29 | INFO     | MassiveScan | info:70 | Added 726 stocks from screener
2025-06-11 08:55:29 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:55:29 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:55:30 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:55:30 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:55:30 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:55:30 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:55:30 | INFO     | MassiveScan | info:70 | Scannable universe: 827 symbols
2025-06-11 08:55:36 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Buying Power: $292,871.53
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,884.51
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $2.83, Trades: 7, Open: 2
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Loaded 2 open trades
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Stock screener returned 726 stocks
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Added 726 stocks from screener
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:55:38 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:55:38 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:55:38 | INFO     | MassiveScan | info:70 | Scannable universe: 827 symbols
2025-06-11 08:55:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CRWV | vwap_bounce | Confidence: 80.00% | entry_price: 155.615 | target_price: 155.60500000000002 | expected_profit: 1.0 | risk_reward: 2.5351962182541596
2025-06-11 08:55:40 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:55:41 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:55:41 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:55:43 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | YMM | vwap_bounce | Confidence: 80.00% | entry_price: 12.305 | target_price: 12.295 | expected_profit: 1.0 | risk_reward: 3.0807582823837887
2025-06-11 08:55:43 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:55:43 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | momentum_breakout | Confidence: 95.00% | entry_price: 18.805 | target_price: 18.815 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:55:43 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:55:43 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | volume_surge | Confidence: 73.77% | entry_price: 18.805 | target_price: 18.815 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:55:43 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:55:45 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:55:45 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:55:45 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:55:46 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BIL | vwap_bounce | Confidence: 80.00% | entry_price: 91.53 | target_price: 91.52 | expected_profit: 1.0 | risk_reward: 3.202570651099495
2025-06-11 08:55:46 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:55:46 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:55:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BIL | vwap_bounce | Confidence: 80.00% | entry_price: 91.53 | target_price: 91.52 | expected_profit: 1.0 | risk_reward: 3.202570651099495
2025-06-11 08:55:47 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:55:48 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:55:51 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:55:57 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JBLU | vwap_bounce | Confidence: 80.00% | entry_price: 5.035 | target_price: 5.025 | expected_profit: 1.0 | risk_reward: 1.5548293900296364
2025-06-11 08:55:57 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:56:00 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.885 | target_price: 12.895 | expected_profit: 1.0 | risk_reward: 1.8825381703830102
2025-06-11 08:56:00 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:56:03 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:56:03 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:56:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMCR | vwap_bounce | Confidence: 80.00% | entry_price: 9.225 | target_price: 9.235 | expected_profit: 1.0 | risk_reward: 50.665341328784635
2025-06-11 08:56:05 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Bot started from GUI
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Market is open
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Broker connected - Buying Power: $292,871.53
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Daily risk statistics reset
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | TRADING STRATEGY - Target: $50 Daily Profit
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Configuration loaded:
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Target profit per trade: $1.0
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Stop loss per trade: $0.5
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Max daily trades: 100
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Max daily loss limit: $25.0
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Max concurrent positions: 10
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Scan interval: 30s
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Expected trades for $50: 50 trades
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | MassiveScan Trading Bot initialized successfully
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Starting MassiveScan Trading Bot...
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Starting market scanning...
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:56:08 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.885 | target_price: 12.895 | expected_profit: 1.0 | risk_reward: 1.8825381703830102
2025-06-11 08:56:08 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:56:09 | INFO     | MassiveScan | info:70 | Stock screener returned 738 stocks
2025-06-11 08:56:09 | INFO     | MassiveScan | info:70 | Added 738 stocks from screener
2025-06-11 08:56:09 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:56:09 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:56:09 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:56:09 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:56:09 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:56:09 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:56:09 | INFO     | MassiveScan | info:70 | Scannable universe: 839 symbols
2025-06-11 08:56:12 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:56:12 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:56:12 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:56:13 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:56:14 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:56:15 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TIGR | vwap_bounce | Confidence: 80.00% | entry_price: 8.605 | target_price: 8.615 | expected_profit: 1.0 | risk_reward: 2.269937195986822
2025-06-11 08:56:15 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:56:15 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JBLU | vwap_bounce | Confidence: 80.00% | entry_price: 5.0304 | target_price: 5.0204 | expected_profit: 1.0 | risk_reward: 5.459785398209257
2025-06-11 08:56:15 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:56:21 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:56:21 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:56:21 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:56:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AUR | vwap_bounce | Confidence: 80.00% | entry_price: 5.935 | target_price: 5.925 | expected_profit: 1.0 | risk_reward: 5.405701077446651
2025-06-11 08:56:22 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:56:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/KEY?apikey=********************************
2025-06-11 08:56:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/SBUX?apikey=********************************
2025-06-11 08:56:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SAFX?apikey=********************************
2025-06-11 08:56:23 | WARNING  | MassiveScan | warning:78 | No quote data for SAFX
2025-06-11 08:56:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BULL?apikey=********************************
2025-06-11 08:56:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DFNS?apikey=********************************
2025-06-11 08:56:23 | WARNING  | MassiveScan | warning:78 | No quote data for BULL
2025-06-11 08:56:23 | WARNING  | MassiveScan | warning:78 | No quote data for DFNS
2025-06-11 08:56:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/OKLO?apikey=********************************
2025-06-11 08:56:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RENB?apikey=********************************
2025-06-11 08:56:23 | WARNING  | MassiveScan | warning:78 | No quote data for RENB
2025-06-11 08:56:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PAYA?apikey=********************************
2025-06-11 08:56:23 | WARNING  | MassiveScan | warning:78 | No quote data for PAYA
2025-06-11 08:56:23 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:56:23 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:56:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/CDE?apikey=********************************
2025-06-11 08:56:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BBAI?apikey=********************************
2025-06-11 08:56:24 | WARNING  | MassiveScan | warning:78 | No quote data for BBAI
2025-06-11 08:56:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/INUV?apikey=********************************
2025-06-11 08:56:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FORG?apikey=********************************
2025-06-11 08:56:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CNR.TO?apikey=********************************
2025-06-11 08:56:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/AGNC?apikey=********************************
2025-06-11 08:56:24 | WARNING  | MassiveScan | warning:78 | No quote data for INUV
2025-06-11 08:56:24 | WARNING  | MassiveScan | warning:78 | No quote data for FORG
2025-06-11 08:56:24 | WARNING  | MassiveScan | warning:78 | No quote data for CNR.TO
2025-06-11 08:56:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AMAM?apikey=********************************
2025-06-11 08:56:24 | WARNING  | MassiveScan | warning:78 | No quote data for AMAM
2025-06-11 08:56:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:56:24 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:56:24 | INFO     | MassiveScan | info:70 | Scan completed: 2 signals found in 15.8s
2025-06-11 08:56:24 | INFO     | MassiveScan | info:70 | Found 2 trading signals
2025-06-11 08:56:24 | INFO     | MassiveScan | info:70 | Starting signal processing...
2025-06-11 08:56:24 | INFO     | MassiveScan | info:70 | Processing signal: JBLU - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:24 | INFO     | MassiveScan | info:70 | Position size calculated: 39 shares for JBLU (risk per share: $0.0018)
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Position size calculated: 39 shares for JBLU (risk per share: $0.0018)
2025-06-11 08:56:25 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Scan completed: 12 signals found in 56.0s
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Found 12 trading signals
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Processing signal: OUST - momentum_breakout (confidence: 95.00%)
2025-06-11 08:56:25 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Market order placed: JBLU buy 39
2025-06-11 08:56:25 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:25 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | JBLU | BUY 39 @ $5.0304 | Strategy: vwap_bounce
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Trade executed: db64865a-6caa-47a2-87f5-b0a7869c0ebf - JBLU buy 39
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Trade executed: JBLU - Total trades today: 1
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Processing signal: AUR - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:25 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Scan completed: 4 signals found in 48.2s
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Position size calculated: 33 shares for AUR (risk per share: $0.0018)
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 08:56:26 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Market order placed: OUST buy 10
2025-06-11 08:56:26 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:26 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | OUST | BUY 10 @ $18.8050 | Strategy: momentum_breakout
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Trade executed: 28b7405f-dad2-4828-a8e5-33f576b47494 - OUST buy 10
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Trade executed: OUST - Total trades today: 43
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Processing signal: CRWV - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Position size calculated: 33 shares for AUR (risk per share: $0.0018)
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:56:27 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Market order placed: AUR buy 33
2025-06-11 08:56:27 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:27 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AUR | BUY 33 @ $5.9350 | Strategy: vwap_bounce
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Trade executed: 7d54fcc4-9e4a-422c-9db8-1dd88ca5c994 - AUR buy 33
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Trade executed: AUR - Total trades today: 2
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Starting trade monitoring...
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Trade monitoring started
2025-06-11 08:56:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU,AFRM,AUR,CMG?apikey=********************************
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for CRWV (risk per share: $0.0039)
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:56:27 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 08:56:27 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 08:56:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AFRM?apikey=********************************
2025-06-11 08:56:27 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AFRM
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for CRWV (risk per share: $0.0039)
2025-06-11 08:56:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG?apikey=********************************
2025-06-11 08:56:28 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for CMG
2025-06-11 08:56:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG,AFRM?apikey=********************************
2025-06-11 08:56:28 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:28 | INFO     | MassiveScan | info:70 | Market order placed: CRWV buy 1
2025-06-11 08:56:28 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:28 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CRWV | BUY 1 @ $155.6150 | Strategy: vwap_bounce
2025-06-11 08:56:28 | INFO     | MassiveScan | info:70 | Trade executed: d7a8860f-d42c-4c52-a2df-11f7ce496a41 - CRWV buy 1
2025-06-11 08:56:28 | INFO     | MassiveScan | info:70 | Trade executed: CRWV - Total trades today: 44
2025-06-11 08:56:28 | INFO     | MassiveScan | info:70 | Processing signal: PDCO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:28 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:56:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:28 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:29 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:29 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:29 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:56:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:29 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:29 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:56:29 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:56:29 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PDCO
2025-06-11 08:56:29 | INFO     | MassiveScan | info:70 | Processing signal: YMM - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:30 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for YMM (risk per share: $0.0032)
2025-06-11 08:56:30 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for YMM (risk per share: $0.0032)
2025-06-11 08:56:31 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:31 | INFO     | MassiveScan | info:70 | Market order placed: YMM buy 16
2025-06-11 08:56:31 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:31 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | YMM | BUY 16 @ $12.3050 | Strategy: vwap_bounce
2025-06-11 08:56:31 | INFO     | MassiveScan | info:70 | Trade executed: 00faab81-1ba3-4f02-8e28-d88a7601dd21 - YMM buy 16
2025-06-11 08:56:31 | INFO     | MassiveScan | info:70 | Trade executed: YMM - Total trades today: 45
2025-06-11 08:56:31 | INFO     | MassiveScan | info:70 | Processing signal: BIL - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:31 | INFO     | MassiveScan | info:70 | Position size calculated: 2 shares for BIL (risk per share: $0.0031)
2025-06-11 08:56:32 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU,AFRM,AUR,CMG?apikey=********************************
2025-06-11 08:56:32 | INFO     | MassiveScan | info:70 | Position size calculated: 2 shares for BIL (risk per share: $0.0031)
2025-06-11 08:56:32 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:32 | INFO     | MassiveScan | info:70 | Market order placed: BIL buy 2
2025-06-11 08:56:32 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:32 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | BIL | BUY 2 @ $91.5300 | Strategy: vwap_bounce
2025-06-11 08:56:32 | INFO     | MassiveScan | info:70 | Trade executed: 0c118c30-bf4d-4559-9172-8687ca7d6738 - BIL buy 2
2025-06-11 08:56:32 | INFO     | MassiveScan | info:70 | Trade executed: BIL - Total trades today: 46
2025-06-11 08:56:32 | INFO     | MassiveScan | info:70 | Processing signal: JBLU - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG,AFRM?apikey=********************************
2025-06-11 08:56:33 | INFO     | MassiveScan | info:70 | Position size calculated: 39 shares for JBLU (risk per share: $0.0064)
2025-06-11 08:56:33 | INFO     | MassiveScan | info:70 | Position size calculated: 39 shares for JBLU (risk per share: $0.0064)
2025-06-11 08:56:34 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:34 | INFO     | MassiveScan | info:70 | Market order placed: JBLU buy 39
2025-06-11 08:56:34 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:34 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | JBLU | BUY 39 @ $5.0350 | Strategy: vwap_bounce
2025-06-11 08:56:34 | INFO     | MassiveScan | info:70 | Trade executed: 114ac2c3-2332-4f9b-8f17-344c2a4d3730 - JBLU buy 39
2025-06-11 08:56:34 | INFO     | MassiveScan | info:70 | Trade executed: JBLU - Total trades today: 47
2025-06-11 08:56:34 | INFO     | MassiveScan | info:70 | Processing signal: AMCR - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:34 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:34 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:34 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0002)
2025-06-11 08:56:34 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:34 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:35 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:35 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:35 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0002)
2025-06-11 08:56:35 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:35 | INFO     | MassiveScan | info:70 | Market order placed: AMCR buy 21
2025-06-11 08:56:35 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:35 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AMCR | BUY 21 @ $9.2250 | Strategy: vwap_bounce
2025-06-11 08:56:35 | INFO     | MassiveScan | info:70 | Trade executed: 935f3dc2-c119-417e-8f19-d2b3dec5c9b0 - AMCR buy 21
2025-06-11 08:56:35 | INFO     | MassiveScan | info:70 | Trade executed: AMCR - Total trades today: 48
2025-06-11 08:56:35 | INFO     | MassiveScan | info:70 | Processing signal: CNH - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:36 | INFO     | MassiveScan | info:70 | Position size calculated: 15 shares for CNH (risk per share: $0.0053)
2025-06-11 08:56:36 | INFO     | MassiveScan | info:70 | Position size calculated: 15 shares for CNH (risk per share: $0.0053)
2025-06-11 08:56:37 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU,AFRM,AUR,CMG?apikey=********************************
2025-06-11 08:56:37 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:37 | INFO     | MassiveScan | info:70 | Market order placed: CNH buy 15
2025-06-11 08:56:37 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:37 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CNH | BUY 15 @ $12.8850 | Strategy: vwap_bounce
2025-06-11 08:56:37 | INFO     | MassiveScan | info:70 | Trade executed: 875fd64a-9eb6-481b-a31a-35a448ce4920 - CNH buy 15
2025-06-11 08:56:37 | INFO     | MassiveScan | info:70 | Trade executed: CNH - Total trades today: 49
2025-06-11 08:56:37 | INFO     | MassiveScan | info:70 | Processing signal: JWN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:37 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:56:38 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG,AFRM?apikey=********************************
2025-06-11 08:56:38 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:56:38 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:56:38 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:56:38 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for JWN
2025-06-11 08:56:38 | INFO     | MassiveScan | info:70 | Processing signal: TIGR - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:39 | INFO     | MassiveScan | info:70 | Position size calculated: 23 shares for TIGR (risk per share: $0.0044)
2025-06-11 08:56:39 | INFO     | MassiveScan | info:70 | Position size calculated: 23 shares for TIGR (risk per share: $0.0044)
2025-06-11 08:56:40 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:40 | INFO     | MassiveScan | info:70 | Market order placed: TIGR buy 23
2025-06-11 08:56:40 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:40 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | TIGR | BUY 23 @ $8.6050 | Strategy: vwap_bounce
2025-06-11 08:56:40 | INFO     | MassiveScan | info:70 | Trade executed: f9a5d100-3b1a-480d-9ea8-32ca613f8c4f - TIGR buy 23
2025-06-11 08:56:40 | INFO     | MassiveScan | info:70 | Trade executed: TIGR - Total trades today: 50
2025-06-11 08:56:40 | INFO     | MassiveScan | info:70 | Processing signal: PTVE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:56:40 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:40 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:40 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:40 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:40 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:40 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:40 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:56:41 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:41 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:41 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:56:41 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:56:41 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:56:41 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PTVE
2025-06-11 08:56:41 | INFO     | MassiveScan | info:70 | Processing signal: OUST - volume_surge (confidence: 73.77%)
2025-06-11 08:56:42 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU,AFRM,AUR,CMG?apikey=********************************
2025-06-11 08:56:42 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 08:56:42 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 08:56:43 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:43 | INFO     | MassiveScan | info:70 | Market order placed: OUST buy 10
2025-06-11 08:56:43 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:43 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | OUST | BUY 10 @ $18.8050 | Strategy: volume_surge
2025-06-11 08:56:43 | INFO     | MassiveScan | info:70 | Trade executed: 255b5f1d-2846-419e-8e1b-2ba734381eb1 - OUST buy 10
2025-06-11 08:56:43 | INFO     | MassiveScan | info:70 | Trade executed: OUST - Total trades today: 51
2025-06-11 08:56:43 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,TIGR,BIL,CONL,AVDX,CMG,KGC,CRWV,SWTX,JBLU,B,YMM,AMCR,VALE,AFRM,PINS,OUST,GTLB,BAC,SGOV,EMLC?apikey=********************************
2025-06-11 08:56:43 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG,AFRM?apikey=********************************
2025-06-11 08:56:43 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/SWTX?apikey=********************************
2025-06-11 08:56:43 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for SWTX
2025-06-11 08:56:43 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/BAC?apikey=********************************
2025-06-11 08:56:43 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for BAC
2025-06-11 08:56:43 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/SGOV?apikey=********************************
2025-06-11 08:56:43 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for SGOV
2025-06-11 08:56:44 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/SWTX?apikey=********************************
2025-06-11 08:56:44 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for SWTX
2025-06-11 08:56:44 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/KGC?apikey=********************************
2025-06-11 08:56:44 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for KGC
2025-06-11 08:56:44 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AVDX?apikey=********************************
2025-06-11 08:56:44 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AVDX
2025-06-11 08:56:44 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AMCR?apikey=********************************
2025-06-11 08:56:44 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AMCR
2025-06-11 08:56:44 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/SWTX?apikey=********************************
2025-06-11 08:56:44 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for SWTX
2025-06-11 08:56:45 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/KGC?apikey=********************************
2025-06-11 08:56:45 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for KGC
2025-06-11 08:56:45 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/VALE?apikey=********************************
2025-06-11 08:56:45 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for VALE
2025-06-11 08:56:45 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/EMLC?apikey=********************************
2025-06-11 08:56:45 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for EMLC
2025-06-11 08:56:45 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AFRM?apikey=********************************
2025-06-11 08:56:45 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AFRM
2025-06-11 08:56:45 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG?apikey=********************************
2025-06-11 08:56:45 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for CMG
2025-06-11 08:56:46 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/EMLC?apikey=********************************
2025-06-11 08:56:46 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for EMLC
2025-06-11 08:56:46 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:46 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:46 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:46 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:46 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/B?apikey=********************************
2025-06-11 08:56:46 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for B
2025-06-11 08:56:46 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/PINS?apikey=********************************
2025-06-11 08:56:46 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for PINS
2025-06-11 08:56:46 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:46 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:46 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CONL?apikey=********************************
2025-06-11 08:56:46 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for CONL
2025-06-11 08:56:46 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:46 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:46 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AVDX?apikey=********************************
2025-06-11 08:56:46 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AVDX
2025-06-11 08:56:47 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/SGOV?apikey=********************************
2025-06-11 08:56:47 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for SGOV
2025-06-11 08:56:47 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/B?apikey=********************************
2025-06-11 08:56:47 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for B
2025-06-11 08:56:47 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU,AFRM,AUR,CMG?apikey=********************************
2025-06-11 08:56:47 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 08:56:47 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for GTLB
2025-06-11 08:56:47 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/SWTX?apikey=********************************
2025-06-11 08:56:47 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for SWTX
2025-06-11 08:56:47 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/OUST?apikey=********************************
2025-06-11 08:56:47 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for OUST
2025-06-11 08:56:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CRWV?apikey=********************************
2025-06-11 08:56:48 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for CRWV
2025-06-11 08:56:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/YMM?apikey=********************************
2025-06-11 08:56:48 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for YMM
2025-06-11 08:56:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG,AFRM?apikey=********************************
2025-06-11 08:56:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/BIL?apikey=********************************
2025-06-11 08:56:48 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for BIL
2025-06-11 08:56:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:48 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AMCR?apikey=********************************
2025-06-11 08:56:48 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AMCR
2025-06-11 08:56:49 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH?apikey=********************************
2025-06-11 08:56:49 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for CNH
2025-06-11 08:56:49 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/TIGR?apikey=********************************
2025-06-11 08:56:49 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for TIGR
2025-06-11 08:56:49 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/OUST?apikey=********************************
2025-06-11 08:56:49 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for OUST
2025-06-11 08:56:49 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 51 | PnL: $-4.09 | Win Rate: 35.0% | Avg Profit: $-0.08
2025-06-11 08:56:49 | INFO     | MassiveScan | info:70 | Status Report:
2025-06-11 08:56:49 | INFO     | MassiveScan | info:70 |   Runtime: 0.3 hours
2025-06-11 08:56:49 | INFO     | MassiveScan | info:70 |   Signals processed: 94
2025-06-11 08:56:49 | INFO     | MassiveScan | info:70 |   Trades executed: 51
2025-06-11 08:56:49 | INFO     | MassiveScan | info:70 |   Active trades: 31
2025-06-11 08:56:49 | INFO     | MassiveScan | info:70 |   Daily P&L: $-4.09
2025-06-11 08:56:49 | INFO     | MassiveScan | info:70 |   Win rate: 35.0%
2025-06-11 08:56:49 | INFO     | MassiveScan | info:70 |   Scans completed: 10
2025-06-11 08:56:49 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-0.08
2025-06-11 08:56:49 | WARNING  | MassiveScan | warning:78 | Low win rate: 35.0%
2025-06-11 08:56:49 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,TIGR,BIL,CONL,AVDX,CMG,KGC,CRWV,SWTX,JBLU,B,YMM,AMCR,VALE,AFRM,PINS,OUST,GTLB,BAC,SGOV,EMLC?apikey=********************************
2025-06-11 08:56:51 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:51 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:52 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:52 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:52 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:52 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:52 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:52 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:52 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU,AFRM,AUR,CMG?apikey=********************************
2025-06-11 08:56:53 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG,AFRM?apikey=********************************
2025-06-11 08:56:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:56:54 | INFO     | MassiveScan | info:70 | Market closed, waiting...
2025-06-11 08:56:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,TIGR,BIL,CONL,AVDX,CMG,KGC,CRWV,SWTX,JBLU,B,YMM,AMCR,VALE,AFRM,PINS,OUST,GTLB,BAC,SGOV,EMLC?apikey=********************************
2025-06-11 08:56:55 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:56:55 | INFO     | MassiveScan | info:70 | Market closed, waiting...
2025-06-11 08:56:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AFRM?apikey=********************************
2025-06-11 08:56:57 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AFRM
2025-06-11 08:56:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG?apikey=********************************
2025-06-11 08:56:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU,AFRM,AUR,CMG?apikey=********************************
2025-06-11 08:56:57 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for CMG
2025-06-11 08:56:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:57 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:58 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:56:58 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:56:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/stock-screener?marketCapMoreThan=500000000&volumeMoreThan=500000&priceMoreThan=5.0&priceLowerThan=200.0&limit=2000&apikey=********************************
2025-06-11 08:56:58 | WARNING  | MassiveScan | warning:78 | Stock screener returned no data
2025-06-11 08:56:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:58 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/stock_market/actives?apikey=********************************
2025-06-11 08:56:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AFRM?apikey=********************************
2025-06-11 08:56:58 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AFRM
2025-06-11 08:56:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/stock_market/gainers?apikey=********************************
2025-06-11 08:56:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG?apikey=********************************
2025-06-11 08:56:58 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for CMG
2025-06-11 08:56:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/stock_market/losers?apikey=********************************
2025-06-11 08:56:58 | INFO     | MassiveScan | info:70 | Scannable universe: 0 symbols
2025-06-11 08:56:58 | WARNING  | MassiveScan | warning:78 | No symbols to scan
2025-06-11 08:56:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:56:58 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for JBLU
2025-06-11 08:56:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AFRM?apikey=********************************
2025-06-11 08:56:58 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AFRM
2025-06-11 08:56:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR?apikey=********************************
2025-06-11 08:56:58 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for AUR
2025-06-11 08:56:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG?apikey=********************************
2025-06-11 08:56:59 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for CMG
2025-06-11 08:56:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CMG,AFRM?apikey=********************************
2025-06-11 08:56:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,TIGR,BIL,CONL,AVDX,CMG,KGC,CRWV,SWTX,JBLU,B,YMM,AMCR,VALE,AFRM,PINS,OUST,GTLB,BAC,SGOV,EMLC?apikey=********************************
2025-06-11 08:57:02 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:02 | INFO     | MassiveScan | info:70 | Market order placed: AFRM buy 3.0
2025-06-11 08:57:02 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 08:57:02 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AFRM | BUY 3.0 @ $63.8800 | PnL: -$1.65 | Reason: Stop loss triggered
2025-06-11 08:57:02 | INFO     | MassiveScan | info:70 | Trade closed: b77cf085-985e-45e9-9f40-3274dede9ca5 - AFRM - P&L: $-1.65
2025-06-11 08:57:02 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:04 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:04 | INFO     | MassiveScan | info:70 | Market order placed: AFRM buy 3.0
2025-06-11 08:57:04 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 08:57:04 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AFRM | BUY 3.0 @ $63.8800 | PnL: -$1.65 | Reason: Stop loss triggered
2025-06-11 08:57:04 | INFO     | MassiveScan | info:70 | Trade closed: b77cf085-985e-45e9-9f40-3274dede9ca5 - AFRM - P&L: $-1.65
2025-06-11 08:57:04 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:05 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:05 | INFO     | MassiveScan | info:70 | Market order placed: BAC sell 4
2025-06-11 08:57:05 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:57:05 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:05 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | BAC | SELL 4 @ $45.3999 | PnL: +$1.19 | Reason: Profit target reached
2025-06-11 08:57:05 | INFO     | MassiveScan | info:70 | Trade closed: e038bceb-7257-4c4d-8dc9-11f38d28ba6e - BAC - P&L: $1.19
2025-06-11 08:57:05 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:08 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:08 | INFO     | MassiveScan | info:70 | Market order placed: CMG buy 3.0
2025-06-11 08:57:08 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 08:57:08 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CMG | BUY 3.0 @ $51.7050 | PnL: -$0.59 | Reason: Stop loss triggered
2025-06-11 08:57:08 | INFO     | MassiveScan | info:70 | Trade closed: a11bcda1-13ad-44f0-9c62-6e062e1650d7 - CMG - P&L: $-0.59
2025-06-11 08:57:08 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:09 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:09 | INFO     | MassiveScan | info:70 | Market order placed: CMG buy 3.0
2025-06-11 08:57:09 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 08:57:09 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CMG | BUY 3.0 @ $51.7100 | PnL: -$0.60 | Reason: Stop loss triggered
2025-06-11 08:57:09 | INFO     | MassiveScan | info:70 | Trade closed: a11bcda1-13ad-44f0-9c62-6e062e1650d7 - CMG - P&L: $-0.60
2025-06-11 08:57:09 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:11 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:11 | INFO     | MassiveScan | info:70 | Market order placed: KGC sell 13
2025-06-11 08:57:11 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:57:11 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:11 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | KGC | SELL 13 @ $14.9350 | PnL: -$0.71 | Reason: Stop loss triggered
2025-06-11 08:57:11 | INFO     | MassiveScan | info:70 | Trade closed: b29305f4-83b4-46cb-bbaf-23af49b347f3 - KGC - P&L: $-0.71
2025-06-11 08:57:11 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:19 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:19 | INFO     | MassiveScan | info:70 | Market order placed: AFRM buy 3
2025-06-11 08:57:19 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:19 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AFRM | BUY 3 @ $63.8300 | PnL: -$1.50 | Reason: Stop loss triggered
2025-06-11 08:57:19 | INFO     | MassiveScan | info:70 | Trade closed: b77cf085-985e-45e9-9f40-3274dede9ca5 - AFRM - P&L: $-1.50
2025-06-11 08:57:19 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:26 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:26 | INFO     | MassiveScan | info:70 | Market order placed: CMG buy 3
2025-06-11 08:57:26 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 3 consecutive losses
2025-06-11 08:57:26 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:26 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CMG | BUY 3 @ $51.7100 | PnL: -$0.60 | Reason: Stop loss triggered
2025-06-11 08:57:26 | INFO     | MassiveScan | info:70 | Trade closed: a11bcda1-13ad-44f0-9c62-6e062e1650d7 - CMG - P&L: $-0.60
2025-06-11 08:57:26 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:29 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:57:29 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:57:29 | INFO     | MassiveScan | info:70 | Stock screener returned 744 stocks
2025-06-11 08:57:29 | INFO     | MassiveScan | info:70 | Added 744 stocks from screener
2025-06-11 08:57:29 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:57:29 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:57:29 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:57:29 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:57:29 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:57:29 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:57:29 | INFO     | MassiveScan | info:70 | Scannable universe: 843 symbols
2025-06-11 08:57:30 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Buying Power: $290,878.24
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,924.25
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.73, Trades: 7, Open: 0
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Stock screener returned 744 stocks
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Added 744 stocks from screener
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:57:32 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:57:32 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:57:32 | INFO     | MassiveScan | info:70 | Scannable universe: 843 symbols
2025-06-11 08:57:32 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:57:32 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:57:32 | INFO     | MassiveScan | info:70 | Stock screener returned 744 stocks
2025-06-11 08:57:32 | INFO     | MassiveScan | info:70 | Added 744 stocks from screener
2025-06-11 08:57:33 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:57:33 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:57:33 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:57:33 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:57:33 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:57:33 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:57:33 | INFO     | MassiveScan | info:70 | Scannable universe: 843 symbols
2025-06-11 08:57:34 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CORZ | vwap_bounce | Confidence: 80.00% | entry_price: 12.58 | target_price: 12.57 | expected_profit: 1.0 | risk_reward: 45.850652375096075
2025-06-11 08:57:34 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:57:35 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:35 | INFO     | MassiveScan | info:70 | Market order placed: CONL sell 7
2025-06-11 08:57:35 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:57:35 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 4 consecutive losses
2025-06-11 08:57:35 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:35 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CONL | SELL 7 @ $25.9836 | PnL: -$1.20 | Reason: Stop loss triggered
2025-06-11 08:57:35 | INFO     | MassiveScan | info:70 | Trade closed: 1989a6d2-f91d-435f-954b-6ee13ed766f9 - CONL - P&L: $-1.20
2025-06-11 08:57:35 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:57:36 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:57:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GTLB | momentum_breakout | Confidence: 92.62% | entry_price: 43.32 | target_price: 43.31 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:57:36 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:57:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GTLB | volume_surge | Confidence: 78.81% | entry_price: 43.32 | target_price: 43.31 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:57:36 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:57:37 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:57:37 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:57:45 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:45 | INFO     | MassiveScan | info:70 | Market order placed: GTLB sell 4
2025-06-11 08:57:45 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:57:45 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:45 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | GTLB | SELL 4 @ $43.5700 | PnL: +$2.78 | Reason: Profit target reached
2025-06-11 08:57:45 | INFO     | MassiveScan | info:70 | Trade closed: e06007b7-d2aa-42cb-9749-9577838c2dce - GTLB - P&L: $2.78
2025-06-11 08:57:45 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:52 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.8793 | target_price: 12.8893 | expected_profit: 1.0 | risk_reward: 26.188186133005097
2025-06-11 08:57:52 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:57:54 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:57:57 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:57:58 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KGC | vwap_bounce | Confidence: 80.00% | entry_price: 14.935 | target_price: 14.925 | expected_profit: 1.0 | risk_reward: 77.7897219953648
2025-06-11 08:57:58 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:58:00 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | UBER | vwap_bounce | Confidence: 80.00% | entry_price: 87.08 | target_price: 87.09 | expected_profit: 1.0 | risk_reward: 2.034703809653028
2025-06-11 08:58:00 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PLAN?apikey=********************************
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/AES?apikey=********************************
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/SJR-B.TO?apikey=********************************
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for PLAN
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/YMM?apikey=********************************
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/APLS?apikey=********************************
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JNK?apikey=********************************
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for APLS
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for JNK
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ISEE?apikey=********************************
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VXRT?apikey=********************************
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/UEC?apikey=********************************
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GRCL?apikey=********************************
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RKLB?apikey=********************************
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SMCI?apikey=********************************
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VRT?apikey=********************************
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for ISEE
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for VXRT
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for UEC
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for GRCL
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for RKLB
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for SMCI
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for VRT
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SAN?apikey=********************************
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/GFLU?apikey=********************************
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for SAN
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HST?apikey=********************************
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for HST
2025-06-11 08:58:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HOLI?apikey=********************************
2025-06-11 08:58:01 | WARNING  | MassiveScan | warning:78 | No quote data for HOLI
2025-06-11 08:58:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.875 | target_price: 12.885 | expected_profit: 1.0 | risk_reward: 2.1359070978003247
2025-06-11 08:58:03 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:58:03 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:58:04 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:58:04 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:58:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LYFT | vwap_bounce | Confidence: 80.00% | entry_price: 15.6762 | target_price: 15.6862 | expected_profit: 1.0 | risk_reward: 5.858067232158642
2025-06-11 08:58:05 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:58:06 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:58:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MRVL | vwap_bounce | Confidence: 80.00% | entry_price: 69.51 | target_price: 69.5 | expected_profit: 1.0 | risk_reward: 1.822911045417119
2025-06-11 08:58:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:58:07 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:58:07 | INFO     | MassiveScan | info:70 | Market order placed: OUST sell 10
2025-06-11 08:58:07 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:58:07 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:58:07 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | OUST | SELL 10 @ $18.6950 | PnL: -$1.10 | Reason: Stop loss triggered
2025-06-11 08:58:07 | INFO     | MassiveScan | info:70 | Trade closed: 28b7405f-dad2-4828-a8e5-33f576b47494 - OUST - P&L: $-1.10
2025-06-11 08:58:07 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:58:10 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:58:10 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:58:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:58:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:58:11 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:58:16 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:58:18 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:58:19 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:58:19 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:58:19 | INFO     | MassiveScan | info:70 | Market order placed: OUST sell 10
2025-06-11 08:58:19 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:58:19 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:58:19 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:58:19 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | OUST | SELL 10 @ $18.7100 | PnL: -$0.95 | Reason: Stop loss triggered
2025-06-11 08:58:19 | INFO     | MassiveScan | info:70 | Trade closed: 255b5f1d-2846-419e-8e1b-2ba734381eb1 - OUST - P&L: $-0.95
2025-06-11 08:58:19 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:58:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:58:20 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:58:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CORZ | vwap_bounce | Confidence: 80.00% | entry_price: 12.58 | target_price: 12.57 | expected_profit: 1.0 | risk_reward: 45.30035471657295
2025-06-11 08:58:22 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:58:25 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:58:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:58:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:58:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:58:28 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:58:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AAL?apikey=********************************
2025-06-11 08:58:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/CTLT?apikey=********************************
2025-06-11 08:58:28 | WARNING  | MassiveScan | warning:78 | No quote data for AAL
2025-06-11 08:58:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/SRCL?apikey=********************************
2025-06-11 08:58:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/FSLR?apikey=********************************
2025-06-11 08:58:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/BBY?apikey=********************************
2025-06-11 08:58:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/RBRK?apikey=********************************
2025-06-11 08:58:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AQNU?apikey=********************************
2025-06-11 08:58:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/EWZ?apikey=********************************
2025-06-11 08:58:29 | WARNING  | MassiveScan | warning:78 | No quote data for AQNU
2025-06-11 08:58:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CHS?apikey=********************************
2025-06-11 08:58:29 | WARNING  | MassiveScan | warning:78 | No quote data for EWZ
2025-06-11 08:58:29 | WARNING  | MassiveScan | warning:78 | No quote data for CHS
2025-06-11 08:58:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR,JBLU?apikey=********************************
2025-06-11 08:58:30 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:58:30 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:58:30 | INFO     | MassiveScan | info:70 | Scan completed: 10 signals found in 61.8s
2025-06-11 08:58:31 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:58:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:58:31 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:58:31 | INFO     | MassiveScan | info:70 | Scan completed: 6 signals found in 58.8s
2025-06-11 08:58:31 | INFO     | MassiveScan | info:70 | Position size calculated: 15 shares for CORZ (risk per share: $0.0002)
2025-06-11 08:58:32 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:58:32 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:58:33 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:58:33 | INFO     | MassiveScan | info:70 | Market order placed: GTLB buy 4
2025-06-11 08:58:33 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:58:33 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | GTLB | BUY 4 @ $43.3200 | Strategy: momentum_breakout
2025-06-11 08:58:33 | INFO     | MassiveScan | info:70 | Trade executed: 67f1a3ce-1f83-4005-848d-6c00f6b5e1a1 - GTLB buy 4
2025-06-11 08:58:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 08:58:33 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for GTLB
2025-06-11 08:58:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 08:58:34 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR,JBLU?apikey=********************************
2025-06-11 08:58:36 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,PINS,B,KGC,CRWV,SWTX,YMM,AMCR,TIGR,BIL,VALE,AVDX,SGOV,JBLU,EMLC?apikey=********************************
2025-06-11 08:58:38 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 08:58:39 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR,JBLU?apikey=********************************
2025-06-11 08:58:41 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,PINS,B,KGC,CRWV,SWTX,YMM,AMCR,TIGR,BIL,VALE,AVDX,SGOV,JBLU,EMLC?apikey=********************************
2025-06-11 08:58:43 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 08:58:44 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR,JBLU?apikey=********************************
2025-06-11 08:58:46 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,PINS,B,KGC,CRWV,SWTX,YMM,AMCR,TIGR,BIL,VALE,AVDX,SGOV,JBLU,EMLC?apikey=********************************
2025-06-11 08:58:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 08:58:49 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR,JBLU?apikey=********************************
2025-06-11 08:58:51 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,PINS,B,KGC,CRWV,SWTX,YMM,AMCR,TIGR,BIL,VALE,AVDX,SGOV,JBLU,EMLC?apikey=********************************
2025-06-11 08:58:53 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 08:58:55 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR,JBLU?apikey=********************************
2025-06-11 08:58:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,PINS,B,KGC,CRWV,SWTX,YMM,AMCR,TIGR,BIL,VALE,AVDX,SGOV,JBLU,EMLC?apikey=********************************
2025-06-11 08:58:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 08:59:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/AUR,JBLU?apikey=********************************
2025-06-11 08:59:03 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:59:03 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:59:03 | INFO     | MassiveScan | info:70 | Stock screener returned 759 stocks
2025-06-11 08:59:03 | INFO     | MassiveScan | info:70 | Added 759 stocks from screener
2025-06-11 08:59:03 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:59:03 | INFO     | MassiveScan | info:70 | Market order placed: VALE sell 20
2025-06-11 08:59:03 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:59:03 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:59:03 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | VALE | SELL 20 @ $9.6311 | PnL: +$1.12 | Reason: Profit target reached
2025-06-11 08:59:03 | INFO     | MassiveScan | info:70 | Trade closed: 44e58658-5381-43ce-9526-711c964f50cb - VALE - P&L: $1.12
2025-06-11 08:59:03 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:59:04 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:59:04 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:59:07 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:59:07 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:59:07 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:59:07 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:59:07 | INFO     | MassiveScan | info:70 | Scannable universe: 858 symbols
2025-06-11 08:59:13 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:59:13 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:59:13 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:59:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GLDM | vwap_bounce | Confidence: 80.00% | entry_price: 65.955 | target_price: 65.945 | expected_profit: 1.0 | risk_reward: 2.8426218852745464
2025-06-11 08:59:16 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:59:29 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.885 | target_price: 12.895 | expected_profit: 1.0 | risk_reward: 1.879002710845123
2025-06-11 08:59:29 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:59:31 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:59:38 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:59:38 | INFO     | MassiveScan | info:70 | Market order placed: AUR sell 33
2025-06-11 08:59:38 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:59:38 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 3 consecutive losses
2025-06-11 08:59:38 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 08:59:38 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AUR | SELL 33 @ $5.9150 | PnL: -$0.66 | Reason: Stop loss triggered
2025-06-11 08:59:38 | INFO     | MassiveScan | info:70 | Trade closed: 7d54fcc4-9e4a-422c-9db8-1dd88ca5c994 - AUR - P&L: $-0.66
2025-06-11 08:59:38 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:59:39 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:59:40 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:59:43 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LYFT | vwap_bounce | Confidence: 80.00% | entry_price: 15.685 | target_price: 15.675 | expected_profit: 1.0 | risk_reward: 20.080686418388996
2025-06-11 08:59:43 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:59:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:59:47 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:59:47 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:59:47 | INFO     | MassiveScan | info:70 | Market order placed: CRWV sell 1
2025-06-11 08:59:47 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:59:47 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:59:47 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CRWV | SELL 1 @ $155.0446 | PnL: -$0.57 | Reason: Stop loss triggered
2025-06-11 08:59:47 | INFO     | MassiveScan | info:70 | Trade closed: d7a8860f-d42c-4c52-a2df-11f7ce496a41 - CRWV - P&L: $-0.57
2025-06-11 08:59:47 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:59:49 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:59:49 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WINT?apikey=********************************
2025-06-11 08:59:49 | WARNING  | MassiveScan | warning:78 | No quote data for WINT
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/TGH?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/DCP?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CNQ.TO?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CMLT?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/LI?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/PEP?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/HMY?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AA?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/FUSN?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/KHC?apikey=********************************
2025-06-11 08:59:50 | WARNING  | MassiveScan | warning:78 | No quote data for CNQ.TO
2025-06-11 08:59:50 | WARNING  | MassiveScan | warning:78 | No quote data for CMLT
2025-06-11 08:59:50 | WARNING  | MassiveScan | warning:78 | No quote data for AA
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NLOK?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CBAH?apikey=********************************
2025-06-11 08:59:50 | WARNING  | MassiveScan | warning:78 | No quote data for NLOK
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GRALV?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LDTC?apikey=********************************
2025-06-11 08:59:50 | WARNING  | MassiveScan | warning:78 | No quote data for CBAH
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SKX?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PEAK?apikey=********************************
2025-06-11 08:59:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OKLO?apikey=********************************
2025-06-11 08:59:50 | WARNING  | MassiveScan | warning:78 | No quote data for GRALV
2025-06-11 08:59:50 | WARNING  | MassiveScan | warning:78 | No quote data for LDTC
2025-06-11 08:59:50 | WARNING  | MassiveScan | warning:78 | No quote data for SKX
2025-06-11 08:59:50 | WARNING  | MassiveScan | warning:78 | No quote data for PEAK
2025-06-11 08:59:50 | WARNING  | MassiveScan | warning:78 | No quote data for OKLO
2025-06-11 08:59:51 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:59:51 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:59:51 | INFO     | MassiveScan | info:70 | Scan completed: 5 signals found in 48.3s
2025-06-11 08:59:51 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:59:52 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:59:52 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,PINS,B,KGC,SWTX,YMM,AMCR,TIGR,BIL,AVDX,SGOV,JBLU,EMLC?apikey=********************************
2025-06-11 08:59:53 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:59:53 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 08:59:53 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 08:59:53 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 08:59:53 | WARNING  | MassiveScan | warning:78 | Could not get real-time price for GTLB
2025-06-11 08:59:53 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:59:53 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 08:59:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,PINS,B,KGC,SWTX,YMM,AMCR,TIGR,BIL,AVDX,SGOV,JBLU,EMLC?apikey=********************************
2025-06-11 08:59:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 08:59:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 09:00:03 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CNH,PINS,B,KGC,SWTX,YMM,AMCR,TIGR,BIL,AVDX,SGOV,JBLU,EMLC?apikey=********************************
2025-06-11 09:00:03 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/JBLU?apikey=********************************
2025-06-11 09:00:03 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/GTLB?apikey=********************************
2025-06-11 09:00:09 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:00:09 | INFO     | MassiveScan | info:70 | Market order placed: GTLB sell 4
2025-06-11 09:00:09 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:00:09 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 09:00:09 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | GTLB | SELL 4 @ $44.0050 | PnL: +$2.74 | Reason: Profit target reached
2025-06-11 09:00:09 | INFO     | MassiveScan | info:70 | Trade closed: 67f1a3ce-1f83-4005-848d-6c00f6b5e1a1 - GTLB - P&L: $2.74
2025-06-11 09:00:09 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:00:23 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 09:00:23 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 09:00:23 | INFO     | MassiveScan | info:70 | Stock screener returned 768 stocks
2025-06-11 09:00:23 | INFO     | MassiveScan | info:70 | Added 768 stocks from screener
2025-06-11 09:00:23 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 09:00:23 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 09:00:24 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 09:00:24 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 09:00:24 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 09:00:24 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 09:00:24 | INFO     | MassiveScan | info:70 | Scannable universe: 867 symbols
2025-06-11 09:00:32 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 09:00:32 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 09:00:32 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 09:00:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.88 | target_price: 12.89 | expected_profit: 1.0 | risk_reward: 31.308781168616708
2025-06-11 09:00:47 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 09:00:47 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:00:47 | INFO     | MassiveScan | info:70 | Market order placed: TIGR sell 23
2025-06-11 09:00:47 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:00:47 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 09:00:47 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | TIGR | SELL 23 @ $8.6550 | PnL: +$1.15 | Reason: Profit target reached
2025-06-11 09:00:47 | INFO     | MassiveScan | info:70 | Trade closed: f9a5d100-3b1a-480d-9ea8-32ca613f8c4f - TIGR - P&L: $1.15
2025-06-11 09:00:47 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:00:49 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 09:00:49 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ITUB | vwap_bounce | Confidence: 80.00% | entry_price: 6.5538 | target_price: 6.5638 | expected_profit: 1.0 | risk_reward: 1.7641658449506623
2025-06-11 09:00:49 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 09:00:57 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 09:00:58 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 09:01:08 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 09:01:08 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 09:01:09 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 09:01:15 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 09:01:16 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 09:01:18 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 09:01:18 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 09:01:23 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 09:01:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:01:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 09:01:27 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 2 | PnL: $-2.89 | Win Rate: 0.0% | Avg Profit: $-1.45
2025-06-11 09:01:27 | INFO     | MassiveScan | info:70 | Status Report:
2025-06-11 09:01:27 | INFO     | MassiveScan | info:70 |   Runtime: 0.1 hours
2025-06-11 09:01:27 | INFO     | MassiveScan | info:70 |   Signals processed: 2
2025-06-11 09:01:27 | INFO     | MassiveScan | info:70 |   Trades executed: 2
2025-06-11 09:01:27 | INFO     | MassiveScan | info:70 |   Active trades: 1
2025-06-11 09:01:27 | INFO     | MassiveScan | info:70 |   Daily P&L: $-2.89
2025-06-11 09:01:27 | INFO     | MassiveScan | info:70 |   Win rate: 0.0%
2025-06-11 09:01:27 | INFO     | MassiveScan | info:70 |   Scans completed: 1
2025-06-11 09:01:27 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-1.45
2025-06-11 09:01:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EWZ | rsi_overbought | Confidence: 59.56% | entry_price: 28.0401 | target_price: 28.030099999999997 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 09:01:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CLSK | vwap_bounce | Confidence: 80.00% | entry_price: 9.995 | target_price: 10.004999999999999 | expected_profit: 1.0 | risk_reward: 2.158084105978741
2025-06-11 09:01:35 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 09:01:42 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:01:42 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 09:01:52 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 51 | PnL: $-4.48 | Win Rate: 35.5% | Avg Profit: $-0.09
2025-06-11 09:01:52 | INFO     | MassiveScan | info:70 | Status Report:
2025-06-11 09:01:52 | INFO     | MassiveScan | info:70 |   Runtime: 0.4 hours
2025-06-11 09:01:52 | INFO     | MassiveScan | info:70 |   Signals processed: 94
2025-06-11 09:01:52 | INFO     | MassiveScan | info:70 |   Trades executed: 51
2025-06-11 09:01:52 | INFO     | MassiveScan | info:70 |   Active trades: 20
2025-06-11 09:01:52 | INFO     | MassiveScan | info:70 |   Daily P&L: $-4.48
2025-06-11 09:01:52 | INFO     | MassiveScan | info:70 |   Win rate: 35.5%
2025-06-11 09:01:52 | INFO     | MassiveScan | info:70 |   Scans completed: 10
2025-06-11 09:01:52 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-0.09
2025-06-11 09:01:52 | WARNING  | MassiveScan | warning:78 | Low win rate: 35.5%
2025-06-11 09:01:54 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 09:01:54 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 09:01:54 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 09:01:54 | INFO     | MassiveScan | info:70 | Stock screener returned 785 stocks
2025-06-11 09:01:54 | INFO     | MassiveScan | info:70 | Added 785 stocks from screener
2025-06-11 09:01:54 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 09:01:54 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 09:01:55 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 09:01:55 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 09:01:55 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 09:01:55 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 09:01:55 | INFO     | MassiveScan | info:70 | Scannable universe: 885 symbols
2025-06-11 09:01:55 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 09:01:55 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 09:01:55 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 09:01:55 | INFO     | MassiveScan | info:70 | Stock screener returned 785 stocks
2025-06-11 09:01:55 | INFO     | MassiveScan | info:70 | Added 785 stocks from screener
2025-06-11 09:01:56 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 09:01:56 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 09:01:56 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 09:01:56 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 09:01:56 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 09:01:56 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 09:01:56 | INFO     | MassiveScan | info:70 | Scannable universe: 885 symbols
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/BPMC?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/TEM?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/BPMC?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/EBAY?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/FNA?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WRK?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/LHCG?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/DELL?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AS?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/VG?apikey=********************************
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for WRK
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for AS
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/IBDP?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IBIT?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AGG?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/YINN?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/KDNY?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CPRT?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/ARM?apikey=********************************
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for IBIT
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for AGG
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for CPRT
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FNGD?apikey=********************************
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for FNGD
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/COP?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KHC?apikey=********************************
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for KHC
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AIMC?apikey=********************************
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HP?apikey=********************************
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for HP
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for COP
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BAFN?apikey=********************************
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for AIMC
2025-06-11 09:01:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SWTX?apikey=********************************
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for BAFN
2025-06-11 09:01:57 | WARNING  | MassiveScan | warning:78 | No quote data for SWTX
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RELY?apikey=********************************
2025-06-11 09:01:58 | WARNING  | MassiveScan | warning:78 | No quote data for RELY
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HEES?apikey=********************************
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SGOV?apikey=********************************
2025-06-11 09:01:58 | WARNING  | MassiveScan | warning:78 | No quote data for HEES
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OAS?apikey=********************************
2025-06-11 09:01:58 | WARNING  | MassiveScan | warning:78 | No quote data for SGOV
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BDXA?apikey=********************************
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LTHM?apikey=********************************
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JPST?apikey=********************************
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TSLQ?apikey=********************************
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/STLA?apikey=********************************
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OPEN?apikey=********************************
2025-06-11 09:01:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FLMN?apikey=********************************
2025-06-11 09:01:58 | WARNING  | MassiveScan | warning:78 | No quote data for OAS
2025-06-11 09:01:58 | WARNING  | MassiveScan | warning:78 | No quote data for BDXA
2025-06-11 09:01:58 | WARNING  | MassiveScan | warning:78 | No quote data for LTHM
2025-06-11 09:01:58 | WARNING  | MassiveScan | warning:78 | No quote data for JPST
2025-06-11 09:01:58 | WARNING  | MassiveScan | warning:78 | No quote data for TSLQ
2025-06-11 09:01:58 | WARNING  | MassiveScan | warning:78 | No quote data for STLA
2025-06-11 09:01:58 | WARNING  | MassiveScan | warning:78 | No quote data for OPEN
2025-06-11 09:01:58 | WARNING  | MassiveScan | warning:78 | No quote data for FLMN
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CGNT?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CX?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GFLU?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for CGNT
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for CX
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IREN?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BIDU?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RXDX?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MOS?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for GFLU
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ONEM?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FUSE?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/UNVR?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for IREN
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for BIDU
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for RXDX
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for MOS
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for ONEM
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for FUSE
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for UNVR
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FSM?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for FSM
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/STAR?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for STAR
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BULLZ?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RKLB?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IINN?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MODV?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BCAC?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XLC?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for BULLZ
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AKAM?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SBET?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for RKLB
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for IINN
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for MODV
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for BCAC
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for XLC
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for AKAM
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for SBET
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ISBC?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for ISBC
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SGH?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SPY?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for SGH
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for SPY
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AIHS?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/COUP?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for AIHS
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for COUP
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ORAN?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NEM?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CCI?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for ORAN
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SJR?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for NEM
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for CCI
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HOOD?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for SJR
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for HOOD
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RGTX?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for RGTX
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TGT?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for TGT
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BILI?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PINS?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for PINS
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HZNP?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for BILI
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PDD?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for HZNP
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SOUN?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DCP?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AAWW?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GAP?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for PDD
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for SOUN
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for DCP
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for AAWW
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for GAP
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GWAC?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for GWAC
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MMP?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TSLY?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DICE?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ICPT?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SGOV?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FLTW?apikey=********************************
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CMRX?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for MMP
2025-06-11 09:01:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AG?apikey=********************************
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for TSLY
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for DICE
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for ICPT
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for SGOV
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for FLTW
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for CMRX
2025-06-11 09:01:59 | WARNING  | MassiveScan | warning:78 | No quote data for AG
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ALBO?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for ALBO
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KR?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VWO?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RONI?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VTAK?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for KR
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WFC?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SPFR?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for SPFR
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KSS?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AZN?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for VWO
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for RONI
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for VTAK
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for WFC
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IINN?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for KSS
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for AZN
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for IINN
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DNB?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BITO?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for DNB
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for BITO
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VKTX?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DCPH?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SLV?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NTCO?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BTI?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LQD?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MUI?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for VKTX
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for DCPH
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for SLV
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for NTCO
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for BTI
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for LQD
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for MUI
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CMLT?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for CMLT
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AES?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KIM?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for AES
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SOVO?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DKNG?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CPRT?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TRTN?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BWA?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OUSTZ?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for KIM
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for SOVO
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for DKNG
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for CPRT
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for TRTN
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for BWA
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for OUSTZ
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SOXS?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for SOXS
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AYA.TO?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for AYA.TO
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DEN?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ML?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for ML
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for DEN
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DCT?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for DCT
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DDOG?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SWM?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MFH?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for DDOG
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DNAA?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for SWM
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PYCR?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DOOR?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for MFH
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for DNAA
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for PYCR
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for DOOR
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ITUB?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for ITUB
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MO?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PLTR?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HERA?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ETRN?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MYOV?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AMK?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TAST?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BSCL?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/QQQI?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for MO
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for PLTR
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for HERA
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for ETRN
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for MYOV
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for AMK
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for TAST
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for BSCL
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for QQQI
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RF?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for RF
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VXRT?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/W?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TLT?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JBLU?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for VXRT
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MLEC?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/UCBI?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ASTS?apikey=********************************
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FOCS?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for FOCS
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for TLT
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for JBLU
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for MLEC
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for UCBI
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for ASTS
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for W
2025-06-11 09:02:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BERY?apikey=********************************
2025-06-11 09:02:00 | WARNING  | MassiveScan | warning:78 | No quote data for BERY
2025-06-11 09:02:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PRVB?apikey=********************************
2025-06-11 09:02:01 | WARNING  | MassiveScan | warning:78 | No quote data for PRVB
2025-06-11 09:02:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 09:02:05 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 09:02:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.775 | target_price: 46.785 | expected_profit: 1.0 | risk_reward: 41.38161823561366
2025-06-11 09:02:05 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 09:02:09 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EWZ | rsi_overbought | Confidence: 59.56% | entry_price: 28.065 | target_price: 28.055 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 09:02:11 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 09:02:12 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 09:02:15 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | Stopping MassiveScan Trading Bot...
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | Trade monitoring stopped
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | Closing all positions: Bot shutdown
2025-06-11 09:02:20 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | Market order placed: JBLU sell 39
2025-06-11 09:02:20 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:02:20 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 4 consecutive losses
2025-06-11 09:02:20 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 09:02:20 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | JBLU | SELL 39 @ $5.0250 | PnL: -$0.21 | Reason: Bot shutdown
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | Trade closed: db64865a-6caa-47a2-87f5-b0a7869c0ebf - JBLU - P&L: $-0.21
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | All positions closed
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | Bot stopped from GUI
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IEF?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for IEF
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BRKS?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for BRKS
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SOXL?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for SOXL
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SJM?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/DEH?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for SJM
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LSI?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for LSI
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HA?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BITX?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for HA
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/THCB?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SRAC?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for BITX
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for THCB
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for SRAC
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/T?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NVAX?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for T
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for NVAX
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HEP?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for HEP
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ATH?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for ATH
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NVDA?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for NVDA
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ALCC?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VMW?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for ALCC
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for VMW
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IONQ?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for IONQ
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/AR?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RBLX?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ABB?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LCID?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BKI?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XLRE?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PSTX?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for RBLX
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PETQ?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for ABB
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for LCID
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/QSG?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for BKI
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for XLRE
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for PSTX
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for PETQ
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for QSG
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MWYN?apikey=********************************
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SISI?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for MWYN
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for SISI
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MTTR?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for MTTR
2025-06-11 09:02:21 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DELL?apikey=********************************
2025-06-11 09:02:21 | WARNING  | MassiveScan | warning:78 | No quote data for DELL
2025-06-11 09:02:22 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:22 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RGTX?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for RGTX
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BECN?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XNET?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MRK?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JBLU?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MU?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for BECN
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AMD?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WWE?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JEPI?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for MRK
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FRX?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BITO?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IBTE?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CTVA?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HCP?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for JBLU
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HIMS?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GOLD?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for MU
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BRKS?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for WWE
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BP?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for XNET
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for FRX
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for IBTE
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for AMD
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for GOLD
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for JEPI
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for BITO
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for CTVA
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for HCP
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for HIMS
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for BRKS
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for BP
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TLT?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for TLT
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ELUT?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for ELUT
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VVNT?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CLI?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for VVNT
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SRAC?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FUTU?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for SRAC
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ASHR?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for CLI
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GDS?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SERV?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ORAN?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SCWX?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/QBTS?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ETHA?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for FUTU
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for ASHR
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for GDS
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for SERV
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for IEFA
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GDX?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for SCWX
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for QBTS
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for ETHA
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ARKK?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for ORAN
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AR?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/APH?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/COTY?apikey=********************************
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IAU?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for GDX
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for ARKK
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for AR
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for APH
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for COTY
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for IAU
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MNTN?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for MNTN
2025-06-11 09:02:23 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VALE?apikey=********************************
2025-06-11 09:02:23 | WARNING  | MassiveScan | warning:78 | No quote data for VALE
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DHI?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for DHI
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SONY?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for SONY
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XLU?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GRNV?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for XLU
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/INFY?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for GRNV
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for INFY
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SCHW?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for SCHW
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CHK?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HP?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for CHK
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for HP
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BHP?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AEG?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SST?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/USAR?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GEAR?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for BHP
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for AEG
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NOK?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ALTR?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for SST
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/UBER?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VIVO?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for USAR
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for GEAR
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for NOK
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for ALTR
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for UBER
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for VIVO
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AEL?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PONY?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for AEL
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for PONY
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DKNG?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for DKNG
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CCJ?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VSAT?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for CCJ
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GME?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for VSAT
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GTI?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CVE.TO?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for GME
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for GTI
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for CVE.TO
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CBTX?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TQQQ?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for CBTX
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for TQQQ
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LUV?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for LUV
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CVX?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XYZ?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CHWY?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NEP?apikey=********************************
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HPQ?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for CVX
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for XYZ
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TCN?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for CHWY
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for NEP
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ARM?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for HPQ
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for TCN
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RFP?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for ARM
2025-06-11 09:02:24 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ICPT?apikey=********************************
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for RFP
2025-06-11 09:02:24 | WARNING  | MassiveScan | warning:78 | No quote data for ICPT
2025-06-11 09:02:25 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/UNVR?apikey=********************************
2025-06-11 09:02:25 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KAMN?apikey=********************************
2025-06-11 09:02:25 | WARNING  | MassiveScan | warning:78 | No quote data for UNVR
2025-06-11 09:02:25 | WARNING  | MassiveScan | warning:78 | No quote data for KAMN
2025-06-11 09:02:25 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DD?apikey=********************************
2025-06-11 09:02:25 | WARNING  | MassiveScan | warning:78 | No quote data for DD
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GHVI?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FBHS?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BIRD?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PRVB?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GOVT?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/UBA?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KWEB?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BTI?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AS?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for GHVI
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ICPT?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for FBHS
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for BIRD
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for PRVB
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for GOVT
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for UBA
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for KWEB
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for BTI
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for AS
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for ICPT
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/EVGN?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WCP.TO?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CHK?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AGG?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for EVGN
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NTCL?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RNW.TO?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PFAI?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LI?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AKAM?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for WCP.TO
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SAIL?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for CHK
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for AGG
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for NTCL
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for RNW.TO
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for PFAI
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for LI
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for AKAM
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for SAIL
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/APEN?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DMYI?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CPG.TO?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for APEN
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CLF?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for DMYI
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DICE?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/YMM?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ON?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GPS?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for CPG.TO
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AMSC?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for CLF
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/EWG?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for DICE
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for YMM
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for ON
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for GPS
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for AMSC
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for EWG
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ARNC?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/YRI.TO?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PKI?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for ARNC
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NUVA?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for YRI.TO
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DNAA?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for PKI
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IAA?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for NUVA
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SKX?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NVAX?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for DNAA
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for IAA
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for SKX
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for NVAX
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OMH?apikey=********************************
2025-06-11 09:02:26 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BP?apikey=********************************
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for OMH
2025-06-11 09:02:26 | WARNING  | MassiveScan | warning:78 | No quote data for BP
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NVO?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for NVO
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FBMS?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FRX?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for FBMS
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CVE?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PDD?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ACHR?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LWAC?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AFRM?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for FRX
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for CVE
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for PDD
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for ACHR
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for LWAC
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for AFRM
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PBR?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TDOC?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for PBR
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for TDOC
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DESP?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SBET?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for DESP
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BMR?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MLEC?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for SBET
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PFC?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for BMR
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for MLEC
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for PFC
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AQUA?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for AQUA
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RDFN?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GSAH?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for RDFN
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for GSAH
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SOXS?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for SOXS
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AUR?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HARP?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SILJ?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for AUR
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NID?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for HARP
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ZZZ.TO?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CABA?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ARAI?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RFL?apikey=********************************
2025-06-11 09:02:27 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NBIS?apikey=********************************
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for SILJ
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for NID
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for ZZZ.TO
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for CABA
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for ARAI
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for RFL
2025-06-11 09:02:27 | WARNING  | MassiveScan | warning:78 | No quote data for NBIS
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AMCR?apikey=********************************
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for AMCR
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BBY?apikey=********************************
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for BBY
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HSAI?apikey=********************************
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for HSAI
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SOFI?apikey=********************************
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for SOFI
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/QUBT?apikey=********************************
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SCWX?apikey=********************************
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for QUBT
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GRAB?apikey=********************************
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for SCWX
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AIHS?apikey=********************************
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/USB?apikey=********************************
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for GRAB
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LIMN?apikey=********************************
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for AIHS
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for USB
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AMAM?apikey=********************************
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for LIMN
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for AMAM
2025-06-11 09:02:28 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SNAP?apikey=********************************
2025-06-11 09:02:28 | WARNING  | MassiveScan | warning:78 | No quote data for SNAP
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KEY.TO?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VSCO?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RGTI?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ITB?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RTP?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HFC?apikey=********************************
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for KEY.TO
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for VSCO
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for RGTI
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for ITB
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for RTP
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for HFC
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NVTS?apikey=********************************
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for NVTS
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JOBY?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GME?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NYCB?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IBIT?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SWI?apikey=********************************
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for JOBY
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/INTR?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AIRC?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FUSN?apikey=********************************
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for GME
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for NYCB
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for IBIT
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for SWI
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for INTR
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for AIRC
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for FUSN
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XLC?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RLGY?apikey=********************************
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for XLC
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DJT?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CNET?apikey=********************************
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CNH?apikey=********************************
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for RLGY
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for DJT
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GLDM?apikey=********************************
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for CNET
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for CNH
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for GLDM
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AGR?apikey=********************************
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for AGR
2025-06-11 09:02:29 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ATSG?apikey=********************************
2025-06-11 09:02:29 | WARNING  | MassiveScan | warning:78 | No quote data for ATSG
2025-06-11 09:02:30 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SRCL?apikey=********************************
2025-06-11 09:02:30 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/APXT?apikey=********************************
2025-06-11 09:02:30 | WARNING  | MassiveScan | warning:78 | No quote data for APXT
2025-06-11 09:02:30 | WARNING  | MassiveScan | warning:78 | No quote data for SRCL
2025-06-11 09:02:30 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CELH?apikey=********************************
2025-06-11 09:02:30 | WARNING  | MassiveScan | warning:78 | No quote data for CELH
2025-06-11 09:02:30 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TMF?apikey=********************************
2025-06-11 09:02:30 | WARNING  | MassiveScan | warning:78 | No quote data for TMF
2025-06-11 09:02:30 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:30 | INFO     | MassiveScan | info:70 | Scan completed: 10 signals found in 126.7s
2025-06-11 09:02:30 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MRNA?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for MRNA
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CPRT?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for CPRT
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NVDX?apikey=********************************
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FSLR?apikey=********************************
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HEES?apikey=********************************
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ICVX?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for NVDX
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for FSLR
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for HEES
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for ICVX
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ACHC?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for ACHC
2025-06-11 09:02:31 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CSCO?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for CSCO
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SERV?apikey=********************************
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SLB?apikey=********************************
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CTLT?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for SERV
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ALTM?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for SLB
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SQQQ?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for CTLT
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for ALTM
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for SQQQ
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CPB?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for CPB
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KLTO?apikey=********************************
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NETE?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for KLTO
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for NETE
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AIRT?apikey=********************************
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CR.TO?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for AIRT
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for CR.TO
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OSH?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for OSH
2025-06-11 09:02:31 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BKI?apikey=********************************
2025-06-11 09:02:31 | WARNING  | MassiveScan | warning:78 | No quote data for BKI
2025-06-11 09:02:32 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:02:32 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 09:02:32 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 09:02:32 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RTX?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for RTX
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VOD?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for VOD
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PBR?apikey=********************************
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DDOG?apikey=********************************
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SQSP?apikey=********************************
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/THCB?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for PBR
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for DDOG
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for SQSP
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for THCB
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JWN?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for JWN
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GILD?apikey=********************************
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LWAC?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for GILD
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JUNS?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for LWAC
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LDTC?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for JUNS
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for LDTC
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ETSY?apikey=********************************
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TRTN?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for ETSY
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for TRTN
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BULL?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for BULL
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SGOV?apikey=********************************
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ESTE?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for SGOV
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JILL?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for ESTE
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for JILL
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KR?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for KR
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HYG?apikey=********************************
2025-06-11 09:02:33 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MIND?apikey=********************************
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for HYG
2025-06-11 09:02:33 | WARNING  | MassiveScan | warning:78 | No quote data for MIND
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VRN?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for VRN
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JEPI?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for JEPI
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HIMS?apikey=********************************
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GHVI?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for HIMS
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ZUO?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for GHVI
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CVE.TO?apikey=********************************
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ARCH?apikey=********************************
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SGFY?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for ZUO
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PVAC?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for CVE.TO
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for ARCH
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for SGFY
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TIGR?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for PVAC
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for TIGR
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IMGN?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for IMGN
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PWSC?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for PWSC
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HAL?apikey=********************************
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/EDR?apikey=********************************
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DSEY?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for HAL
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for EDR
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NAPA?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for DSEY
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CTIC?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for NAPA
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TSLT?apikey=********************************
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XLV?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for CTIC
2025-06-11 09:02:35 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SQ?apikey=********************************
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for TSLT
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for XLV
2025-06-11 09:02:35 | WARNING  | MassiveScan | warning:78 | No quote data for SQ
