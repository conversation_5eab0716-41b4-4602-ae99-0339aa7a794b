2025-06-11 08:22:04 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:22:05 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:22:05 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:22:05 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,926.62
2025-06-11 08:22:05 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:22:12 | INFO     | MassiveScan | info:70 | Stock screener returned 5 stocks
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,850.34
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:29:48 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:29:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | Bot started from GUI
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:29:56 | WARNING  | MassiveScan | warning:78 | Market appears to be closed
2025-06-11 08:29:56 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:29:56 | ERROR    | MassiveScan | error:82 | Failed to connect to broker
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | Cleaning up resources...
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | Final Performance Summary:
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 |   Total trades: 0
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 |   Win rate: 0.0%
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 |   Total P&L: $0.00
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 |   Average P&L: $0.00
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 | MassiveScan Trading Bot stopped
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,850.93
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:29:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:04 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:04 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:30:09 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:14 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:19 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:24 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:29 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:34 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:39 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:44 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:49 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:04 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:10 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:15 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:20 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:25 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:30 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:35 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:40 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:45 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:50 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:55 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:00 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:05 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:11 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:11 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:32:16 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Demo mode enabled - will scan even when market is closed
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Stock screener returned 483 stocks
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Added 483 stocks from screener
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:32:16 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:32:17 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:32:17 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:32:17 | INFO     | MassiveScan | info:70 | Scannable universe: 610 symbols
2025-06-11 08:32:21 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:26 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:33:50 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:33:50 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Stock screener returned 513 stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Added 513 stocks from screener
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:33:51 | INFO     | MassiveScan | info:70 | Scannable universe: 616 symbols
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Stock screener returned 516 stocks
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Added 516 stocks from screener
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:34:00 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:34:01 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:34:01 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:34:01 | INFO     | MassiveScan | info:70 | Scannable universe: 619 symbols
2025-06-11 08:34:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JOBY | rsi_overbought | Confidence: 64.96% | entry_price: 9.915 | target_price: 9.905 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:34:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:34:05 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:08 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GILD | rsi_oversold | Confidence: 59.98% | entry_price: 109.98 | target_price: 109.99000000000001 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:09 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EEM | rsi_overbought | Confidence: 50.34% | entry_price: 47.83 | target_price: 47.82 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:34:15 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SBUX | rsi_overbought | Confidence: 70.95% | entry_price: 93.4568 | target_price: 93.4468 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:17 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:34:23 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:34:24 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:24 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:31 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SLV | rsi_oversold | Confidence: 63.16% | entry_price: 32.9399 | target_price: 32.9499 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:34:31 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:34:33 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BIL | vwap_bounce | Confidence: 80.00% | entry_price: 91.5307 | target_price: 91.52069999999999 | expected_profit: 1.0 | risk_reward: 2.511842290125823
2025-06-11 08:34:33 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | momentum_breakout | Confidence: 95.00% | entry_price: 17.98 | target_price: 17.97 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:36 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | volume_surge | Confidence: 90.00% | entry_price: 17.98 | target_price: 17.97 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:36 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MP | rsi_oversold | Confidence: 78.61% | entry_price: 23.91 | target_price: 23.92 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDD | rsi_overbought | Confidence: 57.64% | entry_price: 103.92 | target_price: 103.91 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:39 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:34:39 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | FCX | rsi_oversold | Confidence: 64.42% | entry_price: 40.93 | target_price: 40.94 | expected_profit: 1.0 | risk_reward: 1.999999999998579
2025-06-11 08:34:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:40 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BDX | rsi_overbought | Confidence: 56.05% | entry_price: 175.56 | target_price: 175.55 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 08:34:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PFE | rsi_overbought | Confidence: 60.57% | entry_price: 24.635 | target_price: 24.625 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:44 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SAIL | rsi_overbought | Confidence: 82.02% | entry_price: 22.875 | target_price: 22.865 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:45 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BILI | rsi_overbought | Confidence: 79.20% | entry_price: 21.2 | target_price: 21.189999999999998 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:49 | ERROR    | MassiveScan | error:82 | Error parsing market data for OAS: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:34:51 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMSC | momentum_breakout | Confidence: 86.51% | entry_price: 28.03 | target_price: 28.02 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:51 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:51 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMSC | rsi_oversold | Confidence: 80.45% | entry_price: 28.03 | target_price: 28.040000000000003 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:52 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:34:53 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TIGR | rsi_overbought | Confidence: 74.01% | entry_price: 8.805 | target_price: 8.795 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:34:53 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:34:53 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:34:58 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CLF | rsi_oversold | Confidence: 69.93% | entry_price: 7.335 | target_price: 7.345 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 08:35:05 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:35:08 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:35:11 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:35:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:35:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:35:23 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:35:36 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:35:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CSCO | rsi_oversold | Confidence: 56.36% | entry_price: 64.29 | target_price: 64.************** | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:35:39 | INFO     | MassiveScan | info:70 | Scan completed: 25 signals found in 99.1s
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,820.02
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:36:27 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:32 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,845.90
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:36:37 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Market is open
2025-06-11 08:36:37 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:37 | ERROR    | MassiveScan | error:82 | Failed to connect to broker
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Cleaning up resources...
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Final Performance Summary:
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 |   Total trades: 0
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 |   Win rate: 0.0%
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 |   Total P&L: $0.00
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 |   Average P&L: $0.00
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | MassiveScan Trading Bot stopped
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Stopping MassiveScan Trading Bot...
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Trade monitoring stopped
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Closing all positions: Bot shutdown
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | All positions closed
2025-06-11 08:36:42 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:47 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:52 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:57 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:02 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:07 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:13 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:23 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,863.30
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:37:26 | INFO     | MassiveScan | info:70 | Market is open
2025-06-11 08:37:26 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:26 | ERROR    | MassiveScan | error:82 | Failed to connect to broker
2025-06-11 08:37:28 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:33 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:38 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:43 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:48 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:53 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:58 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:58 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:37:59 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:37:59 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:37:59 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,865.86
2025-06-11 08:38:03 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,886.68
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 | Market is open
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 | Broker connected - Buying Power: $296,867.68
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 | Daily risk statistics reset
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 | Configuration loaded:
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Target profit per trade: $1.0
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Stop loss per trade: $0.5
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Max daily trades: 100
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Max daily loss limit: $25.0
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Max concurrent positions: 10
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Scan interval: 30s
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Expected trades for $50: 50 trades
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 | MassiveScan Trading Bot initialized successfully
2025-06-11 08:38:08 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:13 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:22 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Portfolio Value: $100,923.52
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Market is open
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Broker connected - Buying Power: $296,867.68
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Daily risk statistics reset
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | TRADING STRATEGY - Target: $50 Daily Profit
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Configuration loaded:
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Target profit per trade: $1.0
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Stop loss per trade: $0.5
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Max daily trades: 100
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Max daily loss limit: $25.0
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Max concurrent positions: 10
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Scan interval: 30s
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Expected trades for $50: 50 trades
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | MassiveScan Trading Bot initialized successfully
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Starting MassiveScan Trading Bot...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Starting market scanning...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Stock screener returned 567 stocks
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Added 567 stocks from screener
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:38:24 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:24 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:38:24 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:38:24 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:38:24 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:38:24 | INFO     | MassiveScan | info:70 | Scannable universe: 668 symbols
2025-06-11 08:38:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | IREN | vwap_bounce | Confidence: 80.00% | entry_price: 10.245 | target_price: 10.*************** | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:38:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:38:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BILI | rsi_overbought | Confidence: 62.42% | entry_price: 21.17 | target_price: 21.16 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:38:29 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:32 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:38:32 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:38:34 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | HBAN | vwap_bounce | Confidence: 80.00% | entry_price: 16.285 | target_price: 16.295 | expected_profit: 1.0 | risk_reward: 4.***************
2025-06-11 08:38:35 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:38:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AAL | vwap_bounce | Confidence: 80.00% | entry_price: 11.8287 | target_price: 11.8187 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:38:35 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:38:37 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:38:38 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CLF | rsi_oversold | Confidence: 62.68% | entry_price: 7.27 | target_price: 7.*************** | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 08:38:38 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:38:39 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:40 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:38:44 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VALE | vwap_bounce | Confidence: 80.00% | entry_price: 9.565 | target_price: 9.555 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:38:47 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:38:49 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:50 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:38:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:59 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:38:59 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:39:04 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:09 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:13 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:39:13 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:39:14 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:19 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:24 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:39:24 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:39:24 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:26 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:39:29 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:35 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:38 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | XPEV | rsi_overbought | Confidence: 51.42% | entry_price: 20.955 | target_price: 20.*************** | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Connected to Alpaca - Account: PA34LCX34I7S
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Portfolio Value: $101,001.59
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:39:40 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:42 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:39:42 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:39:43 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:39:43 | INFO     | MassiveScan | info:70 | Stock screener returned 583 stocks
2025-06-11 08:39:43 | INFO     | MassiveScan | info:70 | Added 583 stocks from screener
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Scannable universe: 685 symbols
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Stock screener returned 584 stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Added 584 stocks from screener
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:39:44 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:39:45 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:39:45 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:39:45 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:45 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:39:45 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:39:45 | INFO     | MassiveScan | info:70 | Scannable universe: 686 symbols
2025-06-11 08:39:48 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:39:48 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:39:49 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BILI | rsi_overbought | Confidence: 57.71% | entry_price: 21.09 | target_price: 21.08 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:39:50 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:51 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:39:52 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:39:52 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BDXB?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NR?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for BDXB
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for NR
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/UMPQ?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OAS?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for OAS
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ZOOZ?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for ZOOZ
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/DEH?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CNHI?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OSTK?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for CNHI
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for OSTK
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AGNC?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for AGNC
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/STER?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/SNPO?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LGF-B?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for LGF-B
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BECN?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for BECN
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/SOAC?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FPAC?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/CLI?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for FPAC
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WMT?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for WMT
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/LQD?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/ENLC?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/HTLF?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IBDP?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NYCB?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JWN?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for IBDP
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ZOOZ?apikey=********************************
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LWAC?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for NYCB
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MNTV?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for JWN
2025-06-11 08:39:54 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SIL.TO?apikey=********************************
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for ZOOZ
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for LWAC
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for MNTV
2025-06-11 08:39:54 | WARNING  | MassiveScan | warning:78 | No quote data for SIL.TO
2025-06-11 08:39:55 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:55 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Scan completed: 11 signals found in 92.0s
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Found 11 trading signals
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Starting signal processing...
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Processing signal: IREN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:39:55 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Scan completed: 2 signals found in 11.3s
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for IREN (risk per share: $0.0059)
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for IREN (risk per share: $0.0059)
2025-06-11 08:39:56 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Market order placed: IREN buy 19
2025-06-11 08:39:56 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:39:56 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | IREN | BUY 19 @ $10.2450 | Strategy: vwap_bounce
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Trade executed: e1972db6-6cdf-4ea0-be9a-ffc7ce41cae2 - IREN buy 19
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Trade executed: IREN - Total trades today: 1
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Processing signal: PDCO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:39:57 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:39:58 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:39:58 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:39:58 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:39:58 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PDCO
2025-06-11 08:39:58 | INFO     | MassiveScan | info:70 | Processing signal: HBAN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:39:58 | INFO     | MassiveScan | info:70 | Position size calculated: 12 shares for HBAN (risk per share: $0.0022)
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Position size calculated: 12 shares for HBAN (risk per share: $0.0022)
2025-06-11 08:39:59 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Market order placed: HBAN buy 12
2025-06-11 08:39:59 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:39:59 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | HBAN | BUY 12 @ $16.2850 | Strategy: vwap_bounce
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Trade executed: 4c7d2f26-9579-4ec7-aff5-dc7d381b67eb - HBAN buy 12
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Trade executed: HBAN - Total trades today: 2
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Processing signal: AAL - vwap_bounce (confidence: 80.00%)
2025-06-11 08:40:00 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:00 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for AAL (risk per share: $0.0044)
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for AAL (risk per share: $0.0044)
2025-06-11 08:40:01 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Market order placed: AAL buy 16
2025-06-11 08:40:01 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:40:01 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AAL | BUY 16 @ $11.8287 | Strategy: vwap_bounce
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Trade executed: 1c724dad-5db4-4430-af23-d8b6a36d91dc - AAL buy 16
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Trade executed: AAL - Total trades today: 3
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Processing signal: VALE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for VALE (risk per share: $0.0060)
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for VALE (risk per share: $0.0060)
2025-06-11 08:40:02 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Market order placed: VALE buy 20
2025-06-11 08:40:02 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:40:02 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | VALE | BUY 20 @ $9.5650 | Strategy: vwap_bounce
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Trade executed: d8c4bdb5-1242-4014-8677-f24de9febc15 - VALE buy 20
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Trade executed: VALE - Total trades today: 4
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Processing signal: JWN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:40:03 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:40:04 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:40:04 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:40:04 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:40:04 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for JWN
2025-06-11 08:40:04 | INFO     | MassiveScan | info:70 | Processing signal: PTVE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:40:04 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:40:05 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:05 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:40:05 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:40:05 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:40:05 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PTVE
2025-06-11 08:40:05 | INFO     | MassiveScan | info:70 | Processing signal: ACCD - vwap_bounce (confidence: 80.00%)
2025-06-11 08:40:06 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:40:07 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:40:07 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 08:40:07 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 08:40:07 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for ACCD
2025-06-11 08:40:07 | INFO     | MassiveScan | info:70 | Processing signal: CLF - rsi_oversold (confidence: 62.68%)
2025-06-11 08:40:07 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CLF (risk per share: $0.0050)
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CLF (risk per share: $0.0050)
2025-06-11 08:40:08 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Market order placed: CLF buy 27
2025-06-11 08:40:08 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CLF | BUY 27 @ $7.2700 | Strategy: rsi_oversold
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Trade executed: 890f00ff-db12-4042-b11e-1f5a7fe66f3b - CLF buy 27
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Trade executed: CLF - Total trades today: 5
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Processing signal: BILI - rsi_overbought (confidence: 62.42%)
2025-06-11 08:40:09 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for BILI (risk per share: $0.0050)
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for BILI (risk per share: $0.0050)
2025-06-11 08:40:10 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Market order placed: BILI sell 9
2025-06-11 08:40:10 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | BILI | SELL 9 @ $21.1700 | Strategy: rsi_overbought
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Trade executed: 58586d0e-5c34-44c4-a682-bceb0a15e2f0 - BILI sell 9
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Trade executed: BILI - Total trades today: 6
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Processing signal: XPEV - rsi_overbought (confidence: 51.42%)
2025-06-11 08:40:10 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for XPEV (risk per share: $0.0050)
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for XPEV (risk per share: $0.0050)
2025-06-11 08:40:11 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Market order placed: XPEV sell 9
2025-06-11 08:40:11 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | XPEV | SELL 9 @ $20.9550 | Strategy: rsi_overbought
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Trade executed: ae7c92da-ae73-4825-a513-101c800cebdc - XPEV sell 9
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Trade executed: XPEV - Total trades today: 7
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Starting trade monitoring...
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Trade monitoring started
2025-06-11 08:40:12 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:12 | INFO     | MassiveScan | info:70 | Market order placed: IREN sell 19
2025-06-11 08:40:12 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:40:12 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:40:12 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | IREN | SELL 19 @ $10.1500 | PnL: -$1.80 | Reason: Stop loss triggered
2025-06-11 08:40:12 | INFO     | MassiveScan | info:70 | Trade closed: e1972db6-6cdf-4ea0-be9a-ffc7ce41cae2 - IREN - P&L: $-1.80
2025-06-11 08:40:12 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:40:15 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:18 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:18 | INFO     | MassiveScan | info:70 | Market order placed: BILI buy 9
2025-06-11 08:40:18 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:40:18 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | BILI | BUY 9 @ $20.9550 | PnL: +$1.94 | Reason: Profit target reached
2025-06-11 08:40:18 | INFO     | MassiveScan | info:70 | Trade closed: 58586d0e-5c34-44c4-a682-bceb0a15e2f0 - BILI - P&L: $1.94
2025-06-11 08:40:18 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:40:20 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:25 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:40:25 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:40:25 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:40:25 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:25 | INFO     | MassiveScan | info:70 | Stock screener returned 585 stocks
2025-06-11 08:40:25 | INFO     | MassiveScan | info:70 | Added 585 stocks from screener
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:40:26 | INFO     | MassiveScan | info:70 | Scannable universe: 687 symbols
2025-06-11 08:40:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.79 | target_price: 46.78 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:40:27 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:40:30 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:35 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:39 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:40:39 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:40:40 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:41 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BAC | vwap_bounce | Confidence: 80.00% | entry_price: 45.1018 | target_price: 45.*************** | expected_profit: 1.0 | risk_reward: 41.**************
2025-06-11 08:40:41 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:40:41 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:40:43 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:40:44 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:40:46 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:51 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:55 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:40:56 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:01 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:01 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MRNA | vwap_bounce | Confidence: 80.00% | entry_price: 28.085 | target_price: 28.075 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:01 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:06 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:07 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SGOV | vwap_bounce | Confidence: 80.00% | entry_price: 100.47 | target_price: 100.46 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:41:07 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:11 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:12 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | WBD | rsi_overbought | Confidence: 56.47% | entry_price: 10.465 | target_price: 10.455 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:16 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:41:17 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:21 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:23 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RKT | rsi_overbought | Confidence: 54.77% | entry_price: 13.53 | target_price: 13.52 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:26 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:28 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:41:28 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:41:31 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:41:31 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AG | vwap_bounce | Confidence: 80.00% | entry_price: 8.4 | target_price: 8.39 | expected_profit: 1.0 | risk_reward: 5.***************
2025-06-11 08:41:35 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:36 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:41 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:46 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:41:47 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:41:51 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:53 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:41:56 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:59 | ERROR    | MassiveScan | error:82 | Error parsing market data for OAS: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:42:00 | INFO     | MassiveScan | info:70 | Database initialized successfully
2025-06-11 08:42:00 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:42:00 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Stock screener returned 607 stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Added 607 stocks from screener
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:42:01 | INFO     | MassiveScan | info:70 | Scannable universe: 709 symbols
2025-06-11 08:42:02 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:02 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:42:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:42:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:42:07 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:09 | ERROR    | MassiveScan | error:82 | Error parsing market data for ELY: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:42:12 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:17 | INFO     | MassiveScan | info:70 | Scan completed: 12 signals found in 111.5s
2025-06-11 08:42:17 | INFO     | MassiveScan | info:70 | Found 12 trading signals
2025-06-11 08:42:17 | INFO     | MassiveScan | info:70 | Processing signal: SWTX - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:17 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:17 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0045)
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0045)
2025-06-11 08:42:18 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Market order placed: SWTX buy 4
2025-06-11 08:42:18 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:18 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SWTX | BUY 4 @ $46.7900 | Strategy: vwap_bounce
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Trade executed: ********-d29e-43e0-afb8-fe4655b9aba6 - SWTX buy 4
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Trade executed: SWTX - Total trades today: 8
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Processing signal: PDCO - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:19 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:42:19 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:42:19 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:42:19 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:42:19 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PDCO
2025-06-11 08:42:19 | INFO     | MassiveScan | info:70 | Processing signal: BAC - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:20 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for BAC (risk per share: $0.0002)
2025-06-11 08:42:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:42:20 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:42:20 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for BAC (risk per share: $0.0002)
2025-06-11 08:42:21 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Market order placed: BAC buy 4
2025-06-11 08:42:21 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:21 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | BAC | BUY 4 @ $45.1018 | Strategy: vwap_bounce
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Trade executed: e038bceb-7257-4c4d-8dc9-11f38d28ba6e - BAC buy 4
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Trade executed: BAC - Total trades today: 9
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Processing signal: MRNA - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for MRNA (risk per share: $0.0057)
2025-06-11 08:42:22 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for MRNA (risk per share: $0.0057)
2025-06-11 08:42:22 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Market order placed: MRNA buy 7
2025-06-11 08:42:22 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:22 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | MRNA | BUY 7 @ $28.0850 | Strategy: vwap_bounce
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Trade executed: 624f7de8-a1b0-43f4-82c8-f6a1bae9fd87 - MRNA buy 7
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Trade executed: MRNA - Total trades today: 10
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Processing signal: JWN - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:23 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:42:24 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:42:24 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:42:24 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:42:24 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for JWN
2025-06-11 08:42:24 | INFO     | MassiveScan | info:70 | Processing signal: SGOV - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for SGOV (risk per share: $0.0044)
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for SGOV (risk per share: $0.0044)
2025-06-11 08:42:25 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Market order placed: SGOV buy 1
2025-06-11 08:42:25 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:25 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SGOV | BUY 1 @ $100.4700 | Strategy: vwap_bounce
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Trade executed: c30a0e92-da7e-404a-b9e4-73cdd08e598e - SGOV buy 1
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Trade executed: SGOV - Total trades today: 11
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Processing signal: PTVE - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:26 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:42:26 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:42:27 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:42:27 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:27 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:42:27 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:42:27 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for PTVE
2025-06-11 08:42:27 | INFO     | MassiveScan | info:70 | Processing signal: AG - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RKT | rsi_overbought | Confidence: 60.16% | entry_price: 13.56 | target_price: 13.55 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Position size calculated: 23 shares for AG (risk per share: $0.0017)
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Position size calculated: 23 shares for AG (risk per share: $0.0017)
2025-06-11 08:42:28 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Market order placed: AG buy 23
2025-06-11 08:42:28 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:28 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AG | BUY 23 @ $8.4000 | Strategy: vwap_bounce
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Trade executed: 052ff125-2ed7-477d-b91e-41e4ec40e545 - AG buy 23
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Trade executed: AG - Total trades today: 12
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Processing signal: ACCD - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:29 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:42:30 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:42:30 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 08:42:30 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 08:42:30 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for ACCD
2025-06-11 08:42:30 | INFO     | MassiveScan | info:70 | Processing signal: LGTY - vwap_bounce (confidence: 80.00%)
2025-06-11 08:42:30 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:42:31 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:42:31 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:42:31 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 08:42:31 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 08:42:31 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for LGTY
2025-06-11 08:42:31 | INFO     | MassiveScan | info:70 | Processing signal: WBD - rsi_overbought (confidence: 56.47%)
2025-06-11 08:42:32 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:32 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for WBD (risk per share: $0.0050)
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for WBD (risk per share: $0.0050)
2025-06-11 08:42:33 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Market order placed: WBD sell 19
2025-06-11 08:42:33 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | WBD | SELL 19 @ $10.4650 | Strategy: rsi_overbought
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Trade executed: 825074ef-cebf-4c57-81a1-243fdec69b6b - WBD sell 19
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Trade executed: WBD - Total trades today: 13
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Processing signal: RKT - rsi_overbought (confidence: 54.77%)
2025-06-11 08:42:34 | INFO     | MassiveScan | info:70 | Position size calculated: 14 shares for RKT (risk per share: $0.0050)
2025-06-11 08:42:34 | INFO     | MassiveScan | info:70 | Position size calculated: 14 shares for RKT (risk per share: $0.0050)
2025-06-11 08:42:34 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":42210000,"message":"asset \"RKT\" cannot be sold short"}
2025-06-11 08:42:34 | ERROR    | MassiveScan | error:82 | Failed to place entry order for RKT
2025-06-11 08:42:34 | WARNING  | MassiveScan | warning:78 | Failed to execute signal for RKT
2025-06-11 08:42:35 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:35 | INFO     | MassiveScan | info:70 | Market order placed: HBAN sell 12
2025-06-11 08:42:35 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:35 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:42:35 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | HBAN | SELL 12 @ $16.3850 | PnL: +$1.20 | Reason: Profit target reached
2025-06-11 08:42:35 | INFO     | MassiveScan | info:70 | Trade closed: 4c7d2f26-9579-4ec7-aff5-dc7d381b67eb - HBAN - P&L: $1.20
2025-06-11 08:42:35 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:42:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | volume_surge | Confidence: 73.65% | entry_price: 18.59 | target_price: 18.6 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:42:37 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:42:37 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:42 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:43 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:43 | INFO     | MassiveScan | info:70 | Market order placed: WBD buy 19
2025-06-11 08:42:43 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:42:43 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | WBD | BUY 19 @ $10.5300 | PnL: -$1.23 | Reason: Stop loss triggered
2025-06-11 08:42:43 | INFO     | MassiveScan | info:70 | Trade closed: 825074ef-cebf-4c57-81a1-243fdec69b6b - WBD - P&L: $-1.23
2025-06-11 08:42:43 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:42:45 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:42:46 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:42:46 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Stock screener returned 610 stocks
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Added 610 stocks from screener
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:42:47 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:42:47 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:42:48 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:42:48 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:42:48 | INFO     | MassiveScan | info:70 | Scannable universe: 711 symbols
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/BPMC?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HRT?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for HRT
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HSC?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GME?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XLV?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WETF?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SMCI?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/OSG?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CEQP?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MWYN?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MSTY?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for HSC
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for GME
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for XLV
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for WETF
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for SMCI
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for OSG
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for CEQP
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for MWYN
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for MSTY
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/AS?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KHC?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SWTX?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for KHC
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for SWTX
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BAFN?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/AIMC?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for BAFN
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AMAM?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for AMAM
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MIT?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for MIT
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NVDL?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AYA.TO?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for NVDL
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for AYA.TO
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FUSN?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for FUSN
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JDZG?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for JDZG
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JILL?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for JILL
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SWM?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for SWM
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SJR?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for SJR
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BIGZ?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for BIGZ
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/1min/WRK?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CX?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FUSE?apikey=********************************
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IREN?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for CX
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for FUSE
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for IREN
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RXDX?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for RXDX
2025-06-11 08:42:48 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GTI?apikey=********************************
2025-06-11 08:42:48 | WARNING  | MassiveScan | warning:78 | No quote data for GTI
2025-06-11 08:42:49 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:42:49 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:42:49 | INFO     | MassiveScan | info:70 | Scan completed: 4 signals found in 49.1s
2025-06-11 08:42:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 08:42:50 | INFO     | MassiveScan | info:70 | Market closed, stopping scan
2025-06-11 08:42:50 | INFO     | MassiveScan | info:70 | Scan completed: 0 signals found in 2.8s
2025-06-11 08:42:50 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CLF,AG,AAL,SWTX,VALE,BAC,SGOV,XPEV,MRNA?apikey=********************************
2025-06-11 08:42:52 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:55 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CLF,AG,AAL,SWTX,VALE,BAC,SGOV,XPEV,MRNA?apikey=********************************
2025-06-11 08:42:57 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote-short/CLF,AG,AAL,SWTX,VALE,BAC,SGOV,XPEV,MRNA?apikey=********************************
2025-06-11 08:43:02 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:43:07 | INFO     | MassiveScan | info:70 | Market order placed: MRNA sell 7
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:43:07 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:43:07 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | MRNA | SELL 7 @ $27.9975 | PnL: -$0.61 | Reason: Stop loss triggered
2025-06-11 08:43:07 | INFO     | MassiveScan | info:70 | Trade closed: 624f7de8-a1b0-43f4-82c8-f6a1bae9fd87 - MRNA - P&L: $-0.61
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:12 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:21 | INFO     | MassiveScan | info:70 | Running market scan...
2025-06-11 08:43:21 | INFO     | MassiveScan | info:70 | Starting full market scan...
2025-06-11 08:43:21 | INFO     | MassiveScan | info:70 | Building scannable universe...
2025-06-11 08:43:22 | INFO     | MassiveScan | info:70 | Stock screener returned 613 stocks
2025-06-11 08:43:22 | INFO     | MassiveScan | info:70 | Added 613 stocks from screener
2025-06-11 08:43:22 | INFO     | MassiveScan | info:70 | Retrieved 50 active stocks
2025-06-11 08:43:22 | INFO     | MassiveScan | info:70 | Added 50 active stocks
2025-06-11 08:43:23 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:23 | INFO     | MassiveScan | info:70 | Retrieved 50 gaining stocks
2025-06-11 08:43:23 | INFO     | MassiveScan | info:70 | Added 50 gaining stocks
2025-06-11 08:43:23 | INFO     | MassiveScan | info:70 | Retrieved 50 losing stocks
2025-06-11 08:43:23 | INFO     | MassiveScan | info:70 | Added 50 losing stocks
2025-06-11 08:43:23 | INFO     | MassiveScan | info:70 | Scannable universe: 715 symbols
2025-06-11 08:43:24 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.78 | target_price: 46.79 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:43:24 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:43:28 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:30 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KGC | vwap_bounce | Confidence: 80.00% | entry_price: 14.99 | target_price: 14.98 | expected_profit: 1.0 | risk_reward: 7.***************
2025-06-11 08:43:30 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:43:33 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:43:33 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:43:33 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:38 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:38 | ERROR    | MassiveScan | error:82 | Error parsing market data for CBTX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:43:38 | ERROR    | MassiveScan | error:82 | Error parsing market data for HTA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:43:39 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:43:43 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:44 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AVDX | vwap_bounce | Confidence: 80.00% | entry_price: 9.785 | target_price: 9.795 | expected_profit: 1.0 | risk_reward: 5.***************
2025-06-11 08:43:44 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:43:48 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:51 | ERROR    | MassiveScan | error:82 | Error parsing market data for DNAA: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:43:53 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:54 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMCR | vwap_bounce | Confidence: 80.00% | entry_price: 9.22 | target_price: 9.23 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:43:54 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Bot started from GUI
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Initializing MassiveScan Trading Bot...
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Verifying API connections...
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Market is open
2025-06-11 08:43:57 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:57 | ERROR    | MassiveScan | error:82 | Failed to connect to broker
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Cleaning up resources...
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | Final Performance Summary:
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 |   Total trades: 2
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 |   Win rate: 50.0%
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 |   Total P&L: $0.70
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 |   Average P&L: $0.35
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 | MassiveScan Trading Bot stopped
2025-06-11 08:43:58 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:01 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:44:01 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:44:03 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:07 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | WBD | rsi_overbought | Confidence: 61.40% | entry_price: 10.505 | target_price: 10.*************** | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:44:08 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:13 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:44:13 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:44:13 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:23 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:25 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:44:25 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:44:25 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:44:28 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:33 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MSTU | vwap_bounce | Confidence: 80.00% | entry_price: 8.275 | target_price: 8.285 | expected_profit: 1.0 | risk_reward: 4.***************
2025-06-11 08:44:37 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:44:39 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:42 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:44:42 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:44:44 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:48 | ERROR    | MassiveScan | error:82 | Error parsing market data for ITHX: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:44:49 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:54 | ERROR    | MassiveScan | error:82 | Error parsing market data for OAS: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 08:44:57 | ERROR    | MassiveScan | error:82 | Error parsing market data for TVPT: float() argument must be a string or a real number, not 'NoneType'
2025-06-11 08:44:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:45:01 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:45:01 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 08:45:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | NCLH | vwap_bounce | Confidence: 80.00% | entry_price: 19.7099 | target_price: 19.*************** | expected_profit: 1.0 | risk_reward: 6.****************
2025-06-11 08:45:03 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
