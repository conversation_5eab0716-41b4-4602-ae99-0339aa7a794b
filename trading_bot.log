2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IMVT?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NBLY.TO?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NYCB?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VGLT?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ACGL?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BY?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RTP?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NEP?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WEN?apikey=********************************
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for ACGL
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for IMVT
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for NBLY.TO
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for BY
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for NYCB
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for RTP
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for VGLT
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for NEP
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for WEN
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BULLZ?apikey=********************************
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for BULLZ
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WWE?apikey=********************************
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for WWE
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GEO?apikey=********************************
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for GEO
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MU?apikey=********************************
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for MU
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SCHP?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ABX.TO?apikey=********************************
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for SCHP
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for ABX.TO
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ABB?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RDVY?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ALHC?apikey=********************************
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AEL?apikey=********************************
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for ABB
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IVW?apikey=********************************
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for RDVY
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for ALHC
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for AEL
2025-06-11 10:33:56 | WARNING  | MassiveScan | warning:78 | No quote data for IVW
2025-06-11 10:33:56 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PBF?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VSAT?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RLGY?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VTRS?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LRCX?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MGI?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XP?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for PBF
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for VSAT
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for RLGY
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for VTRS
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for LRCX
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for MGI
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for XP
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SAN?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DRH?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PCG?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for SAN
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for DRH
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for PCG
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CORZ?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for CORZ
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PAYO?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for PAYO
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/EVBG?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for EVBG
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CNHI?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CONY?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DLR?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for CNHI
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VIH?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for CONY
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for DLR
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for VIH
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ILF?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for ILF
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PBR-A?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for PBR-A
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VSH?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for VSH
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ORCC?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for ORCC
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MYOV?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BHC?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JBT?apikey=********************************
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NTCO?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for JBT
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RKLB?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for MYOV
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for BHC
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RTPY?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for NTCO
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for RKLB
2025-06-11 10:33:57 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/COPX?apikey=********************************
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for COPX
2025-06-11 10:33:57 | WARNING  | MassiveScan | warning:78 | No quote data for RTPY
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SN?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for SN
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SCHG?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for SCHG
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/XLC?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for XLC
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KIE?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for KIE
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TROX?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for TROX
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WLK?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for WLK
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/USMV?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for USMV
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DJT?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for DJT
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SO?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for SO
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HYG?apikey=********************************
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/STNE?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for HYG
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for STNE
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/YSG?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for YSG
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JNK?apikey=********************************
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SJR?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for JNK
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for SJR
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CRMD?apikey=********************************
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SPHY?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for CRMD
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for SPHY
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HBM?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for HBM
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/T?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for T
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SOVO?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for SOVO
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GILD?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for GILD
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/Z?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for Z
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ROST?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for ROST
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MCHP?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for MCHP
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NLOK?apikey=********************************
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PRCH?apikey=********************************
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HSAI?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for NLOK
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NVDX?apikey=********************************
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BRZE?apikey=********************************
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AVDX?apikey=********************************
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/TPR?apikey=********************************
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AREC?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for PRCH
2025-06-11 10:33:58 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MCHI?apikey=********************************
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for HSAI
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for NVDX
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for BRZE
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for AVDX
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for TPR
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for AREC
2025-06-11 10:33:58 | WARNING  | MassiveScan | warning:78 | No quote data for MCHI
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/SBSW?apikey=********************************
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for SBSW
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/KNX?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FOLD?apikey=********************************
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for KNX
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for FOLD
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GSAH?apikey=********************************
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for GSAH
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CLF?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DWT?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ERIC?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JPST?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VEA?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HRT?apikey=********************************
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for CLF
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for DWT
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for ERIC
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for JPST
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for VEA
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for HRT
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DFAC?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AFRM?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CRSP?apikey=********************************
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for DFAC
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for AFRM
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for CRSP
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CPRT?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/YRI.TO?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MRNA?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CVT?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PLAN?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IUSB?apikey=********************************
2025-06-11 10:33:59 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VGK?apikey=********************************
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for CPRT
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for YRI.TO
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for MRNA
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for CVT
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for PLAN
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for IUSB
2025-06-11 10:33:59 | WARNING  | MassiveScan | warning:78 | No quote data for VGK
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RDW?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ETHA?apikey=********************************
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for RDW
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for ETHA
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ABB?apikey=********************************
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for ABB
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CLBT?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/GDX?apikey=********************************
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for CLBT
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ITB?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ALPN?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MARA?apikey=********************************
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for GDX
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NUWE?apikey=********************************
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for NUWE
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for ITB
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for MARA
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for ALPN
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ET?apikey=********************************
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for ET
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/FOCS?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ELY?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IQV?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NEE?apikey=********************************
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for FOCS
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for ELY
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for IQV
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ASST?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JUNS?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MIND?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/DEI?apikey=********************************
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for NEE
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/IAUM?apikey=********************************
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VRTV?apikey=********************************
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for ASST
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for JUNS
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for MIND
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for DEI
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for IAUM
2025-06-11 10:34:00 | WARNING  | MassiveScan | warning:78 | No quote data for VRTV
2025-06-11 10:34:00 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/market-hours?apikey=********************************
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MDLZ?apikey=********************************
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for MDLZ
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CFG?apikey=********************************
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for CFG
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/STT?apikey=********************************
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/AIMC?apikey=********************************
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for STT
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for AIMC
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CSAN?apikey=********************************
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for CSAN
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MGM?apikey=********************************
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for MGM
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NVAX?apikey=********************************
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/ENPH?apikey=********************************
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for NVAX
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for ENPH
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VALE?apikey=********************************
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for VALE
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/PRCH?apikey=********************************
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for PRCH
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/CBAH?apikey=********************************
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/MSTY?apikey=********************************
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for MSTY
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/RFL?apikey=********************************
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/NOV?apikey=********************************
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/LEU?apikey=********************************
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/VRNA?apikey=********************************
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for CBAH
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/JEPQ?apikey=********************************
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/BRBR?apikey=********************************
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/WFC?apikey=********************************
2025-06-11 10:34:01 | ERROR    | MassiveScan | error:82 | FMP API request failed: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/quote/HWM?apikey=********************************
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for HWM
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for NOV
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for VRNA
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for JEPQ
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for LEU
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for BRBR
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for WFC
2025-06-11 10:34:01 | WARNING  | MassiveScan | warning:78 | No quote data for RFL
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KIE | vwap_bounce | Confidence: 80.00% | entry_price: 58.15 | target_price: 58.17 | expected_profit: 2.0 | risk_reward: 0.182673152391819
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MSTX | vwap_bounce | Confidence: 80.00% | entry_price: 38.4 | target_price: 38.379999999999995 | expected_profit: 2.0 | risk_reward: 0.32174327320190377
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SPYI | vwap_bounce | Confidence: 80.00% | entry_price: 49.9846 | target_price: 50.0046 | expected_profit: 2.0 | risk_reward: 0.10311194755758163
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMCR | vwap_bounce | Confidence: 80.00% | entry_price: 9.245 | target_price: 9.264999999999999 | expected_profit: 2.0 | risk_reward: 0.8331866899482739
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | HLN | vwap_bounce | Confidence: 80.00% | entry_price: 10.945 | target_price: 10.925 | expected_profit: 2.0 | risk_reward: 0.5687381068280394
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:02 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | APLS | vwap_bounce | Confidence: 80.00% | entry_price: 19.64 | target_price: 19.62 | expected_profit: 2.0 | risk_reward: 0.40228765909218955
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TPX | vwap_bounce | Confidence: 80.00% | entry_price: 65.81 | target_price: 65.83 | expected_profit: 2.0 | risk_reward: 0.4844780260096408
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | C | vwap_bounce | Confidence: 80.00% | entry_price: 78.355 | target_price: 78.33500000000001 | expected_profit: 2.0 | risk_reward: 22.025142134614427
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CLBT | vwap_bounce | Confidence: 80.00% | entry_price: 16.43 | target_price: 16.45 | expected_profit: 2.0 | risk_reward: 0.7868163044630511
2025-06-11 10:34:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PPG | vwap_bounce | Confidence: 80.00% | entry_price: 114.35 | target_price: 114.36999999999999 | expected_profit: 2.0 | risk_reward: 0.22582387337742796
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | NTNX | vwap_bounce | Confidence: 80.00% | entry_price: 74.891 | target_price: 74.87100000000001 | expected_profit: 2.0 | risk_reward: 0.3225339195426521
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TFC | vwap_bounce | Confidence: 80.00% | entry_price: 40.57 | target_price: 40.55 | expected_profit: 2.0 | risk_reward: 0.13975498462236444
2025-06-11 10:34:03 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:04 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RIO | vwap_bounce | Confidence: 80.00% | entry_price: 59.14 | target_price: 59.12 | expected_profit: 2.0 | risk_reward: 0.23162558121212182
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | HEES | vwap_bounce | Confidence: 80.00% | entry_price: 94.64 | target_price: 94.62 | expected_profit: 2.0 | risk_reward: 0.09584350956375894
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VGIT | vwap_bounce | Confidence: 80.00% | entry_price: 58.955 | target_price: 58.934999999999995 | expected_profit: 2.0 | risk_reward: 0.20141152276980925
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VCLT | vwap_bounce | Confidence: 80.00% | entry_price: 74.1 | target_price: 74.11999999999999 | expected_profit: 2.0 | risk_reward: 0.0940969493822717
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | FLEX | vwap_bounce | Confidence: 80.00% | entry_price: 43.64 | target_price: 43.660000000000004 | expected_profit: 2.0 | risk_reward: 0.16800164692725042
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:05 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWI | vwap_bounce | Confidence: 80.00% | entry_price: 18.49 | target_price: 18.47 | expected_profit: 2.0 | risk_reward: 1.184319752634262
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SHLS | vwap_bounce | Confidence: 80.00% | entry_price: 5.165 | target_price: 5.1450000000000005 | expected_profit: 2.0 | risk_reward: 3.2574122811463013
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SPAB | vwap_bounce | Confidence: 80.00% | entry_price: 25.215 | target_price: 25.195 | expected_profit: 2.0 | risk_reward: 0.4252573554491935
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SGOV | vwap_bounce | Confidence: 80.00% | entry_price: 100.475 | target_price: 100.49499999999999 | expected_profit: 2.0 | risk_reward: 0.9864153546600709
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | Error parsing market data for CDEV: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | XEL | vwap_bounce | Confidence: 80.00% | entry_price: 68.48 | target_price: 68.46000000000001 | expected_profit: 2.0 | risk_reward: 0.07409555000190085
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BIDU | vwap_bounce | Confidence: 80.00% | entry_price: 87.97 | target_price: 87.99 | expected_profit: 2.0 | risk_reward: 0.19832360399155013
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MRVL | vwap_bounce | Confidence: 80.00% | entry_price: 69.6085 | target_price: 69.58850000000001 | expected_profit: 2.0 | risk_reward: 0.30076608101607016
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACI | vwap_bounce | Confidence: 80.00% | entry_price: 21.17 | target_price: 21.150000000000002 | expected_profit: 2.0 | risk_reward: 17.952870222448656
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:06 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:07 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:07 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SHO | vwap_bounce | Confidence: 80.00% | entry_price: 9.15 | target_price: 9.17 | expected_profit: 2.0 | risk_reward: 0.5734010563302491
2025-06-11 10:34:07 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:07 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SJNK | vwap_bounce | Confidence: 80.00% | entry_price: 25.195 | target_price: 25.215 | expected_profit: 2.0 | risk_reward: 0.4943267405001967
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:08 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | FTNT | vwap_bounce | Confidence: 80.00% | entry_price: 102.93 | target_price: 102.95 | expected_profit: 2.0 | risk_reward: 0.05426399182552206
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MO | vwap_bounce | Confidence: 80.00% | entry_price: 59.345 | target_price: 59.365 | expected_profit: 2.0 | risk_reward: 0.21158166314565302
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:08 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | XEL | vwap_bounce | Confidence: 80.00% | entry_price: 68.48 | target_price: 68.46000000000001 | expected_profit: 2.0 | risk_reward: 0.07409555000190085
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | DDOG | vwap_bounce | Confidence: 80.00% | entry_price: 120.4946 | target_price: 120.5146 | expected_profit: 2.0 | risk_reward: 0.045435328547926
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | UL | vwap_bounce | Confidence: 80.00% | entry_price: 63.035 | target_price: 63.055 | expected_profit: 2.0 | risk_reward: 0.10922072470142022
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PATH | vwap_bounce | Confidence: 80.00% | entry_price: 13.155 | target_price: 13.174999999999999 | expected_profit: 2.0 | risk_reward: 0.4772002381973734
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:09 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:10 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RF | vwap_bounce | Confidence: 80.00% | entry_price: 22.355 | target_price: 22.335 | expected_profit: 2.0 | risk_reward: 0.5259431325486011
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | USHY | vwap_bounce | Confidence: 80.00% | entry_price: 37.03 | target_price: 37.01 | expected_profit: 2.0 | risk_reward: 0.3282970821452785
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PHYS | vwap_bounce | Confidence: 80.00% | entry_price: 25.475 | target_price: 25.455000000000002 | expected_profit: 2.0 | risk_reward: 0.28233639888307727
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | B | vwap_bounce | Confidence: 80.00% | entry_price: 20.155 | target_price: 20.175 | expected_profit: 2.0 | risk_reward: 2.2846776038088454
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AC.TO | vwap_bounce | Confidence: 80.00% | entry_price: 18.83 | target_price: 18.81 | expected_profit: 2.0 | risk_reward: 1.4556905438803147
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SCHD | vwap_bounce | Confidence: 80.00% | entry_price: 26.84 | target_price: 26.86 | expected_profit: 2.0 | risk_reward: 0.2666897997007377
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.765 | target_price: 46.745 | expected_profit: 2.0 | risk_reward: 0.8331212505675092
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:11 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:12 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:12 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:12 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EMB | vwap_bounce | Confidence: 80.00% | entry_price: 91.235 | target_price: 91.255 | expected_profit: 2.0 | risk_reward: 0.06591922862247511
2025-06-11 10:34:12 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:12 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:12 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TGI | vwap_bounce | Confidence: 80.00% | entry_price: 25.865 | target_price: 25.845 | expected_profit: 2.0 | risk_reward: 0.3127509283765188
2025-06-11 10:34:12 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:12 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:12 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:12 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:13 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:13 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:13 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TTD | vwap_bounce | Confidence: 80.00% | entry_price: 73.64 | target_price: 73.62 | expected_profit: 2.0 | risk_reward: 0.26081557638292446
2025-06-11 10:34:13 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:13 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:13 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:13 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MS | vwap_bounce | Confidence: 80.00% | entry_price: 132.465 | target_price: 132.445 | expected_profit: 2.0 | risk_reward: 0.047008240766841965
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | FBTC | vwap_bounce | Confidence: 80.00% | entry_price: 95.915 | target_price: 95.89500000000001 | expected_profit: 2.0 | risk_reward: 0.25489809658923945
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BNDX | vwap_bounce | Confidence: 80.00% | entry_price: 49.325 | target_price: 49.345000000000006 | expected_profit: 2.0 | risk_reward: 1.5839234700083045
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | Error parsing market data for BCAC: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.0 | expected_profit: 2.0 | risk_reward: 2.0432332059584892
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | HSAI | vwap_bounce | Confidence: 80.00% | entry_price: 19.515 | target_price: 19.535 | expected_profit: 2.0 | risk_reward: 0.5888752608275283
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:14 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TROX | vwap_bounce | Confidence: 80.00% | entry_price: 6.105 | target_price: 6.085000000000001 | expected_profit: 2.0 | risk_reward: 1.1733289691493685
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:14 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:15 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:15 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | USFR | vwap_bounce | Confidence: 80.00% | entry_price: 50.385 | target_price: 50.364999999999995 | expected_profit: 2.0 | risk_reward: 2.9600091995988085
2025-06-11 10:34:15 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:15 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:15 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | Error parsing market data for OAS: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ABX.TO | vwap_bounce | Confidence: 80.00% | entry_price: 27.545 | target_price: 27.525000000000002 | expected_profit: 2.0 | risk_reward: 0.2793322570956991
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PEP | vwap_bounce | Confidence: 80.00% | entry_price: 130.2078 | target_price: 130.2278 | expected_profit: 2.0 | risk_reward: 0.14671424400691951
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | DESP | vwap_bounce | Confidence: 80.00% | entry_price: 19.5 | target_price: 19.48 | expected_profit: 2.0 | risk_reward: 1.708513390061062
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | HAL | vwap_bounce | Confidence: 80.00% | entry_price: 21.825 | target_price: 21.845 | expected_profit: 2.0 | risk_reward: 1.8169004001498692
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JEPI | vwap_bounce | Confidence: 80.00% | entry_price: 56.255 | target_price: 56.275000000000006 | expected_profit: 2.0 | risk_reward: 0.4236241438188226
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | Error parsing market data for BRPM: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MRVL | vwap_bounce | Confidence: 80.00% | entry_price: 69.58 | target_price: 69.56 | expected_profit: 2.0 | risk_reward: 0.5263592825469305
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:16 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EMB | vwap_bounce | Confidence: 80.00% | entry_price: 91.2416 | target_price: 91.2616 | expected_profit: 2.0 | risk_reward: 0.06451579426318116
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LUNR | vwap_bounce | Confidence: 80.00% | entry_price: 11.375 | target_price: 11.395 | expected_profit: 2.0 | risk_reward: 0.663464753645655
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SPHY | vwap_bounce | Confidence: 80.00% | entry_price: 23.4902 | target_price: 23.470200000000002 | expected_profit: 2.0 | risk_reward: 0.49511857891799615
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CAG | vwap_bounce | Confidence: 80.00% | entry_price: 22.295 | target_price: 22.275000000000002 | expected_profit: 2.0 | risk_reward: 0.2511368913228034
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:17 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:18 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PEG | vwap_bounce | Confidence: 80.00% | entry_price: 80.63 | target_price: 80.61 | expected_profit: 2.0 | risk_reward: 0.13148979539666256
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CCO.TO | vwap_bounce | Confidence: 80.00% | entry_price: 91.45 | target_price: 91.43 | expected_profit: 2.0 | risk_reward: 0.04765925286930077
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VPL | vwap_bounce | Confidence: 80.00% | entry_price: 80.46 | target_price: 80.47999999999999 | expected_profit: 2.0 | risk_reward: 0.07829973727767311
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | Error parsing market data for CFVI: int() argument must be a string, a bytes-like object or a real number, not 'NoneType'
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VRT | vwap_bounce | Confidence: 80.00% | entry_price: 110.66 | target_price: 110.64 | expected_profit: 2.0 | risk_reward: 0.5357248294811462
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KVYO | vwap_bounce | Confidence: 80.00% | entry_price: 34.655 | target_price: 34.675000000000004 | expected_profit: 2.0 | risk_reward: 0.18274901403011662
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMH | vwap_bounce | Confidence: 80.00% | entry_price: 36.77 | target_price: 36.75 | expected_profit: 2.0 | risk_reward: 5.337563839949134
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PPG | vwap_bounce | Confidence: 80.00% | entry_price: 114.35 | target_price: 114.36999999999999 | expected_profit: 2.0 | risk_reward: 0.22582387337742796
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KHC | vwap_bounce | Confidence: 80.00% | entry_price: 26.465 | target_price: 26.485 | expected_profit: 2.0 | risk_reward: 0.30427242489599116
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:19 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VCLT | vwap_bounce | Confidence: 80.00% | entry_price: 74.105 | target_price: 74.125 | expected_profit: 2.0 | risk_reward: 0.09193426589125075
2025-06-11 10:34:19 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TD.TO | vwap_bounce | Confidence: 80.00% | entry_price: 95.95 | target_price: 95.93 | expected_profit: 2.0 | risk_reward: 0.06451757863756999
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EQT | vwap_bounce | Confidence: 80.00% | entry_price: 54.225 | target_price: 54.205 | expected_profit: 2.0 | risk_reward: 0.1013106263199063
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SQQQ | vwap_bounce | Confidence: 80.00% | entry_price: 21.6651 | target_price: 21.6851 | expected_profit: 2.0 | risk_reward: 0.9808468629240955
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | IBN | vwap_bounce | Confidence: 80.00% | entry_price: 33.51 | target_price: 33.489999999999995 | expected_profit: 2.0 | risk_reward: 0.799643022444318
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AAAU | vwap_bounce | Confidence: 80.00% | entry_price: 32.965 | target_price: 32.945 | expected_profit: 2.0 | risk_reward: 0.426498477043351
2025-06-11 10:34:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GEN | vwap_bounce | Confidence: 80.00% | entry_price: 29.58 | target_price: 29.599999999999998 | expected_profit: 2.0 | risk_reward: 0.1467304056356075
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:20 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | Error updating P&L display: 'TradingBotGUI' object has no attribute 'stats_labels'
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:21 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACGL | vwap_bounce | Confidence: 80.00% | entry_price: 90.31 | target_price: 90.33 | expected_profit: 2.0 | risk_reward: 0.04658971391019076
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:21 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TOST | vwap_bounce | Confidence: 80.00% | entry_price: 43.66 | target_price: 43.68 | expected_profit: 2.0 | risk_reward: 0.23829732260588446
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:21 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SHO | vwap_bounce | Confidence: 80.00% | entry_price: 9.145 | target_price: 9.165 | expected_profit: 2.0 | risk_reward: 0.6693529832425343
2025-06-11 10:34:21 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:22 | INFO     | MassiveScan | info:70 | Stopping MassiveScan Trading Bot...
2025-06-11 10:34:22 | INFO     | MassiveScan | info:70 | Trade monitoring stopped
2025-06-11 10:34:22 | INFO     | MassiveScan | info:70 | Closing all positions: Bot shutdown
2025-06-11 10:34:22 | INFO     | MassiveScan | info:70 | All positions closed
2025-06-11 10:34:22 | INFO     | MassiveScan | info:70 | Bot stopped from GUI
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | DDOG | vwap_bounce | Confidence: 80.00% | entry_price: 120.51 | target_price: 120.53 | expected_profit: 2.0 | risk_reward: 0.04708252006945572
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EWA | vwap_bounce | Confidence: 80.00% | entry_price: 26.695 | target_price: 26.675 | expected_profit: 2.0 | risk_reward: 0.3688651659707432
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CAH | vwap_bounce | Confidence: 80.00% | entry_price: 153.2799 | target_price: 153.2599 | expected_profit: 2.0 | risk_reward: 0.13426743046801265
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BCE.TO | vwap_bounce | Confidence: 80.00% | entry_price: 30.63 | target_price: 30.61 | expected_profit: 2.0 | risk_reward: 0.23079510281515445
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EFA | vwap_bounce | Confidence: 80.00% | entry_price: 90.005 | target_price: 89.985 | expected_profit: 2.0 | risk_reward: 0.1341463557448325
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GOOGL | vwap_bounce | Confidence: 80.00% | entry_price: 178.865 | target_price: 178.88500000000002 | expected_profit: 2.0 | risk_reward: 0.02346592658651808
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:22 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VTEB | vwap_bounce | Confidence: 80.00% | entry_price: 48.775 | target_price: 48.754999999999995 | expected_profit: 2.0 | risk_reward: 0.430812525756953
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KBE | vwap_bounce | Confidence: 80.00% | entry_price: 54.78 | target_price: 54.76 | expected_profit: 2.0 | risk_reward: 0.26454236765782446
2025-06-11 10:34:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CUK | vwap_bounce | Confidence: 80.00% | entry_price: 21.965 | target_price: 21.985 | expected_profit: 2.0 | risk_reward: 0.4792208424460756
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RY.TO | vwap_bounce | Confidence: 80.00% | entry_price: 175.61 | target_price: 175.59 | expected_profit: 2.0 | risk_reward: 0.031202116540373675
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNP | vwap_bounce | Confidence: 80.00% | entry_price: 36.385 | target_price: 36.364999999999995 | expected_profit: 2.0 | risk_reward: 0.5159193674583055
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VIK | vwap_bounce | Confidence: 80.00% | entry_price: 48.54 | target_price: 48.519999999999996 | expected_profit: 2.0 | risk_reward: 0.13367583707976466
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | IR | vwap_bounce | Confidence: 80.00% | entry_price: 83.895 | target_price: 83.875 | expected_profit: 2.0 | risk_reward: 0.07815828952326827
2025-06-11 10:34:25 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TD | vwap_bounce | Confidence: 80.00% | entry_price: 70.195 | target_price: 70.175 | expected_profit: 2.0 | risk_reward: 0.09457256161861613
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | XLP | vwap_bounce | Confidence: 80.00% | entry_price: 81.49 | target_price: 81.47 | expected_profit: 2.0 | risk_reward: 0.10516287786710722
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BGC | vwap_bounce | Confidence: 80.00% | entry_price: 9.7722 | target_price: 9.7522 | expected_profit: 2.0 | risk_reward: 0.5674010962685341
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VEU | vwap_bounce | Confidence: 80.00% | entry_price: 66.856 | target_price: 66.836 | expected_profit: 2.0 | risk_reward: 0.0693653356252157
2025-06-11 10:34:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | FE | vwap_bounce | Confidence: 80.00% | entry_price: 40.165 | target_price: 40.144999999999996 | expected_profit: 2.0 | risk_reward: 0.12074256445484767
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:27 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:28 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AS | vwap_bounce | Confidence: 80.00% | entry_price: 38.135 | target_price: 38.155 | expected_profit: 2.0 | risk_reward: 0.1192433595956506
2025-06-11 10:34:28 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:28 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:28 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:28 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LUNR | vwap_bounce | Confidence: 80.00% | entry_price: 11.37 | target_price: 11.389999999999999 | expected_profit: 2.0 | risk_reward: 0.5690745303709873
2025-06-11 10:34:28 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:28 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:28 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TAK | vwap_bounce | Confidence: 80.00% | entry_price: 15.23 | target_price: 15.21 | expected_profit: 2.0 | risk_reward: 0.44656350008089196
2025-06-11 10:34:28 | ERROR    | MassiveScan | error:82 | Failed to save signal: Object of type bool is not JSON serializable
2025-06-11 10:34:28 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
2025-06-11 10:34:28 | ERROR    | MassiveScan | error:82 | AI enhancement failed, using fallback: name 'scan_start_time' is not defined
