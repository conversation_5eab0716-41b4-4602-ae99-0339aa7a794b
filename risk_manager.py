"""
Risk management module for the MassiveScan trading bot.
Handles position sizing, risk limits, and safety checks.
"""

from datetime import datetime, date, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

from config import config
from models import Trade, Position, TradingSignal, OrderSide, TradeStatus
from database import db
from logger import log_info, log_warning, log_error, log_risk_alert
from broker import broker

@dataclass
class RiskMetrics:
    """Risk metrics for current trading session"""
    daily_pnl: float = 0.0
    daily_trades: int = 0
    open_positions: int = 0
    total_exposure: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    largest_loss: float = 0.0
    consecutive_losses: int = 0

class RiskManager:
    """Risk management system"""
    
    def __init__(self):
        self.daily_stats = {}
        self.risk_metrics = RiskMetrics()
        self.trading_halted = False
        self.halt_reason = ""
        
        # Load today's stats
        self._load_daily_stats()
    
    def _load_daily_stats(self):
        """Load today's trading statistics"""
        try:
            today = date.today()
            trades = db.get_trades_by_date(today)
            
            if trades:
                self.risk_metrics.daily_trades = len(trades)
                self.risk_metrics.daily_pnl = sum(t.net_pnl for t in trades if t.status == TradeStatus.CLOSED)
                
                # Calculate win rate
                closed_trades = [t for t in trades if t.status == TradeStatus.CLOSED]
                if closed_trades:
                    winning_trades = [t for t in closed_trades if t.net_pnl > 0]
                    self.risk_metrics.win_rate = len(winning_trades) / len(closed_trades)
                    
                    # Find largest loss
                    losses = [t.net_pnl for t in closed_trades if t.net_pnl < 0]
                    if losses:
                        self.risk_metrics.largest_loss = min(losses)
                    
                    # Calculate consecutive losses
                    self.risk_metrics.consecutive_losses = self._calculate_consecutive_losses(closed_trades)
            
            # Get open positions
            open_trades = db.get_open_trades()
            self.risk_metrics.open_positions = len(open_trades)
            self.risk_metrics.total_exposure = sum(abs(t.entry_price * t.quantity) for t in open_trades)
            
            log_info(f"Risk metrics loaded - Daily PnL: ${self.risk_metrics.daily_pnl:.2f}, "
                    f"Trades: {self.risk_metrics.daily_trades}, Open: {self.risk_metrics.open_positions}")
            
        except Exception as e:
            log_error(f"Failed to load daily stats: {e}")
    
    def _calculate_consecutive_losses(self, trades: List[Trade]) -> int:
        """Calculate consecutive losses from most recent trades"""
        if not trades:
            return 0
        
        # Sort by exit time (most recent first)
        sorted_trades = sorted(trades, key=lambda t: t.exit_time or datetime.now(), reverse=True)
        
        consecutive = 0
        for trade in sorted_trades:
            if trade.net_pnl < 0:
                consecutive += 1
            else:
                break
        
        return consecutive
    
    def can_place_trade(self, signal: TradingSignal) -> Tuple[bool, str]:
        """Check if we can place a trade based on risk limits"""
        try:
            # Check if trading is halted
            if self.trading_halted:
                return False, f"Trading halted: {self.halt_reason}"
            
            # Check daily trade limit
            if self.risk_metrics.daily_trades >= config.trading.max_daily_trades:
                return False, f"Daily trade limit reached: {self.risk_metrics.daily_trades}"
            
            # Check daily loss limit
            if self.risk_metrics.daily_pnl <= -config.trading.max_daily_loss:
                self.halt_trading("Daily loss limit exceeded")
                return False, f"Daily loss limit exceeded: ${self.risk_metrics.daily_pnl:.2f}"
            
            # Check maximum concurrent positions
            if self.risk_metrics.open_positions >= config.trading.max_concurrent_positions:
                return False, f"Maximum concurrent positions reached: {self.risk_metrics.open_positions}"
            
            # Check consecutive losses
            if self.risk_metrics.consecutive_losses >= 5:
                return False, f"Too many consecutive losses: {self.risk_metrics.consecutive_losses}"
            
            # Check if we have enough buying power
            buying_power = broker.get_buying_power()
            required_capital = self.calculate_position_value(signal)
            
            if required_capital > buying_power:
                return False, f"Insufficient buying power: ${buying_power:.2f} < ${required_capital:.2f}"
            
            # Check total exposure limit
            if self.risk_metrics.total_exposure + required_capital > buying_power * 0.8:
                return False, "Total exposure limit would be exceeded"
            
            # Check signal quality
            if signal.confidence < 0.5:
                return False, f"Signal confidence too low: {signal.confidence:.2%}"
            
            if signal.risk_reward_ratio < 1.5:
                return False, f"Risk/reward ratio too low: {signal.risk_reward_ratio:.2f}"
            
            # Check market conditions
            if not broker.is_market_open():
                return False, "Market is closed"
            
            return True, "Trade approved"
            
        except Exception as e:
            log_error(f"Error checking trade approval: {e}")
            return False, f"Risk check error: {e}"
    
    def calculate_position_size(self, signal: TradingSignal) -> int:
        """Calculate appropriate position size for a signal"""
        try:
            entry_price = signal.entry_price
            stop_loss_price = signal.stop_loss_price
            
            if entry_price <= 0 or stop_loss_price <= 0:
                return 0
            
            # Calculate risk per share
            risk_per_share = abs(entry_price - stop_loss_price)
            
            if risk_per_share <= 0:
                return 0
            
            # Calculate position size based on fixed dollar risk
            max_risk = min(config.trading.stop_loss_dollars, config.trading.max_position_size * 0.02)
            position_size = int(max_risk / risk_per_share)
            
            # Ensure minimum position size
            position_size = max(1, position_size)
            
            # Check against maximum position size
            position_value = position_size * entry_price
            if position_value > config.trading.max_position_size:
                position_size = int(config.trading.max_position_size / entry_price)
            
            # Check against buying power
            buying_power = broker.get_buying_power()
            max_shares_by_power = int(buying_power * 0.1 / entry_price)  # Use max 10% of buying power per trade
            position_size = min(position_size, max_shares_by_power)
            
            # Final safety check
            position_size = max(0, position_size)
            
            log_info(f"Position size calculated: {position_size} shares for {signal.symbol} "
                    f"(risk per share: ${risk_per_share:.4f})")
            
            return position_size
            
        except Exception as e:
            log_error(f"Error calculating position size: {e}")
            return 0
    
    def calculate_position_value(self, signal: TradingSignal) -> float:
        """Calculate total position value"""
        position_size = self.calculate_position_size(signal)
        return position_size * signal.entry_price
    
    def update_trade_metrics(self, trade: Trade):
        """Update risk metrics when a trade is completed"""
        try:
            if trade.status == TradeStatus.CLOSED:
                self.risk_metrics.daily_pnl += trade.net_pnl
                
                if trade.net_pnl < 0:
                    self.risk_metrics.consecutive_losses += 1
                    if trade.net_pnl < self.risk_metrics.largest_loss:
                        self.risk_metrics.largest_loss = trade.net_pnl
                else:
                    self.risk_metrics.consecutive_losses = 0
                
                # Check for risk alerts
                self._check_risk_alerts()
            
        except Exception as e:
            log_error(f"Error updating trade metrics: {e}")
    
    def _check_risk_alerts(self):
        """Check for risk alert conditions"""
        try:
            # Daily loss approaching limit
            loss_pct = abs(self.risk_metrics.daily_pnl) / config.trading.max_daily_loss
            if loss_pct > 0.8:
                log_risk_alert("Daily Loss", 
                             f"Daily loss at {loss_pct:.1%} of limit (${self.risk_metrics.daily_pnl:.2f})",
                             "WARNING")
            
            # Too many consecutive losses
            if self.risk_metrics.consecutive_losses >= 3:
                log_risk_alert("Consecutive Losses", 
                             f"{self.risk_metrics.consecutive_losses} consecutive losses",
                             "WARNING")
            
            # Win rate too low
            if self.risk_metrics.daily_trades >= 10 and self.risk_metrics.win_rate < 0.4:
                log_risk_alert("Low Win Rate", 
                             f"Win rate: {self.risk_metrics.win_rate:.1%} with {self.risk_metrics.daily_trades} trades",
                             "WARNING")
            
            # Large single loss
            if self.risk_metrics.largest_loss < -config.trading.stop_loss_dollars * 2:
                log_risk_alert("Large Loss", 
                             f"Largest loss: ${self.risk_metrics.largest_loss:.2f}",
                             "CRITICAL")
            
        except Exception as e:
            log_error(f"Error checking risk alerts: {e}")
    
    def halt_trading(self, reason: str):
        """Halt all trading"""
        self.trading_halted = True
        self.halt_reason = reason
        log_risk_alert("Trading Halted", reason, "CRITICAL")
    
    def resume_trading(self):
        """Resume trading"""
        self.trading_halted = False
        self.halt_reason = ""
        log_info("Trading resumed")
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """Get current risk summary"""
        return {
            'daily_pnl': self.risk_metrics.daily_pnl,
            'daily_trades': self.risk_metrics.daily_trades,
            'open_positions': self.risk_metrics.open_positions,
            'total_exposure': self.risk_metrics.total_exposure,
            'win_rate': self.risk_metrics.win_rate,
            'largest_loss': self.risk_metrics.largest_loss,
            'consecutive_losses': self.risk_metrics.consecutive_losses,
            'trading_halted': self.trading_halted,
            'halt_reason': self.halt_reason,
            'daily_loss_limit': config.trading.max_daily_loss,
            'max_daily_trades': config.trading.max_daily_trades,
            'max_concurrent_positions': config.trading.max_concurrent_positions,
            'loss_limit_used_pct': abs(self.risk_metrics.daily_pnl) / config.trading.max_daily_loss * 100,
            'trade_limit_used_pct': self.risk_metrics.daily_trades / config.trading.max_daily_trades * 100
        }
    
    def reset_daily_stats(self):
        """Reset daily statistics (call at market open)"""
        self.risk_metrics = RiskMetrics()
        self.trading_halted = False
        self.halt_reason = ""
        log_info("Daily risk statistics reset")
    
    def check_position_risk(self, position: Position) -> Dict[str, Any]:
        """Check risk for an open position"""
        try:
            current_pnl = position.unrealized_pnl
            position_value = abs(position.quantity * position.current_price)
            
            # Calculate risk metrics
            risk_pct = abs(current_pnl) / position_value * 100 if position_value > 0 else 0
            
            # Determine risk level
            risk_level = "LOW"
            if risk_pct > 2:
                risk_level = "HIGH"
            elif risk_pct > 1:
                risk_level = "MEDIUM"
            
            # Check if stop loss should be triggered
            should_close = False
            close_reason = ""
            
            if current_pnl < -config.trading.stop_loss_dollars:
                should_close = True
                close_reason = "Stop loss triggered"
            elif risk_pct > 3:
                should_close = True
                close_reason = "Risk percentage too high"
            
            return {
                'symbol': position.symbol,
                'current_pnl': current_pnl,
                'position_value': position_value,
                'risk_percentage': risk_pct,
                'risk_level': risk_level,
                'should_close': should_close,
                'close_reason': close_reason
            }
            
        except Exception as e:
            log_error(f"Error checking position risk: {e}")
            return {}

# Global risk manager instance
risk_manager = RiskManager()

if __name__ == "__main__":
    # Test risk manager
    from models import TradingSignal, SignalType
    
    # Create test signal
    signal = TradingSignal(
        symbol="AAPL",
        signal_type=SignalType.MOMENTUM_BREAKOUT,
        timestamp=datetime.now(),
        confidence=0.75,
        entry_price=150.00,
        target_price=151.00,
        stop_loss_price=149.50,
        expected_profit=1.00,
        risk_reward_ratio=2.0
    )
    
    # Test trade approval
    can_trade, reason = risk_manager.can_place_trade(signal)
    print(f"Can place trade: {can_trade} - {reason}")
    
    # Test position sizing
    position_size = risk_manager.calculate_position_size(signal)
    print(f"Position size: {position_size} shares")
    
    # Get risk summary
    summary = risk_manager.get_risk_summary()
    print(f"Risk summary: {summary}")
