"""
Test script to demonstrate the scanner working even when market is closed.
This will show you how the bot scans for trading opportunities.
"""

import asyncio
from datetime import datetime
from scanner import scanner
from data_provider import data_provider
from logger import log_info

async def test_scanner_demo():
    """Test the scanner in demo mode"""
    print("🚀 MassiveScan Scanner Test")
    print("=" * 50)
    
    # Check market status
    market_open = data_provider.is_market_open()
    print(f"Market Status: {'OPEN' if market_open else 'CLOSED'}")
    print(f"Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not market_open:
        print("\n⚠️  Market is closed, enabling DEMO MODE for testing")
        scanner.enable_demo_mode()
    
    print("\n📊 Testing Stock Universe...")
    
    # Test getting scannable universe
    symbols = scanner.get_scannable_universe()
    print(f"✅ Found {len(symbols)} stocks to scan")
    
    if symbols:
        print(f"Sample stocks: {symbols[:10]}")
        
        print("\n🔍 Testing Individual Stock Scanning...")
        
        # Test scanning a few individual stocks
        test_symbols = symbols[:5]  # Test first 5 stocks
        
        for symbol in test_symbols:
            print(f"\nScanning {symbol}...")
            try:
                signals = scanner.scan_symbol(symbol)
                if signals:
                    print(f"  🎯 Found {len(signals)} signals!")
                    for signal in signals:
                        print(f"    - {signal.signal_type.value}: {signal.confidence:.1%} confidence")
                        print(f"      Entry: ${signal.entry_price:.2f}, Target: ${signal.target_price:.2f}")
                else:
                    print(f"  ⚪ No signals found")
            except Exception as e:
                print(f"  ❌ Error scanning {symbol}: {e}")
        
        print("\n🚀 Testing Batch Scanning...")
        
        # Test batch scanning
        batch_signals = scanner.scan_batch(test_symbols[:3])
        print(f"✅ Batch scan found {len(batch_signals)} total signals")
        
        for signal in batch_signals:
            print(f"  🎯 {signal.symbol}: {signal.signal_type.value} ({signal.confidence:.1%})")
    
    else:
        print("❌ No stocks found in universe")
    
    # Get scanner statistics
    stats = scanner.get_scan_statistics()
    print(f"\n📈 Scanner Statistics:")
    print(f"  Total scans: {stats['total_scans']}")
    print(f"  Signals found: {stats['signals_found']}")
    print(f"  Scanned symbols: {stats['scanned_symbols_count']}")
    print(f"  Currently scanning: {stats['is_currently_scanning']}")
    
    print("\n✅ Scanner test completed!")
    print("\nTo run the full bot:")
    print("  python gui.py        # Desktop interface")
    print("  python main.py       # Command line")
    print("  python run_bot.py    # Easy launcher")

def test_quick_scan():
    """Quick synchronous test"""
    print("🔍 Quick Scanner Test")
    print("=" * 30)
    
    # Enable demo mode
    scanner.enable_demo_mode()
    
    # Test getting a few stocks
    print("Getting stock universe...")
    symbols = scanner.get_scannable_universe()
    print(f"Found {len(symbols)} stocks")
    
    if symbols:
        # Test scanning one stock
        test_symbol = symbols[0]
        print(f"\nTesting scan of {test_symbol}...")
        
        try:
            signals = scanner.scan_symbol(test_symbol)
            print(f"Result: {len(signals)} signals found")
            
            for signal in signals:
                print(f"  - {signal.signal_type.value}: {signal.confidence:.1%}")
        
        except Exception as e:
            print(f"Error: {e}")
    
    print("\n✅ Quick test completed!")

if __name__ == "__main__":
    print("Choose test mode:")
    print("1. Quick Test (fast)")
    print("2. Full Demo (detailed)")
    
    try:
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            test_quick_scan()
        elif choice == "2":
            asyncio.run(test_scanner_demo())
        else:
            print("Invalid choice, running quick test...")
            test_quick_scan()
            
    except KeyboardInterrupt:
        print("\n\nTest cancelled by user")
    except Exception as e:
        print(f"\nTest error: {e}")
