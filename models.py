"""
Data models for the MassiveScan trading bot.
Defines data structures for trades, positions, and market data.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum

class OrderSide(Enum):
    """Order side enumeration"""
    BUY = "buy"
    SELL = "sell"

class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

class TradeStatus(Enum):
    """Trade status enumeration"""
    OPEN = "open"
    CLOSED = "closed"
    CANCELLED = "cancelled"

class SignalType(Enum):
    """Trading signal types"""
    MOMENTUM_BREAKOUT = "momentum_breakout"
    VOLUME_SURGE = "volume_surge"
    RSI_OVERSOLD = "rsi_oversold"
    RSI_OVERBOUGHT = "rsi_overbought"
    VWAP_BOUNCE = "vwap_bounce"
    SQUEEZE_BREAKOUT = "squeeze_breakout"
    SUPPORT_BOUNCE = "support_bounce"
    RESISTANCE_BREAK = "resistance_break"

@dataclass
class MarketData:
    """Market data for a stock"""
    symbol: str
    timestamp: datetime
    price: float
    volume: int
    open_price: float
    high: float
    low: float
    close: float
    vwap: Optional[float] = None
    avg_volume: Optional[int] = None
    market_cap: Optional[int] = None
    
    def __post_init__(self):
        if self.vwap is None:
            # Calculate VWAP if not provided
            self.vwap = (self.high + self.low + self.close) / 3

@dataclass
class TechnicalIndicators:
    """Technical analysis indicators"""
    symbol: str
    timestamp: datetime
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    bb_upper: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_lower: Optional[float] = None
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    ema_12: Optional[float] = None
    ema_26: Optional[float] = None
    volume_sma: Optional[float] = None
    atr: Optional[float] = None

@dataclass
class TradingSignal:
    """Trading signal generated by scanner"""
    symbol: str
    signal_type: SignalType
    timestamp: datetime
    confidence: float  # 0.0 to 1.0
    entry_price: float
    target_price: float
    stop_loss_price: float
    expected_profit: float
    risk_reward_ratio: float
    volume_ratio: Optional[float] = None
    technical_data: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert signal to dictionary"""
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type.value,
            'timestamp': self.timestamp.isoformat(),
            'confidence': self.confidence,
            'entry_price': self.entry_price,
            'target_price': self.target_price,
            'stop_loss_price': self.stop_loss_price,
            'expected_profit': self.expected_profit,
            'risk_reward_ratio': self.risk_reward_ratio,
            'volume_ratio': self.volume_ratio,
            'technical_data': self.technical_data
        }

@dataclass
class Order:
    """Trading order"""
    id: Optional[str] = None
    symbol: str = ""
    side: OrderSide = OrderSide.BUY
    order_type: OrderType = OrderType.MARKET
    quantity: float = 0.0
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    filled_at: Optional[datetime] = None
    filled_price: Optional[float] = None
    filled_quantity: float = 0.0
    broker_order_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert order to dictionary"""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'stop_price': self.stop_price,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'filled_at': self.filled_at.isoformat() if self.filled_at else None,
            'filled_price': self.filled_price,
            'filled_quantity': self.filled_quantity,
            'broker_order_id': self.broker_order_id
        }

@dataclass
class Position:
    """Trading position"""
    symbol: str
    quantity: float
    entry_price: float
    current_price: float
    entry_time: datetime
    side: OrderSide
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    target_price: Optional[float] = None
    stop_loss_price: Optional[float] = None
    
    @property
    def market_value(self) -> float:
        """Current market value of position"""
        return self.quantity * self.current_price
    
    @property
    def cost_basis(self) -> float:
        """Cost basis of position"""
        return self.quantity * self.entry_price
    
    def update_pnl(self, current_price: float):
        """Update unrealized PnL"""
        self.current_price = current_price
        if self.side == OrderSide.BUY:
            self.unrealized_pnl = (current_price - self.entry_price) * self.quantity
        else:
            self.unrealized_pnl = (self.entry_price - current_price) * self.quantity

@dataclass
class Trade:
    """Completed trade record"""
    id: Optional[str] = None
    symbol: str = ""
    side: OrderSide = OrderSide.BUY
    quantity: float = 0.0
    entry_price: float = 0.0
    exit_price: float = 0.0
    entry_time: datetime = field(default_factory=datetime.now)
    exit_time: Optional[datetime] = None
    pnl: float = 0.0
    commission: float = 0.0
    net_pnl: float = 0.0
    status: TradeStatus = TradeStatus.OPEN
    strategy: str = ""
    exit_reason: str = ""
    entry_order_id: Optional[str] = None
    exit_order_id: Optional[str] = None
    signal_data: Optional[Dict[str, Any]] = None
    
    def close_trade(self, exit_price: float, exit_time: datetime, exit_reason: str = "", commission: float = 0.0):
        """Close the trade and calculate PnL"""
        self.exit_price = exit_price
        self.exit_time = exit_time
        self.exit_reason = exit_reason
        self.commission = commission
        self.status = TradeStatus.CLOSED
        
        # Calculate PnL
        if self.side == OrderSide.BUY:
            self.pnl = (exit_price - self.entry_price) * self.quantity
        else:
            self.pnl = (self.entry_price - exit_price) * self.quantity
        
        self.net_pnl = self.pnl - commission
    
    @property
    def duration_minutes(self) -> Optional[float]:
        """Trade duration in minutes"""
        if self.exit_time:
            return (self.exit_time - self.entry_time).total_seconds() / 60
        return None
    
    @property
    def return_percent(self) -> float:
        """Return percentage"""
        if self.entry_price > 0:
            return (self.pnl / (self.entry_price * self.quantity)) * 100
        return 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert trade to dictionary"""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'side': self.side.value,
            'quantity': self.quantity,
            'entry_price': self.entry_price,
            'exit_price': self.exit_price,
            'entry_time': self.entry_time.isoformat(),
            'exit_time': self.exit_time.isoformat() if self.exit_time else None,
            'pnl': self.pnl,
            'commission': self.commission,
            'net_pnl': self.net_pnl,
            'status': self.status.value,
            'strategy': self.strategy,
            'exit_reason': self.exit_reason,
            'entry_order_id': self.entry_order_id,
            'exit_order_id': self.exit_order_id,
            'signal_data': self.signal_data,
            'duration_minutes': self.duration_minutes,
            'return_percent': self.return_percent
        }

@dataclass
class DailyStats:
    """Daily trading statistics"""
    date: datetime
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_pnl: float = 0.0
    gross_profit: float = 0.0
    gross_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    total_commission: float = 0.0
    
    def calculate_stats(self, trades: List[Trade]):
        """Calculate statistics from list of trades"""
        if not trades:
            return
        
        self.total_trades = len(trades)
        winning_trades = [t for t in trades if t.net_pnl > 0]
        losing_trades = [t for t in trades if t.net_pnl < 0]
        
        self.winning_trades = len(winning_trades)
        self.losing_trades = len(losing_trades)
        self.total_pnl = sum(t.net_pnl for t in trades)
        self.total_commission = sum(t.commission for t in trades)
        
        if winning_trades:
            self.gross_profit = sum(t.net_pnl for t in winning_trades)
            self.largest_win = max(t.net_pnl for t in winning_trades)
            self.avg_win = self.gross_profit / len(winning_trades)
        
        if losing_trades:
            self.gross_loss = abs(sum(t.net_pnl for t in losing_trades))
            self.largest_loss = min(t.net_pnl for t in losing_trades)
            self.avg_loss = self.gross_loss / len(losing_trades)
        
        self.win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        self.profit_factor = self.gross_profit / self.gross_loss if self.gross_loss > 0 else float('inf')

if __name__ == "__main__":
    # Test models
    signal = TradingSignal(
        symbol="AAPL",
        signal_type=SignalType.MOMENTUM_BREAKOUT,
        timestamp=datetime.now(),
        confidence=0.85,
        entry_price=150.00,
        target_price=151.00,
        stop_loss_price=149.50,
        expected_profit=1.00,
        risk_reward_ratio=2.0
    )
    
    print("Signal created:", signal.to_dict())
    
    trade = Trade(
        symbol="AAPL",
        side=OrderSide.BUY,
        quantity=10,
        entry_price=150.00,
        strategy="Momentum Breakout"
    )
    
    trade.close_trade(151.00, datetime.now(), "Target Profit", 1.00)
    print("Trade completed:", trade.to_dict())
