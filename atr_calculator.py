"""
ATR (Average True Range) Calculator for Volatility-Adjusted Trading
Calculates dynamic stops and position sizes based on stock volatility.
"""

import pandas as pd
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from config import config
from data_provider import data_provider
from logger import log_info, log_debug, log_error

class ATRCalculator:
    """Calculate ATR and volatility-adjusted trading parameters"""
    
    def __init__(self):
        self.atr_cache = {}  # Cache ATR values to avoid recalculation
        self.cache_expiry = {}  # Track when cache expires
        self.cache_duration = 300  # Cache for 5 minutes
    
    def calculate_true_range(self, high: float, low: float, prev_close: float) -> float:
        """Calculate True Range for a single period"""
        try:
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            return max(tr1, tr2, tr3)
        except Exception as e:
            log_error(f"Error calculating true range: {e}")
            return 0.0
    
    def calculate_atr(self, symbol: str, period: int = None) -> Optional[float]:
        """Calculate ATR for a symbol"""
        try:
            if period is None:
                period = config.trading.atr_period
            
            # Check cache first
            cache_key = f"{symbol}_{period}"
            now = datetime.now()
            
            if (cache_key in self.atr_cache and 
                cache_key in self.cache_expiry and 
                now < self.cache_expiry[cache_key]):
                log_debug(f"Using cached ATR for {symbol}: {self.atr_cache[cache_key]:.4f}")
                return self.atr_cache[cache_key]
            
            # Get historical data (need period + 1 for previous close)
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=period + 10)  # Extra buffer
            
            historical_data = data_provider.get_historical_data(
                symbol, 
                start_time, 
                end_time, 
                timeframe='1min'
            )
            
            if not historical_data or len(historical_data) < period + 1:
                log_debug(f"Insufficient data for ATR calculation: {symbol}")
                return None
            
            # Calculate True Range for each period
            true_ranges = []
            for i in range(1, len(historical_data)):
                current = historical_data[i]
                previous = historical_data[i-1]
                
                tr = self.calculate_true_range(
                    current['high'],
                    current['low'], 
                    previous['close']
                )
                true_ranges.append(tr)
            
            if len(true_ranges) < period:
                log_debug(f"Not enough true ranges for ATR: {symbol}")
                return None
            
            # Calculate ATR (average of last 'period' true ranges)
            atr = sum(true_ranges[-period:]) / period
            
            # Cache the result
            self.atr_cache[cache_key] = atr
            self.cache_expiry[cache_key] = now + timedelta(seconds=self.cache_duration)
            
            log_debug(f"Calculated ATR for {symbol}: {atr:.4f}")
            return atr
            
        except Exception as e:
            log_error(f"Error calculating ATR for {symbol}: {e}")
            return None
    
    def calculate_volatility_stops(self, symbol: str, entry_price: float) -> Tuple[float, float]:
        """Calculate ATR-based stop loss and initial trailing stop"""
        try:
            atr = self.calculate_atr(symbol)
            
            if atr is None or atr <= 0:
                # Fallback to fixed stops if ATR unavailable
                stop_loss = entry_price - config.trading.stop_loss_dollars
                trailing_stop = entry_price - config.trading.trailing_stop_dollars
                log_debug(f"Using fallback stops for {symbol}: SL={stop_loss:.2f}, TS={trailing_stop:.2f}")
                return stop_loss, trailing_stop
            
            # Calculate ATR-based stops
            stop_loss = entry_price - (config.trading.stop_loss_atr_multiplier * atr)
            trailing_stop = entry_price - (config.trading.trailing_stop_atr_multiplier * atr)
            
            # Ensure stops are reasonable (not negative or too close)
            min_stop_distance = 0.01  # Minimum 1 cent
            stop_loss = max(stop_loss, entry_price - (entry_price * 0.1))  # Max 10% stop
            stop_loss = min(stop_loss, entry_price - min_stop_distance)
            
            trailing_stop = max(trailing_stop, entry_price - (entry_price * 0.08))  # Max 8% trailing
            trailing_stop = min(trailing_stop, entry_price - min_stop_distance)
            
            log_info(f"ATR stops for {symbol}: Entry=${entry_price:.2f}, ATR=${atr:.4f}, "
                    f"SL=${stop_loss:.2f}, TS=${trailing_stop:.2f}")
            
            return stop_loss, trailing_stop
            
        except Exception as e:
            log_error(f"Error calculating volatility stops for {symbol}: {e}")
            # Return fallback stops
            return (entry_price - config.trading.stop_loss_dollars, 
                   entry_price - config.trading.trailing_stop_dollars)
    
    def calculate_position_size(self, symbol: str, entry_price: float, stop_loss: float) -> int:
        """Calculate position size for fixed risk per trade"""
        try:
            risk_per_trade = config.trading.risk_per_trade_dollars
            stop_distance = entry_price - stop_loss
            
            if stop_distance <= 0:
                log_error(f"Invalid stop distance for {symbol}: {stop_distance}")
                return 0
            
            # Calculate shares for fixed risk
            shares = risk_per_trade / stop_distance
            shares = int(shares)  # Round down to whole shares
            
            # Ensure minimum position
            if shares < 1:
                shares = 1
            
            # Check position value constraints
            position_value = shares * entry_price
            
            # Respect maximum position size
            if position_value > config.trading.max_position_size:
                shares = int(config.trading.max_position_size / entry_price)
            
            # Ensure minimum position value
            min_position_value = 100  # $100 minimum
            if position_value < min_position_value:
                shares = max(1, int(min_position_value / entry_price))
            
            final_position_value = shares * entry_price
            actual_risk = shares * stop_distance
            
            log_info(f"ATR position sizing for {symbol}: {shares} shares, "
                    f"value=${final_position_value:.0f}, risk=${actual_risk:.2f}")
            
            return shares
            
        except Exception as e:
            log_error(f"Error calculating position size for {symbol}: {e}")
            return 0
    
    def update_trailing_stop(self, symbol: str, current_price: float, highest_price: float) -> float:
        """Update trailing stop based on current ATR"""
        try:
            atr = self.calculate_atr(symbol)
            
            if atr is None or atr <= 0:
                # Fallback to fixed trailing stop
                return highest_price - config.trading.trailing_stop_dollars
            
            # Calculate ATR-based trailing stop
            new_trailing_stop = highest_price - (config.trading.trailing_stop_atr_multiplier * atr)
            
            # Ensure trailing stop is reasonable
            min_distance = 0.01
            max_distance = highest_price * 0.1  # Max 10% trailing
            
            new_trailing_stop = max(new_trailing_stop, highest_price - max_distance)
            new_trailing_stop = min(new_trailing_stop, highest_price - min_distance)
            
            log_debug(f"Updated trailing stop for {symbol}: ${new_trailing_stop:.2f} "
                     f"(ATR=${atr:.4f}, High=${highest_price:.2f})")
            
            return new_trailing_stop
            
        except Exception as e:
            log_error(f"Error updating trailing stop for {symbol}: {e}")
            return highest_price - config.trading.trailing_stop_dollars
    
    def get_atr_summary(self, symbols: List[str]) -> Dict[str, float]:
        """Get ATR values for multiple symbols"""
        atr_values = {}
        for symbol in symbols:
            atr = self.calculate_atr(symbol)
            if atr is not None:
                atr_values[symbol] = atr
        return atr_values
    
    def clear_cache(self):
        """Clear ATR cache"""
        self.atr_cache.clear()
        self.cache_expiry.clear()
        log_info("ATR cache cleared")

# Global ATR calculator instance
atr_calculator = ATRCalculator()

if __name__ == "__main__":
    # Test ATR calculation
    test_symbols = ['AAPL', 'TSLA', 'MSFT']
    
    print("Testing ATR Calculator...")
    for symbol in test_symbols:
        atr = atr_calculator.calculate_atr(symbol)
        if atr:
            print(f"{symbol}: ATR = ${atr:.4f}")
            
            # Test stops calculation
            entry_price = 150.00
            stop_loss, trailing_stop = atr_calculator.calculate_volatility_stops(symbol, entry_price)
            shares = atr_calculator.calculate_position_size(symbol, entry_price, stop_loss)
            
            print(f"  Entry: ${entry_price:.2f}")
            print(f"  Stop Loss: ${stop_loss:.2f}")
            print(f"  Trailing Stop: ${trailing_stop:.2f}")
            print(f"  Position Size: {shares} shares")
            print(f"  Position Value: ${shares * entry_price:.0f}")
            print(f"  Risk: ${shares * (entry_price - stop_loss):.2f}")
            print()
        else:
            print(f"{symbol}: ATR calculation failed")
    
    print("ATR Calculator test completed.")
