# Core dependencies
pandas>=2.0.0
numpy>=1.24.0
requests>=2.31.0
python-dotenv>=1.0.0

# Technical Analysis
pandas-ta>=0.3.14b
ta>=0.10.2

# Trading APIs
alpaca-py>=0.8.0
alpaca-trade-api>=3.0.0

# Database (sqlite3 is built into Python)

# GUI (tkinter is built into Python)
customtkinter>=5.2.0
matplotlib>=3.7.0
plotly>=5.15.0

# Async and HTTP
aiohttp>=3.8.0

# Utilities
schedule>=1.2.0
pytz>=2023.3

# Data validation
pydantic>=2.0.0

# Optional: Advanced features
scikit-learn>=1.3.0  # For ML-based signals
websocket-client>=1.6.0  # For real-time data
