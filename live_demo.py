"""
Live demo of the MassiveScan trading bot.
Shows real-time scanning and signal detection.
"""

import asyncio
import time
from datetime import datetime
from scanner import scanner
from data_provider import data_provider
from trade_manager import trade_manager
from risk_manager import risk_manager

async def live_scanning_demo():
    """Live demonstration of the scanning system"""
    print("=" * 60)
    print("🚀 MASSIVESCAN LIVE DEMO")
    print("Target: $50 Daily Profit through Micro-Trading")
    print("=" * 60)
    
    # Check market status
    market_open = data_provider.is_market_open()
    print(f"Market Status: {'OPEN' if market_open else 'CLOSED'}")
    print(f"Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not market_open:
        print("⚠️  Market is closed - this is a simulation")
        scanner.enable_demo_mode()
    
    print("\n📊 SCANNING CONFIGURATION:")
    print(f"  Target profit per trade: $1.00")
    print(f"  Stop loss per trade: $0.50")
    print(f"  Expected trades for $50: 50 trades")
    print(f"  Scan interval: 30 seconds")
    
    print("\n🔍 STARTING LIVE SCANNING...")
    print("Press Ctrl+C to stop\n")
    
    scan_count = 0
    total_signals = 0
    
    try:
        while True:
            scan_count += 1
            start_time = time.time()
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] Scan #{scan_count} - Scanning market...")
            
            # Run a full scan
            signals = scanner.run_full_scan()
            
            scan_duration = time.time() - start_time
            total_signals += len(signals)
            
            print(f"  ✅ Found {len(signals)} signals in {scan_duration:.1f}s")
            
            # Show top signals
            if signals:
                print("  🎯 TOP SIGNALS:")
                for i, signal in enumerate(signals[:3]):  # Show top 3
                    print(f"    {i+1}. {signal.symbol}: {signal.signal_type.value}")
                    print(f"       Confidence: {signal.confidence:.1%}")
                    print(f"       Entry: ${signal.entry_price:.2f} → Target: ${signal.target_price:.2f}")
                    print(f"       Expected Profit: ${signal.expected_profit:.2f}")
                
                # Simulate trade execution for demo
                if len(signals) > 0:
                    top_signal = signals[0]
                    print(f"  💰 WOULD EXECUTE: {top_signal.symbol} - {top_signal.signal_type.value}")
                    print(f"     Risk/Reward: {top_signal.risk_reward_ratio:.1f}:1")
            else:
                print("  ⚪ No signals found this scan")
            
            # Show statistics
            stats = scanner.get_scan_statistics()
            avg_signals_per_scan = total_signals / scan_count if scan_count > 0 else 0
            
            print(f"  📈 STATS: {stats['total_scans']} total scans, {total_signals} total signals")
            print(f"     Average: {avg_signals_per_scan:.1f} signals per scan")
            
            # Show progress toward $50 goal
            estimated_daily_trades = avg_signals_per_scan * (8 * 60 / 0.5)  # 8 hours, scan every 30s
            estimated_daily_profit = estimated_daily_trades * 1.0 * 0.65  # 65% win rate
            
            print(f"  🎯 PROJECTION: {estimated_daily_profit:.0f} potential daily profit")
            
            print(f"  ⏱️  Next scan in 30 seconds...\n")
            
            # Wait 30 seconds (or until interrupted)
            await asyncio.sleep(30)
            
    except KeyboardInterrupt:
        print("\n\n🛑 Demo stopped by user")
        print(f"\nFINAL STATS:")
        print(f"  Total scans: {scan_count}")
        print(f"  Total signals: {total_signals}")
        print(f"  Average signals per scan: {total_signals/scan_count:.1f}")
        print(f"  Scanner is working perfectly! ✅")

def quick_demo():
    """Quick demo showing the scanner working"""
    print("🚀 QUICK SCANNER DEMO")
    print("=" * 30)
    
    # Check market
    market_open = data_provider.is_market_open()
    print(f"Market: {'OPEN' if market_open else 'CLOSED'}")
    
    if not market_open:
        scanner.enable_demo_mode()
    
    print("\n1. Getting stock universe...")
    symbols = scanner.get_scannable_universe()
    print(f"   ✅ Found {len(symbols)} stocks to scan")
    
    print("\n2. Running market scan...")
    start_time = time.time()
    signals = scanner.run_full_scan()
    duration = time.time() - start_time
    
    print(f"   ✅ Scan completed in {duration:.1f}s")
    print(f"   🎯 Found {len(signals)} trading signals")
    
    if signals:
        print("\n3. Top signals found:")
        for i, signal in enumerate(signals[:5]):
            print(f"   {i+1}. {signal.symbol}: {signal.signal_type.value} ({signal.confidence:.1%})")
    
    print(f"\n✅ Scanner is working! Ready for live trading.")
    print(f"💰 Potential for $50 daily profit with {len(signals)} signals found")

if __name__ == "__main__":
    print("Choose demo mode:")
    print("1. Quick Demo (one scan)")
    print("2. Live Demo (continuous scanning)")
    
    try:
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            quick_demo()
        elif choice == "2":
            asyncio.run(live_scanning_demo())
        else:
            print("Invalid choice, running quick demo...")
            quick_demo()
            
    except KeyboardInterrupt:
        print("\n\nDemo cancelled by user")
    except Exception as e:
        print(f"\nDemo error: {e}")
        
    print("\nTo run the full bot:")
    print("  python gui.py        # Desktop interface")
    print("  python main.py       # Command line")
    print("  python run_bot.py    # Easy launcher")
