"""
AI Engine for MASSIVESCAN Trading Bot
Enhances profits through machine learning and intelligent decision making.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pickle
import os
from dataclasses import dataclass

from config import config
from logger import log_info, log_debug, log_error
from data_provider import data_provider

@dataclass
class AISignal:
    """Enhanced signal with AI confidence and predictions"""
    symbol: str
    base_confidence: float
    ai_confidence: float
    profit_prediction: float
    market_regime: str
    risk_score: float
    optimal_hold_time: int  # minutes
    enhanced_confidence: float

class MarketRegimeDetector:
    """Detect current market conditions using AI"""
    
    def __init__(self):
        self.regimes = ['TRENDING_UP', 'TRENDING_DOWN', 'SIDEWAYS', 'VOLATILE', 'BREAKOUT']
        self.current_regime = 'SIDEWAYS'
        self.regime_confidence = 0.5
        
    def detect_regime(self, market_data: Dict) -> Tuple[str, float]:
        """Detect current market regime using multiple indicators"""
        try:
            # Get SPY data for market context
            spy_data = data_provider.get_historical_data('SPY', 
                                                       datetime.now() - timedelta(hours=2),
                                                       datetime.now(),
                                                       '1min')
            
            if not spy_data or len(spy_data) < 20:
                return self.current_regime, self.regime_confidence
            
            # Calculate regime indicators
            prices = [d['close'] for d in spy_data[-20:]]
            volumes = [d['volume'] for d in spy_data[-20:]]
            
            # Trend detection
            price_change = (prices[-1] - prices[0]) / prices[0]
            trend_strength = abs(price_change)
            
            # Volatility detection
            price_std = np.std(prices) / np.mean(prices)
            volume_surge = volumes[-1] / np.mean(volumes[:-1]) if len(volumes) > 1 else 1.0
            
            # AI regime classification
            if trend_strength > 0.015 and price_change > 0:
                regime = 'TRENDING_UP'
                confidence = min(0.9, 0.6 + trend_strength * 10)
            elif trend_strength > 0.015 and price_change < 0:
                regime = 'TRENDING_DOWN' 
                confidence = min(0.9, 0.6 + trend_strength * 10)
            elif price_std > 0.02 and volume_surge > 2.0:
                regime = 'VOLATILE'
                confidence = min(0.85, 0.5 + price_std * 15)
            elif volume_surge > 3.0 and trend_strength > 0.01:
                regime = 'BREAKOUT'
                confidence = min(0.9, 0.7 + volume_surge * 0.1)
            else:
                regime = 'SIDEWAYS'
                confidence = 0.6
            
            self.current_regime = regime
            self.regime_confidence = confidence
            
            log_debug(f"Market regime: {regime} ({confidence:.1%} confidence)")
            return regime, confidence
            
        except Exception as e:
            log_error(f"Error detecting market regime: {e}")
            return self.current_regime, 0.5

class ProfitPredictor:
    """AI model to predict trade profitability"""
    
    def __init__(self):
        self.model_weights = {
            'confidence': 0.3,
            'volume_ratio': 0.2,
            'price_momentum': 0.25,
            'market_regime': 0.15,
            'time_of_day': 0.1
        }
        
    def predict_profit(self, signal_data: Dict, market_regime: str) -> Tuple[float, int]:
        """Predict expected profit and optimal hold time"""
        try:
            # Extract features
            confidence = signal_data.get('confidence', 0.5)
            volume_ratio = signal_data.get('volume_ratio', 1.0)
            price_momentum = signal_data.get('momentum', 0.0)
            
            # Time of day factor (market open = higher volatility)
            current_hour = datetime.now().hour
            time_factor = 1.0
            if 9 <= current_hour <= 10:  # Market open
                time_factor = 1.3
            elif 15 <= current_hour <= 16:  # Market close
                time_factor = 1.2
            elif 12 <= current_hour <= 13:  # Lunch time
                time_factor = 0.8
            
            # Market regime multipliers
            regime_multipliers = {
                'TRENDING_UP': 1.4,
                'TRENDING_DOWN': 1.2,
                'BREAKOUT': 1.6,
                'VOLATILE': 1.1,
                'SIDEWAYS': 0.9
            }
            
            regime_mult = regime_multipliers.get(market_regime, 1.0)
            
            # AI profit prediction
            base_profit = config.trading.target_profit_dollars
            
            profit_multiplier = (
                confidence * self.model_weights['confidence'] +
                min(volume_ratio / 2.0, 1.0) * self.model_weights['volume_ratio'] +
                min(abs(price_momentum) * 100, 1.0) * self.model_weights['price_momentum'] +
                regime_mult * self.model_weights['market_regime'] +
                time_factor * self.model_weights['time_of_day']
            )
            
            predicted_profit = base_profit * profit_multiplier
            
            # Optimal hold time prediction
            if market_regime in ['TRENDING_UP', 'TRENDING_DOWN']:
                hold_time = int(8 + confidence * 12)  # 8-20 minutes
            elif market_regime == 'BREAKOUT':
                hold_time = int(3 + confidence * 7)   # 3-10 minutes
            elif market_regime == 'VOLATILE':
                hold_time = int(2 + confidence * 4)   # 2-6 minutes
            else:  # SIDEWAYS
                hold_time = int(5 + confidence * 5)   # 5-10 minutes
            
            log_debug(f"AI profit prediction: ${predicted_profit:.2f}, hold: {hold_time}min")
            return predicted_profit, hold_time
            
        except Exception as e:
            log_error(f"Error predicting profit: {e}")
            return config.trading.target_profit_dollars, 5

class SignalEnhancer:
    """Enhance trading signals with AI analysis"""
    
    def __init__(self):
        self.regime_detector = MarketRegimeDetector()
        self.profit_predictor = ProfitPredictor()
        self.learning_data = []
        
    def enhance_signal(self, base_signal, market_data: Dict) -> AISignal:
        """Enhance a trading signal with AI analysis"""
        try:
            # Detect market regime
            regime, regime_confidence = self.regime_detector.detect_regime(market_data)
            
            # Prepare signal data for AI analysis
            signal_data = {
                'confidence': base_signal.confidence,
                'volume_ratio': getattr(base_signal, 'volume_ratio', 1.0),
                'momentum': getattr(base_signal, 'momentum', 0.0),
                'symbol': base_signal.symbol
            }
            
            # Predict profit and hold time
            profit_prediction, hold_time = self.profit_predictor.predict_profit(signal_data, regime)
            
            # Calculate AI confidence boost
            ai_confidence_boost = self._calculate_ai_boost(base_signal, regime, regime_confidence)
            ai_confidence = min(0.95, base_signal.confidence + ai_confidence_boost)
            
            # Calculate risk score
            risk_score = self._calculate_risk_score(base_signal, regime)
            
            # Enhanced confidence (combines base + AI + regime)
            enhanced_confidence = (
                base_signal.confidence * 0.4 +
                ai_confidence * 0.4 +
                regime_confidence * 0.2
            )
            
            enhanced_signal = AISignal(
                symbol=base_signal.symbol,
                base_confidence=base_signal.confidence,
                ai_confidence=ai_confidence,
                profit_prediction=profit_prediction,
                market_regime=regime,
                risk_score=risk_score,
                optimal_hold_time=hold_time,
                enhanced_confidence=enhanced_confidence
            )
            
            log_info(f"AI enhanced {base_signal.symbol}: "
                    f"{base_signal.confidence:.1%} → {enhanced_confidence:.1%}, "
                    f"profit: ${profit_prediction:.2f}, regime: {regime}")
            
            return enhanced_signal
            
        except Exception as e:
            log_error(f"Error enhancing signal: {e}")
            # Return basic enhanced signal on error
            return AISignal(
                symbol=base_signal.symbol,
                base_confidence=base_signal.confidence,
                ai_confidence=base_signal.confidence,
                profit_prediction=config.trading.target_profit_dollars,
                market_regime='SIDEWAYS',
                risk_score=0.5,
                optimal_hold_time=5,
                enhanced_confidence=base_signal.confidence
            )
    
    def _calculate_ai_boost(self, signal, regime: str, regime_confidence: float) -> float:
        """Calculate AI confidence boost based on market conditions"""
        boost = 0.0
        
        # Regime-based boosts
        if regime == 'TRENDING_UP' and hasattr(signal, 'signal_type'):
            if 'momentum' in signal.signal_type.value.lower():
                boost += 0.15 * regime_confidence
        elif regime == 'BREAKOUT':
            boost += 0.1 * regime_confidence
        elif regime == 'VOLATILE' and signal.confidence > 0.8:
            boost += 0.05 * regime_confidence
        
        # Time-based boosts
        hour = datetime.now().hour
        if 9 <= hour <= 10 or 15 <= hour <= 16:  # High activity periods
            boost += 0.05
        
        return min(boost, 0.2)  # Cap at 20% boost
    
    def _calculate_risk_score(self, signal, regime: str) -> float:
        """Calculate risk score (0 = low risk, 1 = high risk)"""
        base_risk = 1.0 - signal.confidence
        
        regime_risk = {
            'TRENDING_UP': 0.3,
            'TRENDING_DOWN': 0.4,
            'BREAKOUT': 0.5,
            'VOLATILE': 0.7,
            'SIDEWAYS': 0.6
        }
        
        return min(1.0, base_risk * 0.7 + regime_risk.get(regime, 0.5) * 0.3)

class AITradingEngine:
    """Main AI engine for enhanced trading decisions"""
    
    def __init__(self):
        self.signal_enhancer = SignalEnhancer()
        self.performance_tracker = {}
        self.learning_enabled = True
        
    def process_signals(self, base_signals: List, market_data: Dict) -> List[AISignal]:
        """Process and enhance multiple signals with AI"""
        enhanced_signals = []
        
        for signal in base_signals:
            try:
                enhanced = self.signal_enhancer.enhance_signal(signal, market_data)
                enhanced_signals.append(enhanced)
            except Exception as e:
                log_error(f"Error processing signal {signal.symbol}: {e}")
        
        # Sort by enhanced confidence
        enhanced_signals.sort(key=lambda x: x.enhanced_confidence, reverse=True)
        
        log_info(f"AI processed {len(enhanced_signals)} signals, "
                f"top confidence: {enhanced_signals[0].enhanced_confidence:.1%}" 
                if enhanced_signals else "No signals processed")
        
        return enhanced_signals
    
    def should_trade_signal(self, ai_signal: AISignal) -> Tuple[bool, str]:
        """AI decision on whether to trade a signal"""
        try:
            # AI trading criteria
            min_confidence = config.trading.min_signal_confidence
            
            if ai_signal.enhanced_confidence < min_confidence:
                return False, f"Low AI confidence ({ai_signal.enhanced_confidence:.1%})"
            
            if ai_signal.risk_score > 0.8:
                return False, f"High risk score ({ai_signal.risk_score:.1%})"
            
            if ai_signal.profit_prediction < 1.0:
                return False, f"Low profit prediction (${ai_signal.profit_prediction:.2f})"
            
            # Market regime checks
            if ai_signal.market_regime == 'VOLATILE' and ai_signal.enhanced_confidence < 0.8:
                return False, "Volatile market requires higher confidence"
            
            return True, f"AI approved: {ai_signal.enhanced_confidence:.1%} confidence"
            
        except Exception as e:
            log_error(f"Error in AI trading decision: {e}")
            return False, "AI decision error"
    
    def get_ai_summary(self) -> Dict:
        """Get AI engine performance summary"""
        regime, regime_conf = self.signal_enhancer.regime_detector.current_regime, \
                             self.signal_enhancer.regime_detector.regime_confidence
        
        return {
            'market_regime': regime,
            'regime_confidence': regime_conf,
            'ai_enabled': True,
            'learning_enabled': self.learning_enabled,
            'signals_processed': len(self.performance_tracker)
        }

# Global AI engine instance
ai_engine = AITradingEngine()

if __name__ == "__main__":
    print("🤖 Testing AI Trading Engine...")
    
    # Test market regime detection
    regime, conf = ai_engine.signal_enhancer.regime_detector.detect_regime({})
    print(f"Market Regime: {regime} ({conf:.1%} confidence)")
    
    # Test AI summary
    summary = ai_engine.get_ai_summary()
    print(f"AI Summary: {summary}")
    
    print("✅ AI Engine ready for enhanced trading!")
