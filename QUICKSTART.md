# 🚀 MassiveScan Trading Bot - Quick Start Guide

**Target: $50 Daily Profit through Automated Micro-Trading**

## ⚡ 5-Minute Setup

### 1. Install & Setup
```bash
# Clone or download the project
cd MassiveScan

# Run the automated setup
python setup.py
```

### 2. Get FREE API Keys

**Financial Modeling Prep (Market Data)**
- Visit: https://financialmodelingprep.com/developer/docs
- Sign up for FREE account
- Get your API key (250 requests/day free)

**Alpaca Trading (Broker)**
- Visit: https://alpaca.markets/
- Sign up for FREE account
- Go to "Paper Trading" section
- Generate API keys

### 3. Configure API Keys
Edit the `.env` file:
```env
FMP_API_KEY=your_actual_fmp_key_here
ALPACA_API_KEY=your_actual_alpaca_key_here
ALPACA_SECRET_KEY=your_actual_alpaca_secret_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets
```

### 4. Run the Bot
```bash
# Easy launcher (recommended)
python run_bot.py

# Or directly:
python gui.py        # Desktop interface
python main.py       # Command line
```

## 🎯 Trading Strategy

### Daily Target: $50 Profit
- **Per Trade**: $1 profit target
- **Stop Loss**: $0.50 maximum loss
- **Volume**: 50-100 trades per day
- **Risk**: $25 maximum daily loss

### How It Works
1. **Scans 1000+ stocks** every 30 seconds
2. **Detects signals**: Momentum, RSI, Volume surges, VWAP bounces
3. **Enters trades** with strict $1 profit / $0.50 loss targets
4. **Exits immediately** when targets hit
5. **Repeats** throughout trading day

## 📊 Expected Performance

### Conservative Estimates
- **Daily Goal**: $50 profit
- **Win Rate**: 60-70%
- **Trade Duration**: 1-30 minutes average
- **Capital Required**: $2,000-5,000 recommended
- **Monthly Target**: ~$1,000 (20 trading days)

### Risk Management
- **Maximum Daily Loss**: $25
- **Maximum Concurrent Positions**: 10
- **Position Size**: Auto-calculated based on risk
- **Trading Hours**: Market hours only
- **Paper Trading**: Default (safe testing)

## 🖥️ Desktop Interface

### Main Dashboard
- **Real-time P&L**: Live profit/loss tracking
- **Active Trades**: Current positions with P&L
- **Account Info**: Buying power, portfolio value
- **Risk Metrics**: Daily limits, consecutive losses

### Controls
- **Start/Stop Bot**: One-click trading control
- **Close All Positions**: Emergency exit
- **Settings**: Adjust trading parameters
- **Export**: Trade history and performance

## ⚙️ Key Settings

### Trading Parameters
```python
target_profit_dollars = 1.00      # $1 per trade
stop_loss_dollars = 0.50          # $0.50 max loss
max_daily_trades = 100            # 100 trades max
max_daily_loss = 25.00           # $25 max daily loss
max_concurrent_positions = 10     # 10 positions max
```

### Stock Filters
```python
min_volume = 500000              # 500K volume minimum
min_price = 5.00                 # $5 minimum price
max_price = 200.00               # $200 maximum price
min_market_cap = *********       # $500M market cap
```

## 🛡️ Safety Features

### Built-in Protection
- **Paper Trading Default**: No real money at risk
- **Hard Stop Losses**: Automatic $0.50 loss limit
- **Daily Loss Limits**: Trading halts at $25 loss
- **Position Limits**: Maximum 10 concurrent trades
- **Market Hours Only**: No after-hours trading

### Monitoring
- **Real-time Alerts**: Risk warnings and notifications
- **Detailed Logging**: Complete trade history
- **Performance Tracking**: Win rate, P&L analysis
- **Manual Override**: Emergency controls

## 📈 Getting Started Tips

### Day 1: Setup & Test
1. Run setup and configure API keys
2. Start with paper trading (default)
3. Watch the bot for 1-2 hours
4. Review trade logs and performance

### Day 2-7: Optimization
1. Monitor win rate and average profit
2. Adjust settings if needed
3. Test different market conditions
4. Build confidence in the system

### Week 2+: Scale Up
1. Consider live trading (if comfortable)
2. Start with small position sizes
3. Monitor closely for first week
4. Gradually increase if performing well

## 🚨 Important Reminders

### Before Live Trading
- ✅ Test thoroughly with paper trading
- ✅ Understand all settings and controls
- ✅ Have emergency stop plan
- ✅ Never risk more than you can afford to lose
- ✅ Monitor the bot closely initially

### Risk Warnings
- **Past performance ≠ future results**
- **All trading involves risk of loss**
- **Start small and scale gradually**
- **This is educational software**
- **Trade at your own risk**

## 🆘 Troubleshooting

### Common Issues
1. **"API key not found"** → Check .env file configuration
2. **"No trades executing"** → Verify market is open, check filters
3. **"Connection failed"** → Check internet, API key validity
4. **"Import errors"** → Run `python setup.py` again

### Getting Help
- Check `logs/` directory for detailed error messages
- Review `README.md` for comprehensive documentation
- Verify API keys are correct and active
- Test with paper trading first

## 📞 Support

### Documentation
- `README.md` - Complete documentation
- `config.py` - All configuration options
- `logs/` - Detailed operation logs

### Community
- Test with paper trading community
- Share experiences and optimizations
- Report bugs and improvements

---

## 🎉 Ready to Start?

```bash
# Run the easy launcher
python run_bot.py

# Choose option 1 for Desktop GUI
# Click "Start Bot" and watch it work!
```

**Remember**: Start with paper trading, test thoroughly, and never risk more than you can afford to lose.

**Target**: $50 daily profit through consistent, low-risk micro-trading! 🚀
