"""
Trade management module for the MassiveScan trading bot.
Handles trade execution, monitoring, and exit logic.
"""

import time
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import uuid

from config import config
from models import Trade, Order, Position, TradingSignal, OrderSide, OrderType, OrderStatus, TradeStatus
from broker import broker
from risk_manager import risk_manager
from data_provider import data_provider
from database import db
from logger import log_info, log_error, log_debug, log_warning, log_trade_entry, log_trade_exit

class TradeManager:
    """Trade execution and management system"""
    
    def __init__(self):
        self.active_trades = {}  # trade_id -> Trade
        self.pending_orders = {}  # order_id -> Order
        self.monitoring_active = False
        
        # Performance tracking
        self.trades_today = 0
        self.successful_trades = 0
        self.failed_trades = 0
        
        # Load existing open trades
        self._load_open_trades()
    
    def _load_open_trades(self):
        """Load open trades from database"""
        try:
            open_trades = db.get_open_trades()
            for trade in open_trades:
                self.active_trades[trade.id] = trade
            
            log_info(f"Loaded {len(open_trades)} open trades")
            
        except Exception as e:
            log_error(f"Failed to load open trades: {e}")
    
    def execute_signal(self, signal: TradingSignal) -> Optional[Trade]:
        """Execute a trading signal"""
        try:
            # Check if we can place the trade
            can_trade, reason = risk_manager.can_place_trade(signal)
            if not can_trade:
                log_warning(f"Trade rejected for {signal.symbol}: {reason}")
                return None
            
            # Calculate position size
            position_size = risk_manager.calculate_position_size(signal)
            if position_size <= 0:
                log_warning(f"Invalid position size for {signal.symbol}: {position_size}")
                return None
            
            # Determine order side based on signal
            order_side = OrderSide.BUY  # Default to buy for most signals
            if signal.signal_type.value in ['rsi_overbought']:
                order_side = OrderSide.SELL
            
            # Place entry order
            entry_order = broker.place_market_order(signal.symbol, order_side, position_size)
            if not entry_order:
                log_error(f"Failed to place entry order for {signal.symbol}")
                return None
            
            # Create trade record
            trade_id = str(uuid.uuid4())
            trade = Trade(
                id=trade_id,
                symbol=signal.symbol,
                side=order_side,
                quantity=position_size,
                entry_price=signal.entry_price,  # Will be updated when order fills
                entry_time=datetime.now(),
                status=TradeStatus.OPEN,
                strategy=signal.signal_type.value,
                entry_order_id=entry_order.id,
                signal_data=signal.to_dict()
            )
            
            # Save trade to database
            db.save_trade(trade)
            
            # Add to active trades
            self.active_trades[trade_id] = trade
            self.pending_orders[entry_order.id] = entry_order
            
            # Update counters
            self.trades_today += 1
            
            log_trade_entry(signal.symbol, order_side.value, position_size, signal.entry_price, signal.signal_type.value)
            log_info(f"Trade executed: {trade_id} - {signal.symbol} {order_side.value} {position_size}")
            
            return trade
            
        except Exception as e:
            log_error(f"Failed to execute signal for {signal.symbol}: {e}")
            return None
    
    def monitor_trade(self, trade: Trade) -> bool:
        """Monitor a single trade for exit conditions"""
        try:
            # Get current price
            current_price = data_provider.get_real_time_price(trade.symbol)
            if not current_price:
                log_debug(f"Could not get current price for {trade.symbol}")
                return False
            
            # Check if entry order is filled
            if trade.entry_order_id and trade.entry_order_id in self.pending_orders:
                entry_order = broker.get_order_status(trade.entry_order_id)
                if entry_order and entry_order.status == OrderStatus.FILLED:
                    # Update trade with actual fill price
                    trade.entry_price = entry_order.filled_price or trade.entry_price
                    trade.quantity = entry_order.filled_quantity or trade.quantity
                    db.save_trade(trade)
                    
                    # Remove from pending orders
                    del self.pending_orders[trade.entry_order_id]
                    
                    log_info(f"Entry order filled: {trade.symbol} @ ${trade.entry_price:.4f}")
            
            # Calculate current P&L
            if trade.side == OrderSide.BUY:
                unrealized_pnl = (current_price - trade.entry_price) * trade.quantity
            else:
                unrealized_pnl = (trade.entry_price - current_price) * trade.quantity
            
            # Check exit conditions
            should_exit, exit_reason = self._check_exit_conditions(trade, current_price, unrealized_pnl)
            
            if should_exit:
                return self._exit_trade(trade, current_price, exit_reason)
            
            return True
            
        except Exception as e:
            log_error(f"Error monitoring trade {trade.id}: {e}")
            return False
    
    def _check_exit_conditions(self, trade: Trade, current_price: float, unrealized_pnl: float) -> tuple[bool, str]:
        """Check if trade should be exited"""
        try:
            # Profit target reached
            if unrealized_pnl >= config.trading.target_profit_dollars:
                return True, "Profit target reached"
            
            # Stop loss triggered
            if unrealized_pnl <= -config.trading.stop_loss_dollars:
                return True, "Stop loss triggered"
            
            # Time-based exit (if trade is open too long)
            trade_duration = datetime.now() - trade.entry_time
            if trade_duration > timedelta(hours=4):  # Close after 4 hours
                return True, "Time limit reached"
            
            # Market close exit
            if not broker.is_market_open():
                return True, "Market closing"
            
            # Risk-based exit
            position_value = abs(trade.quantity * current_price)
            risk_pct = abs(unrealized_pnl) / position_value * 100 if position_value > 0 else 0
            
            if risk_pct > 5:  # Exit if risk exceeds 5%
                return True, "Risk percentage too high"
            
            return False, ""
            
        except Exception as e:
            log_error(f"Error checking exit conditions: {e}")
            return True, "Error in exit check"
    
    def _exit_trade(self, trade: Trade, exit_price: float, exit_reason: str) -> bool:
        """Exit a trade"""
        try:
            # Determine exit order side (opposite of entry)
            exit_side = OrderSide.SELL if trade.side == OrderSide.BUY else OrderSide.BUY
            
            # Place exit order
            exit_order = broker.place_market_order(trade.symbol, exit_side, trade.quantity)
            if not exit_order:
                log_error(f"Failed to place exit order for {trade.symbol}")
                return False
            
            # Update trade
            trade.exit_price = exit_price
            trade.exit_time = datetime.now()
            trade.exit_reason = exit_reason
            trade.exit_order_id = exit_order.id
            trade.close_trade(exit_price, trade.exit_time, exit_reason)
            
            # Save updated trade
            db.save_trade(trade)
            
            # Remove from active trades
            if trade.id in self.active_trades:
                del self.active_trades[trade.id]
            
            # Update risk manager
            risk_manager.update_trade_metrics(trade)
            
            # Update counters
            if trade.net_pnl > 0:
                self.successful_trades += 1
            else:
                self.failed_trades += 1
            
            log_trade_exit(trade.symbol, exit_side.value, trade.quantity, exit_price, trade.net_pnl, exit_reason)
            log_info(f"Trade closed: {trade.id} - {trade.symbol} - P&L: ${trade.net_pnl:.2f}")
            
            return True
            
        except Exception as e:
            log_error(f"Failed to exit trade {trade.id}: {e}")
            return False
    
    def monitor_all_trades(self):
        """Monitor all active trades"""
        if not self.active_trades:
            return
        
        try:
            log_debug(f"Monitoring {len(self.active_trades)} active trades")
            
            # Get current prices for all symbols
            symbols = list(set(trade.symbol for trade in self.active_trades.values()))
            current_prices = data_provider.get_real_time_prices(symbols)
            
            # Monitor each trade
            trades_to_remove = []
            for trade_id, trade in self.active_trades.items():
                try:
                    if trade.symbol in current_prices:
                        current_price = current_prices[trade.symbol]
                        
                        # Calculate P&L
                        if trade.side == OrderSide.BUY:
                            unrealized_pnl = (current_price - trade.entry_price) * trade.quantity
                        else:
                            unrealized_pnl = (trade.entry_price - current_price) * trade.quantity
                        
                        # Check exit conditions
                        should_exit, exit_reason = self._check_exit_conditions(trade, current_price, unrealized_pnl)
                        
                        if should_exit:
                            if self._exit_trade(trade, current_price, exit_reason):
                                trades_to_remove.append(trade_id)
                        else:
                            log_debug(f"Trade {trade.symbol}: P&L ${unrealized_pnl:.2f}")
                    
                except Exception as e:
                    log_error(f"Error monitoring trade {trade_id}: {e}")
            
            # Remove closed trades
            for trade_id in trades_to_remove:
                if trade_id in self.active_trades:
                    del self.active_trades[trade_id]
            
        except Exception as e:
            log_error(f"Error monitoring trades: {e}")
    
    async def start_monitoring(self):
        """Start continuous trade monitoring"""
        self.monitoring_active = True
        log_info("Trade monitoring started")
        
        while self.monitoring_active:
            try:
                if self.active_trades:
                    self.monitor_all_trades()
                
                # Sleep for a short interval
                await asyncio.sleep(5)  # Monitor every 5 seconds
                
            except Exception as e:
                log_error(f"Error in trade monitoring loop: {e}")
                await asyncio.sleep(30)  # Wait longer on error
    
    def stop_monitoring(self):
        """Stop trade monitoring"""
        self.monitoring_active = False
        log_info("Trade monitoring stopped")
    
    def close_all_positions(self, reason: str = "Manual close"):
        """Close all open positions"""
        try:
            log_info(f"Closing all positions: {reason}")
            
            for trade_id, trade in list(self.active_trades.items()):
                current_price = data_provider.get_real_time_price(trade.symbol)
                if current_price:
                    self._exit_trade(trade, current_price, reason)
            
            log_info("All positions closed")
            
        except Exception as e:
            log_error(f"Error closing all positions: {e}")
    
    def get_trade_summary(self) -> Dict[str, Any]:
        """Get trading summary"""
        total_trades = self.successful_trades + self.failed_trades
        win_rate = self.successful_trades / total_trades if total_trades > 0 else 0
        
        # Calculate total P&L from active trades
        active_pnl = 0
        for trade in self.active_trades.values():
            current_price = data_provider.get_real_time_price(trade.symbol)
            if current_price:
                if trade.side == OrderSide.BUY:
                    pnl = (current_price - trade.entry_price) * trade.quantity
                else:
                    pnl = (trade.entry_price - current_price) * trade.quantity
                active_pnl += pnl
        
        return {
            'active_trades': len(self.active_trades),
            'trades_today': self.trades_today,
            'successful_trades': self.successful_trades,
            'failed_trades': self.failed_trades,
            'win_rate': win_rate,
            'active_unrealized_pnl': active_pnl,
            'monitoring_active': self.monitoring_active,
            'pending_orders': len(self.pending_orders)
        }
    
    def get_active_trades_info(self) -> List[Dict[str, Any]]:
        """Get information about active trades"""
        trades_info = []
        
        for trade in self.active_trades.values():
            current_price = data_provider.get_real_time_price(trade.symbol)
            
            if current_price:
                if trade.side == OrderSide.BUY:
                    unrealized_pnl = (current_price - trade.entry_price) * trade.quantity
                else:
                    unrealized_pnl = (trade.entry_price - current_price) * trade.quantity
                
                duration = datetime.now() - trade.entry_time
                
                trades_info.append({
                    'id': trade.id,
                    'symbol': trade.symbol,
                    'side': trade.side.value,
                    'quantity': trade.quantity,
                    'entry_price': trade.entry_price,
                    'current_price': current_price,
                    'unrealized_pnl': unrealized_pnl,
                    'duration_minutes': duration.total_seconds() / 60,
                    'strategy': trade.strategy
                })
        
        return trades_info

# Global trade manager instance
trade_manager = TradeManager()

if __name__ == "__main__":
    # Test trade manager
    from models import TradingSignal, SignalType
    
    # Create test signal
    signal = TradingSignal(
        symbol="AAPL",
        signal_type=SignalType.MOMENTUM_BREAKOUT,
        timestamp=datetime.now(),
        confidence=0.75,
        entry_price=150.00,
        target_price=151.00,
        stop_loss_price=149.50,
        expected_profit=1.00,
        risk_reward_ratio=2.0
    )
    
    print("Trade Manager Test")
    print(f"Active trades: {len(trade_manager.active_trades)}")
    
    # Get trade summary
    summary = trade_manager.get_trade_summary()
    print(f"Trade summary: {summary}")
    
    # Get active trades info
    active_info = trade_manager.get_active_trades_info()
    print(f"Active trades info: {active_info}")
