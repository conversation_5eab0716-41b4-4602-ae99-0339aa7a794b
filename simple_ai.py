"""
Simplified AI Enhancement for MASSIVESCAN
Focuses on core AI features without complex data dependencies.
"""

import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from config import config
from logger import log_info, log_debug, log_error

@dataclass
class SimpleAISignal:
    """Simplified AI-enhanced signal"""
    symbol: str
    base_confidence: float
    ai_confidence: float
    profit_prediction: float
    market_regime: str
    enhanced_confidence: float

class SimpleMarketRegime:
    """Simple market regime detection without external data"""
    
    def __init__(self):
        self.current_regime = 'NORMAL'
        self.regime_confidence = 0.7
        
    def detect_regime(self) -> Tuple[str, float]:
        """Simple regime detection based on time and basic factors"""
        try:
            current_hour = datetime.now().hour
            
            # Time-based regime detection
            if 9 <= current_hour <= 10:
                regime = 'OPENING_VOLATILE'
                confidence = 0.8
            elif 15 <= current_hour <= 16:
                regime = 'CLOSING_ACTIVE'
                confidence = 0.8
            elif 12 <= current_hour <= 13:
                regime = 'LUNCH_QUIET'
                confidence = 0.7
            else:
                regime = 'NORMAL'
                confidence = 0.7
            
            self.current_regime = regime
            self.regime_confidence = confidence
            
            return regime, confidence
            
        except Exception as e:
            log_error(f"Error in simple regime detection: {e}")
            return 'NORMAL', 0.5

class SimpleProfitPredictor:
    """Simple profit prediction without complex data"""
    
    def predict_profit(self, signal_confidence: float, market_regime: str) -> float:
        """Simple profit prediction based on confidence and regime"""
        try:
            base_profit = config.trading.target_profit_dollars
            
            # Confidence multiplier
            confidence_mult = 0.5 + (signal_confidence * 1.5)  # 0.5 to 2.0
            
            # Regime multipliers
            regime_multipliers = {
                'OPENING_VOLATILE': 1.4,
                'CLOSING_ACTIVE': 1.3,
                'NORMAL': 1.0,
                'LUNCH_QUIET': 0.8
            }
            
            regime_mult = regime_multipliers.get(market_regime, 1.0)
            
            predicted_profit = base_profit * confidence_mult * regime_mult
            
            # Cap the prediction
            predicted_profit = min(predicted_profit, config.trading.max_profit_target)
            predicted_profit = max(predicted_profit, 1.0)  # Minimum $1
            
            return predicted_profit
            
        except Exception as e:
            log_error(f"Error predicting profit: {e}")
            return config.trading.target_profit_dollars

class SimpleAIEngine:
    """Simplified AI engine for signal enhancement"""
    
    def __init__(self):
        self.regime_detector = SimpleMarketRegime()
        self.profit_predictor = SimpleProfitPredictor()
        
    def enhance_signal(self, base_signal) -> SimpleAISignal:
        """Enhance a signal with simple AI"""
        try:
            # Detect market regime
            regime, regime_confidence = self.regime_detector.detect_regime()
            
            # Predict profit
            profit_prediction = self.profit_predictor.predict_profit(
                base_signal.confidence, regime
            )
            
            # Calculate AI confidence boost
            ai_boost = self._calculate_simple_boost(base_signal, regime)
            ai_confidence = min(0.95, base_signal.confidence + ai_boost)
            
            # Enhanced confidence (weighted average)
            enhanced_confidence = (
                base_signal.confidence * 0.6 +
                ai_confidence * 0.3 +
                regime_confidence * 0.1
            )
            
            enhanced_signal = SimpleAISignal(
                symbol=base_signal.symbol,
                base_confidence=base_signal.confidence,
                ai_confidence=ai_confidence,
                profit_prediction=profit_prediction,
                market_regime=regime,
                enhanced_confidence=enhanced_confidence
            )
            
            log_debug(f"Simple AI enhanced {base_signal.symbol}: "
                     f"{base_signal.confidence:.1%} → {enhanced_confidence:.1%}")
            
            return enhanced_signal
            
        except Exception as e:
            log_error(f"Error in simple AI enhancement: {e}")
            # Return basic signal on error
            return SimpleAISignal(
                symbol=base_signal.symbol,
                base_confidence=base_signal.confidence,
                ai_confidence=base_signal.confidence,
                profit_prediction=config.trading.target_profit_dollars,
                market_regime='NORMAL',
                enhanced_confidence=base_signal.confidence
            )
    
    def _calculate_simple_boost(self, signal, regime: str) -> float:
        """Calculate simple AI boost"""
        boost = 0.0
        
        # High confidence signals get more boost
        if signal.confidence > 0.8:
            boost += 0.05
        
        # Regime-based boosts
        if regime == 'OPENING_VOLATILE':
            boost += 0.08
        elif regime == 'CLOSING_ACTIVE':
            boost += 0.06
        elif regime == 'NORMAL':
            boost += 0.03
        
        # Time-based boost
        hour = datetime.now().hour
        if 9 <= hour <= 10 or 15 <= hour <= 16:
            boost += 0.02
        
        return min(boost, config.trading.ai_confidence_boost_max)
    
    def should_trade_signal(self, ai_signal: SimpleAISignal) -> Tuple[bool, str]:
        """Simple AI decision on trading"""
        try:
            # Basic criteria
            if ai_signal.enhanced_confidence < config.trading.min_signal_confidence:
                return False, f"Low confidence ({ai_signal.enhanced_confidence:.1%})"
            
            if ai_signal.profit_prediction < 1.0:
                return False, f"Low profit prediction (${ai_signal.profit_prediction:.2f})"
            
            # Regime-based decisions
            if ai_signal.market_regime == 'LUNCH_QUIET' and ai_signal.enhanced_confidence < 0.8:
                return False, "Quiet period requires higher confidence"
            
            return True, f"Simple AI approved ({ai_signal.enhanced_confidence:.1%})"
            
        except Exception as e:
            log_error(f"Error in AI decision: {e}")
            return False, "AI decision error"
    
    def process_signals(self, base_signals: List) -> List[SimpleAISignal]:
        """Process multiple signals with simple AI"""
        enhanced_signals = []
        
        for signal in base_signals:
            try:
                enhanced = self.enhance_signal(signal)
                enhanced_signals.append(enhanced)
            except Exception as e:
                log_error(f"Error processing signal {signal.symbol}: {e}")
        
        # Sort by enhanced confidence
        enhanced_signals.sort(key=lambda x: x.enhanced_confidence, reverse=True)
        
        return enhanced_signals
    
    def get_ai_summary(self) -> Dict:
        """Get simple AI summary"""
        regime, confidence = self.regime_detector.current_regime, self.regime_detector.regime_confidence
        
        return {
            'ai_type': 'Simple AI',
            'market_regime': regime,
            'regime_confidence': confidence,
            'ai_enabled': True,
            'status': 'Active'
        }

# Global simple AI engine
simple_ai = SimpleAIEngine()

if __name__ == "__main__":
    print("🤖 Testing Simple AI Engine...")
    
    # Test regime detection
    regime, conf = simple_ai.regime_detector.detect_regime()
    print(f"Market Regime: {regime} ({conf:.1%} confidence)")
    
    # Test AI summary
    summary = simple_ai.get_ai_summary()
    print(f"AI Summary: {summary}")
    
    print("✅ Simple AI Engine ready!")
