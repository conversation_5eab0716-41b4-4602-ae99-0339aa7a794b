"""
Broker integration for Alpaca Trading API.
Handles order execution, position management, and account information.
"""

import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from alpaca.trading.client import TradingClient
from alpaca.trading.requests import MarketOrderRequest, LimitOrderRequest, StopOrderRequest
from alpaca.trading.enums import OrderSide as AlpacaOrderSide, TimeInForce, OrderType as AlpacaOrderType
from alpaca.common.exceptions import APIError

from config import config
from models import Order, Position, Trade, OrderSide, OrderType, OrderStatus, TradeStatus
from logger import log_info, log_error, log_debug, log_warning, log_trade_entry, log_trade_exit
from database import db

class AlpacaBroker:
    """Alpaca broker integration"""
    
    def __init__(self):
        self.api_key = config.api.alpaca_api_key
        self.secret_key = config.api.alpaca_secret_key
        self.base_url = config.api.alpaca_base_url
        
        if not self.api_key or not self.secret_key:
            raise ValueError("Alpaca API credentials are required")
        
        # Initialize Alpaca client
        self.client = TradingClient(
            api_key=self.api_key,
            secret_key=self.secret_key,
            paper=True if 'paper' in self.base_url else False
        )
        
        # Rate limiting
        self.last_request_time = 0
        self.rate_limit_delay = 60 / config.api.alpaca_requests_per_minute
        
        # Verify connection
        self._verify_connection()
    
    def _verify_connection(self):
        """Verify connection to Alpaca API"""
        try:
            account = self.client.get_account()
            log_info(f"Connected to Alpaca - Account: {account.account_number}")
            log_info(f"Buying Power: ${float(account.buying_power):,.2f}")
            log_info(f"Portfolio Value: ${float(account.portfolio_value):,.2f}")
        except Exception as e:
            log_error(f"Failed to connect to Alpaca: {e}")
            raise
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def get_account_info(self) -> Dict[str, Any]:
        """Get account information"""
        try:
            self._rate_limit()
            account = self.client.get_account()
            
            return {
                'account_number': account.account_number,
                'buying_power': float(account.buying_power),
                'cash': float(account.cash),
                'portfolio_value': float(account.portfolio_value),
                'equity': float(account.equity),
                'day_trade_count': account.day_trade_count,
                'pattern_day_trader': account.pattern_day_trader,
                'trading_blocked': account.trading_blocked,
                'account_blocked': account.account_blocked
            }
        except Exception as e:
            log_error(f"Failed to get account info: {e}")
            return {}
    
    def get_positions(self) -> List[Position]:
        """Get current positions"""
        try:
            self._rate_limit()
            alpaca_positions = self.client.get_all_positions()
            
            positions = []
            for pos in alpaca_positions:
                position = Position(
                    symbol=pos.symbol,
                    quantity=float(pos.qty),
                    entry_price=float(pos.avg_cost),
                    current_price=float(pos.market_value) / float(pos.qty) if float(pos.qty) != 0 else 0,
                    entry_time=datetime.now(),  # Alpaca doesn't provide entry time
                    side=OrderSide.BUY if float(pos.qty) > 0 else OrderSide.SELL,
                    unrealized_pnl=float(pos.unrealized_pl),
                    realized_pnl=0.0  # Not available from Alpaca position
                )
                positions.append(position)
            
            return positions
            
        except Exception as e:
            log_error(f"Failed to get positions: {e}")
            return []
    
    def place_market_order(self, symbol: str, side: OrderSide, quantity: float) -> Optional[Order]:
        """Place a market order"""
        try:
            self._rate_limit()
            
            # Convert to Alpaca format
            alpaca_side = AlpacaOrderSide.BUY if side == OrderSide.BUY else AlpacaOrderSide.SELL
            
            # Create market order request
            market_order_data = MarketOrderRequest(
                symbol=symbol,
                qty=quantity,
                side=alpaca_side,
                time_in_force=TimeInForce.DAY
            )
            
            # Submit order
            alpaca_order = self.client.submit_order(order_data=market_order_data)
            
            # Create our order object
            order = Order(
                id=f"order_{int(time.time())}",
                symbol=symbol,
                side=side,
                order_type=OrderType.MARKET,
                quantity=quantity,
                status=OrderStatus.SUBMITTED,
                created_at=datetime.now(),
                broker_order_id=alpaca_order.id
            )
            
            # Save to database
            db.save_order(order)
            
            log_info(f"Market order placed: {symbol} {side.value} {quantity}")
            return order
            
        except APIError as e:
            log_error(f"Alpaca API error placing market order: {e}")
            return None
        except Exception as e:
            log_error(f"Failed to place market order: {e}")
            return None
    
    def place_limit_order(self, symbol: str, side: OrderSide, quantity: float, price: float) -> Optional[Order]:
        """Place a limit order"""
        try:
            self._rate_limit()
            
            # Convert to Alpaca format
            alpaca_side = AlpacaOrderSide.BUY if side == OrderSide.BUY else AlpacaOrderSide.SELL
            
            # Create limit order request
            limit_order_data = LimitOrderRequest(
                symbol=symbol,
                qty=quantity,
                side=alpaca_side,
                time_in_force=TimeInForce.DAY,
                limit_price=price
            )
            
            # Submit order
            alpaca_order = self.client.submit_order(order_data=limit_order_data)
            
            # Create our order object
            order = Order(
                id=f"order_{int(time.time())}",
                symbol=symbol,
                side=side,
                order_type=OrderType.LIMIT,
                quantity=quantity,
                price=price,
                status=OrderStatus.SUBMITTED,
                created_at=datetime.now(),
                broker_order_id=alpaca_order.id
            )
            
            # Save to database
            db.save_order(order)
            
            log_info(f"Limit order placed: {symbol} {side.value} {quantity} @ ${price}")
            return order
            
        except APIError as e:
            log_error(f"Alpaca API error placing limit order: {e}")
            return None
        except Exception as e:
            log_error(f"Failed to place limit order: {e}")
            return None
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            self._rate_limit()
            
            # Find the broker order ID
            order = self.get_order_status(order_id)
            if not order or not order.broker_order_id:
                log_warning(f"Cannot find broker order ID for {order_id}")
                return False
            
            # Cancel the order
            self.client.cancel_order_by_id(order.broker_order_id)
            
            # Update order status
            order.status = OrderStatus.CANCELLED
            db.save_order(order)
            
            log_info(f"Order cancelled: {order_id}")
            return True
            
        except APIError as e:
            log_error(f"Alpaca API error cancelling order: {e}")
            return False
        except Exception as e:
            log_error(f"Failed to cancel order: {e}")
            return False
    
    def get_order_status(self, order_id: str) -> Optional[Order]:
        """Get order status"""
        try:
            # First try to get from database
            with db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM orders WHERE id = ?", (order_id,))
                row = cursor.fetchone()
                
                if row:
                    order = Order(
                        id=row['id'],
                        symbol=row['symbol'],
                        side=OrderSide(row['side']),
                        order_type=OrderType(row['order_type']),
                        quantity=row['quantity'],
                        price=row['price'],
                        stop_price=row['stop_price'],
                        status=OrderStatus(row['status']),
                        created_at=datetime.fromisoformat(row['created_at']),
                        filled_at=datetime.fromisoformat(row['filled_at']) if row['filled_at'] else None,
                        filled_price=row['filled_price'],
                        filled_quantity=row['filled_quantity'],
                        broker_order_id=row['broker_order_id']
                    )
                    
                    # Update status from Alpaca if we have broker order ID
                    if order.broker_order_id and order.status in [OrderStatus.SUBMITTED, OrderStatus.PENDING]:
                        self._update_order_from_alpaca(order)
                    
                    return order
            
            return None
            
        except Exception as e:
            log_error(f"Failed to get order status: {e}")
            return None
    
    def _update_order_from_alpaca(self, order: Order):
        """Update order status from Alpaca"""
        try:
            self._rate_limit()
            alpaca_order = self.client.get_order_by_id(order.broker_order_id)
            
            # Update status
            if alpaca_order.status == 'filled':
                order.status = OrderStatus.FILLED
                order.filled_at = datetime.now()
                order.filled_price = float(alpaca_order.filled_avg_price) if alpaca_order.filled_avg_price else None
                order.filled_quantity = float(alpaca_order.filled_qty)
            elif alpaca_order.status == 'partially_filled':
                order.status = OrderStatus.PARTIALLY_FILLED
                order.filled_price = float(alpaca_order.filled_avg_price) if alpaca_order.filled_avg_price else None
                order.filled_quantity = float(alpaca_order.filled_qty)
            elif alpaca_order.status == 'canceled':
                order.status = OrderStatus.CANCELLED
            elif alpaca_order.status == 'rejected':
                order.status = OrderStatus.REJECTED
            
            # Save updated order
            db.save_order(order)
            
        except Exception as e:
            log_warning(f"Failed to update order from Alpaca: {e}")
    
    def get_buying_power(self) -> float:
        """Get available buying power"""
        try:
            account_info = self.get_account_info()
            return account_info.get('buying_power', 0.0)
        except Exception as e:
            log_error(f"Failed to get buying power: {e}")
            return 0.0
    
    def calculate_position_size(self, price: float, risk_amount: float) -> int:
        """Calculate position size based on risk amount"""
        try:
            if price <= 0 or risk_amount <= 0:
                return 0
            
            # Get available buying power
            buying_power = self.get_buying_power()
            
            # Calculate maximum position size based on buying power
            max_shares_by_power = int(buying_power / price)
            
            # Calculate maximum position size based on risk
            max_shares_by_risk = int(config.trading.max_position_size / price)
            
            # Calculate shares based on risk amount (for stop loss)
            shares_by_risk = int(risk_amount / (price * config.trading.stop_loss_percent))
            
            # Take the minimum to ensure we don't exceed any limits
            position_size = min(max_shares_by_power, max_shares_by_risk, shares_by_risk)
            
            # Ensure minimum position size
            return max(1, position_size) if position_size > 0 else 0
            
        except Exception as e:
            log_error(f"Failed to calculate position size: {e}")
            return 0
    
    def is_market_open(self) -> bool:
        """Check if market is open"""
        try:
            self._rate_limit()
            clock = self.client.get_clock()
            return clock.is_open
        except Exception as e:
            log_warning(f"Failed to check market status: {e}")
            # Fallback to basic time check
            now = datetime.now()
            if now.weekday() >= 5:  # Weekend
                return False
            hour = now.hour
            return 9 <= hour < 16  # Rough market hours
    
    def get_portfolio_value(self) -> float:
        """Get total portfolio value"""
        try:
            account_info = self.get_account_info()
            return account_info.get('portfolio_value', 0.0)
        except Exception as e:
            log_error(f"Failed to get portfolio value: {e}")
            return 0.0

# Global broker instance
broker = AlpacaBroker()

if __name__ == "__main__":
    # Test broker connection
    try:
        account_info = broker.get_account_info()
        print("Account Info:", account_info)
        
        positions = broker.get_positions()
        print(f"Current positions: {len(positions)}")
        
        market_open = broker.is_market_open()
        print(f"Market is open: {market_open}")
        
        buying_power = broker.get_buying_power()
        print(f"Buying power: ${buying_power:,.2f}")
        
    except Exception as e:
        print(f"Broker test failed: {e}")
        print("Make sure to set your Alpaca API credentials in the .env file")
