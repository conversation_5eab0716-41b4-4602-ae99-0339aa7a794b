"""
Simple test to show the MassiveScan bot is working.
This will demonstrate the core functionality.
"""

import time
from datetime import datetime
from scanner import scanner
from data_provider import data_provider
from broker import broker
from risk_manager import risk_manager

def test_bot_functionality():
    """Test all bot components"""
    print("🚀 MASSIVESCAN FUNCTIONALITY TEST")
    print("=" * 50)
    print("Target: $50 Daily Profit")
    print("Strategy: $1 profit per trade, $0.50 stop loss")
    print("=" * 50)
    
    # 1. Test market status
    print("\n1. 📊 TESTING MARKET STATUS...")
    market_open = data_provider.is_market_open()
    print(f"   Market Status: {'OPEN' if market_open else 'CLOSED'}")
    print(f"   Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 2. Test broker connection
    print("\n2. 🏦 TESTING BROKER CONNECTION...")
    account = broker.get_account_info()
    if account:
        print(f"   ✅ Broker connected!")
        print(f"   Account: {account.get('account_number', 'N/A')}")
        print(f"   Buying Power: ${account.get('buying_power', 0):,.2f}")
        print(f"   Portfolio Value: ${account.get('portfolio_value', 0):,.2f}")
    else:
        print("   ❌ Broker connection failed")
        return False
    
    # 3. Test stock universe
    print("\n3. 🌍 TESTING STOCK UNIVERSE...")
    symbols = scanner.get_scannable_universe()
    print(f"   ✅ Found {len(symbols)} stocks to scan")
    if symbols:
        print(f"   Sample stocks: {symbols[:10]}")
    
    # 4. Test individual stock scanning
    print("\n4. 🔍 TESTING STOCK SCANNING...")
    if symbols:
        test_symbol = symbols[0]
        print(f"   Testing scan of {test_symbol}...")
        
        start_time = time.time()
        signals = scanner.scan_symbol(test_symbol)
        duration = time.time() - start_time
        
        print(f"   ✅ Scanned {test_symbol} in {duration:.2f}s")
        print(f"   Found {len(signals)} signals")
        
        for signal in signals:
            print(f"     - {signal.signal_type.value}: {signal.confidence:.1%} confidence")
    
    # 5. Test full market scan
    print("\n5. 🚀 TESTING FULL MARKET SCAN...")
    print("   Running full scan (this may take 30-60 seconds)...")
    
    start_time = time.time()
    all_signals = scanner.run_full_scan()
    duration = time.time() - start_time
    
    print(f"   ✅ Full scan completed in {duration:.1f}s")
    print(f"   🎯 Found {len(all_signals)} total signals!")
    
    if all_signals:
        print("\n   📈 TOP SIGNALS FOUND:")
        for i, signal in enumerate(all_signals[:5]):
            print(f"     {i+1}. {signal.symbol}: {signal.signal_type.value}")
            print(f"        Confidence: {signal.confidence:.1%}")
            print(f"        Entry: ${signal.entry_price:.2f} → Target: ${signal.target_price:.2f}")
            print(f"        Expected Profit: ${signal.expected_profit:.2f}")
    
    # 6. Test risk management
    print("\n6. 🛡️ TESTING RISK MANAGEMENT...")
    risk_summary = risk_manager.get_risk_summary()
    print(f"   Daily Trades: {risk_summary['daily_trades']}/{risk_summary['max_daily_trades']}")
    print(f"   Daily P&L: ${risk_summary['daily_pnl']:.2f}")
    print(f"   Trading Status: {'HALTED' if risk_summary['trading_halted'] else 'ACTIVE'}")
    
    if all_signals:
        top_signal = all_signals[0]
        can_trade, reason = risk_manager.can_place_trade(top_signal)
        print(f"   Can trade {top_signal.symbol}: {can_trade}")
        if not can_trade:
            print(f"   Reason: {reason}")
    
    # 7. Calculate daily potential
    print("\n7. 🎯 DAILY PROFIT POTENTIAL...")
    signals_per_scan = len(all_signals)
    scans_per_day = 8 * 60 / 0.5  # 8 hours, scan every 30 seconds
    potential_signals_per_day = signals_per_scan * (scans_per_day / 100)  # Conservative estimate
    
    # Assume 65% win rate and $1 average profit
    potential_daily_profit = potential_signals_per_day * 0.65 * 1.0
    
    print(f"   Signals this scan: {signals_per_scan}")
    print(f"   Estimated daily signals: {potential_signals_per_day:.0f}")
    print(f"   Potential daily profit (65% win rate): ${potential_daily_profit:.0f}")
    
    if potential_daily_profit >= 50:
        print("   🎉 $50 DAILY TARGET IS ACHIEVABLE!")
    else:
        print(f"   📈 Need to improve signal detection or win rate")
    
    # 8. Summary
    print("\n" + "=" * 50)
    print("✅ ALL SYSTEMS WORKING!")
    print("=" * 50)
    print("🎯 Bot is ready to start making micro-profits!")
    print("💰 Target: $50 daily through consistent $1 trades")
    print("🛡️ Risk managed with $0.50 stop losses")
    print("\nTo run the full bot:")
    print("  python gui.py        # Desktop interface")
    print("  python main.py       # Command line")
    print("  python run_live_bot.py  # Live demo")
    
    return True

if __name__ == "__main__":
    try:
        success = test_bot_functionality()
        if success:
            print("\n🚀 MassiveScan is ready to trade!")
        else:
            print("\n❌ Setup issues detected")
    except KeyboardInterrupt:
        print("\n\nTest cancelled by user")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        import traceback
        traceback.print_exc()
