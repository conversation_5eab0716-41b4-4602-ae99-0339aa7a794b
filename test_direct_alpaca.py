"""
Direct test of Alpaca API with new keys
"""

import os
from alpaca.trading.client import TradingClient

def test_direct_connection():
    print("🔧 DIRECT ALPACA API TEST")
    print("=" * 50)
    
    # Load from environment
    api_key = os.getenv('ALPACA_API_KEY')
    secret_key = os.getenv('ALPACA_SECRET_KEY')
    base_url = os.getenv('ALPACA_BASE_URL')
    
    print(f"API Key from env: {api_key}")
    print(f"Secret Key from env: {secret_key[:8] if secret_key else 'None'}...")
    print(f"Base URL from env: {base_url}")
    
    # Test with explicit keys
    explicit_api_key = "PKYIC5KKD7V7MADSGO9G"
    explicit_secret_key = "2gRdp0rKDVQNmsIvYsn5Cd4Lp4RxwppYqXkpPwOj"
    
    print(f"\nExplicit API Key: {explicit_api_key}")
    print(f"Explicit Secret Key: {explicit_secret_key[:8]}...")
    
    # Test paper trading first
    print("\n🧪 Testing Paper Trading:")
    try:
        paper_client = TradingClient(
            api_key=explicit_api_key,
            secret_key=explicit_secret_key,
            paper=True
        )
        
        paper_account = paper_client.get_account()
        print("✅ Paper trading connection successful!")
        print(f"Paper Account: {paper_account.account_number}")
        print(f"Paper Buying Power: ${float(paper_account.buying_power):,.2f}")
        
    except Exception as e:
        print(f"❌ Paper trading failed: {e}")
    
    # Test live trading
    print("\n💰 Testing Live Trading:")
    try:
        live_client = TradingClient(
            api_key=explicit_api_key,
            secret_key=explicit_secret_key,
            paper=False
        )
        
        live_account = live_client.get_account()
        print("✅ Live trading connection successful!")
        print(f"Live Account: {live_account.account_number}")
        print(f"Live Buying Power: ${float(live_account.buying_power):,.2f}")
        print(f"Live Portfolio: ${float(live_account.portfolio_value):,.2f}")
        print(f"Live Cash: ${float(live_account.cash):,.2f}")
        
        # Check if this is your $30K account
        portfolio_value = float(live_account.portfolio_value)
        if portfolio_value >= 25000:
            print("🎯 This looks like your $30K account!")
        else:
            print(f"⚠️ Portfolio value is ${portfolio_value:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Live trading failed: {e}")
        return False

if __name__ == "__main__":
    success = test_direct_connection()
    
    if success:
        print("\n✅ ALPACA CONNECTION SUCCESSFUL!")
        print("Ready to start live trading with your account!")
    else:
        print("\n❌ ALPACA CONNECTION FAILED!")
        print("Check API keys and account status")
