"""
Simple launcher for the MassiveScan trading bot.
Provides an easy way to start the bot with different interfaces.
"""

import sys
import os
from pathlib import Path

def check_setup():
    """Check if the bot is properly set up"""
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found!")
        print("Please run 'python setup.py' first to configure the bot.")
        return False
    
    # Check if API keys are configured
    try:
        with open(env_file, 'r') as f:
            content = f.read()
        
        if "your_fmp_api_key_here" in content or "your_alpaca_api_key_here" in content:
            print("❌ API keys not configured!")
            print("Please edit the .env file and add your actual API keys.")
            print("\nGet your FREE API keys from:")
            print("• FMP: https://financialmodelingprep.com/developer/docs")
            print("• Alpaca: https://alpaca.markets/ (Paper Trading)")
            return False
    except Exception as e:
        print(f"❌ Error checking .env file: {e}")
        return False
    
    return True

def show_menu():
    """Show the main menu"""
    print("\n" + "="*60)
    print("🚀 MASSIVESCAN TRADING BOT")
    print("Target: $50 Daily Profit through Micro-Trading")
    print("="*60)
    print("\nChoose how to run the bot:")
    print("\n1. 🖥️  Desktop GUI (Recommended)")
    print("   - Real-time dashboard")
    print("   - Visual trade monitoring")
    print("   - Easy start/stop controls")
    print("\n2. 💻 Command Line Interface")
    print("   - Text-based interface")
    print("   - Detailed logging")
    print("   - Advanced users")
    print("\n3. ⚙️  Setup & Configuration")
    print("   - Run initial setup")
    print("   - Configure API keys")
    print("   - Install dependencies")
    print("\n4. 📊 Quick Status Check")
    print("   - Test API connections")
    print("   - Check configuration")
    print("   - Verify setup")
    print("\n5. ❌ Exit")
    print("\n" + "="*60)

def run_gui():
    """Run the GUI interface"""
    try:
        print("\n🖥️  Starting Desktop GUI...")
        print("The trading bot window will open shortly.")
        print("Use the GUI to start/stop the bot and monitor trades.")
        
        import gui
        app = gui.TradingBotGUI()
        app.run()
        
    except ImportError as e:
        print(f"❌ Failed to import GUI: {e}")
        print("Please run 'python setup.py' to install dependencies.")
    except Exception as e:
        print(f"❌ GUI error: {e}")

def run_cli():
    """Run the command line interface"""
    try:
        print("\n💻 Starting Command Line Interface...")
        print("The bot will start automatically.")
        print("Press Ctrl+C to stop the bot gracefully.")
        print("\nStarting in 3 seconds...")
        
        import time
        time.sleep(3)
        
        import main
        import asyncio
        
        bot = main.MassiveScanBot()
        asyncio.run(bot.run())
        
    except ImportError as e:
        print(f"❌ Failed to import main: {e}")
        print("Please run 'python setup.py' to install dependencies.")
    except KeyboardInterrupt:
        print("\n✅ Bot stopped by user.")
    except Exception as e:
        print(f"❌ CLI error: {e}")

def run_setup():
    """Run the setup script"""
    try:
        print("\n⚙️  Running setup...")
        import setup
        setup.main()
    except ImportError as e:
        print(f"❌ Failed to import setup: {e}")
    except Exception as e:
        print(f"❌ Setup error: {e}")

def quick_status():
    """Quick status check"""
    print("\n📊 Quick Status Check")
    print("="*40)
    
    try:
        # Check configuration
        from config import config
        print("✅ Configuration loaded")
        print(f"   Target profit: ${config.trading.target_profit_dollars}")
        print(f"   Stop loss: ${config.trading.stop_loss_dollars}")
        print(f"   Max daily trades: {config.trading.max_daily_trades}")
        
        # Check data provider
        from data_provider import data_provider
        is_market_open = data_provider.is_market_open()
        print(f"✅ Data provider connected")
        print(f"   Market status: {'OPEN' if is_market_open else 'CLOSED'}")
        
        # Check broker
        from broker import broker
        account_info = broker.get_account_info()
        if account_info:
            print("✅ Broker connected")
            print(f"   Buying power: ${account_info.get('buying_power', 0):,.2f}")
            print(f"   Account type: {'Paper' if 'paper' in config.api.alpaca_base_url else 'Live'}")
        else:
            print("❌ Broker connection failed")
        
        # Check database
        from database import db
        performance = db.get_performance_summary(1)
        print("✅ Database connected")
        print(f"   Today's trades: {performance.get('total_trades', 0)}")
        print(f"   Today's P&L: ${performance.get('total_pnl', 0):.2f}")
        
        print("\n✅ All systems ready!")
        
    except Exception as e:
        print(f"❌ Status check failed: {e}")
        print("Please run setup first: python setup.py")

def main():
    """Main launcher function"""
    print("🚀 MassiveScan Trading Bot Launcher")
    
    while True:
        show_menu()
        
        try:
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == "1":
                if check_setup():
                    run_gui()
                else:
                    input("\nPress Enter to continue...")
                    
            elif choice == "2":
                if check_setup():
                    run_cli()
                else:
                    input("\nPress Enter to continue...")
                    
            elif choice == "3":
                run_setup()
                input("\nPress Enter to continue...")
                
            elif choice == "4":
                quick_status()
                input("\nPress Enter to continue...")
                
            elif choice == "5":
                print("\n👋 Goodbye!")
                break
                
            else:
                print("\n❌ Invalid choice. Please enter 1-5.")
                input("Press Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    
    sys.exit(0)
