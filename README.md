# MassiveScan - Automated Stock Trading Bot

🚀 **A fully automated stock scanner and micro-profit trading bot designed to generate $50 daily profit through high-frequency, low-risk trades.**

## 🎯 Trading Strategy

**Goal**: Generate $50 daily profit through micro-profit trading
- **Target**: ~$1 profit per trade
- **Volume**: 50-100 trades per day
- **Risk**: Maximum $0.50 loss per trade
- **Daily Loss Limit**: $25 maximum

### Core Logic
1. **Scan the entire stock market** using Financial Modeling Prep API
2. **Detect high-probability setups** with technical analysis
3. **Enter trades** with strict profit targets ($1) and stop losses ($0.50)
4. **Exit immediately** when targets are hit
5. **Repeat** throughout the trading day

## 📊 Key Features

### Automated Market Scanning
- Scans 1000+ stocks continuously
- Real-time price and volume analysis
- Multiple technical indicators (RSI, MACD, Volume Surge, VWAP)
- Smart filtering for liquid, stable stocks

### Risk Management
- **Position Sizing**: Automatic calculation based on risk
- **Stop Losses**: Hard $0.50 maximum loss per trade
- **Daily Limits**: Maximum 100 trades, $25 total loss
- **Concurrent Positions**: Maximum 10 open positions

### Trading Signals
- **Momentum Breakouts**: Price + volume surge detection
- **RSI Extremes**: Oversold/overbought reversals
- **Volume Surges**: Unusual volume with price movement
- **VWAP Bounces**: Support/resistance at volume-weighted average price

### Desktop Application
- **Real-time Dashboard**: Live P&L, positions, and statistics
- **Trade Monitoring**: Active positions with current P&L
- **Performance Tracking**: Win rate, daily performance, trade history
- **Manual Controls**: Start/stop bot, close all positions
- **Settings Panel**: Adjust trading parameters

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8 or higher
- Windows/Mac/Linux
- Internet connection

### Quick Start

1. **Clone the repository**
```bash
git clone <repository-url>
cd MassiveScan
```

2. **Run the setup script**
```bash
python setup.py
```

3. **Configure API keys** (edit `.env` file)
```env
# Financial Modeling Prep (FREE)
FMP_API_KEY=your_fmp_api_key_here

# Alpaca Trading (FREE Paper Trading)
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets
```

4. **Run the bot**
```bash
# Command line interface
python main.py

# Graphical interface (recommended)
python gui.py
```

## 🔑 API Keys Setup

### Financial Modeling Prep (Market Data)
1. Visit [financialmodelingprep.com](https://financialmodelingprep.com/developer/docs)
2. Sign up for a **FREE** account
3. Get your API key (250 requests/day free)
4. Add to `.env` file

### Alpaca Trading (Broker)
1. Visit [alpaca.markets](https://alpaca.markets/)
2. Sign up for a **FREE** account
3. Go to **Paper Trading** section
4. Generate API keys
5. Add to `.env` file

**⚠️ IMPORTANT**: Start with paper trading only!

## 📈 Expected Performance

### Conservative Estimates
- **Daily Target**: $50 profit
- **Win Rate**: 60-70%
- **Average Trade**: $1 profit, $0.50 max loss
- **Trade Volume**: 50-100 trades/day
- **Risk/Reward**: 2:1 ratio

### Monthly Projection
- **20 Trading Days**: $1,000/month
- **Risk**: $500 maximum monthly loss
- **Capital Required**: $2,000-5,000 recommended

## 🎮 How to Use

### Desktop GUI (Recommended)
1. Run `python gui.py`
2. Click **"Start Bot"** to begin scanning
3. Monitor the **Dashboard** for real-time stats
4. View **Active Trades** tab for current positions
5. Check **Performance** tab for daily/weekly results
6. Use **Settings** to adjust parameters

### Command Line
1. Run `python main.py`
2. Bot will start automatically
3. Monitor logs for trade activity
4. Press `Ctrl+C` to stop gracefully

## 📊 Dashboard Overview

### Main Dashboard
- **Account Info**: Buying power, portfolio value
- **Trading Stats**: Daily trades, P&L, win rate
- **Risk Metrics**: Loss limits, consecutive losses
- **Active Positions**: Real-time P&L tracking

### Performance Tracking
- **Trade History**: Complete record of all trades
- **Win/Loss Analysis**: Detailed performance metrics
- **Export Functionality**: CSV export for analysis
- **Daily/Weekly/Monthly summaries**

## ⚙️ Configuration

### Trading Parameters
```python
# Profit targets
target_profit_dollars = 1.00      # $1 per trade
stop_loss_dollars = 0.50          # $0.50 max loss

# Daily limits
max_daily_trades = 100            # 100 trades max
max_daily_loss = 25.00           # $25 max daily loss
max_concurrent_positions = 10     # 10 open positions max

# Stock filters
min_volume = 500000              # 500K daily volume minimum
min_price = 5.00                 # $5 minimum stock price
max_price = 200.00               # $200 maximum stock price
min_market_cap = *********       # $500M market cap minimum
```

### Technical Analysis Settings
```python
# RSI settings
rsi_oversold = 30               # RSI oversold level
rsi_overbought = 70             # RSI overbought level

# Volume analysis
volume_surge_multiplier = 2.0   # 2x average volume required

# Momentum detection
momentum_threshold = 0.01       # 1% price movement minimum
```

## 🛡️ Safety Features

### Risk Management
- **Hard Stop Losses**: Automatic exit at $0.50 loss
- **Daily Loss Limits**: Trading halts at $25 daily loss
- **Position Limits**: Maximum 10 concurrent positions
- **Market Hours**: Only trades during market hours
- **Paper Trading**: Default configuration for safety

### Monitoring & Alerts
- **Real-time Monitoring**: Continuous position tracking
- **Risk Alerts**: Warnings for high-risk situations
- **Performance Logging**: Detailed trade records
- **Manual Override**: Emergency stop and position closing

## 📁 Project Structure

```
MassiveScan/
├── main.py                 # Main application entry point
├── gui.py                  # Desktop GUI interface
├── config.py               # Configuration management
├── scanner.py              # Stock market scanner
├── technical_analysis.py   # Technical indicators & signals
├── trade_manager.py        # Trade execution & monitoring
├── risk_manager.py         # Risk management system
├── broker.py               # Alpaca broker integration
├── data_provider.py        # Financial data API
├── database.py             # SQLite database operations
├── models.py               # Data models & structures
├── logger.py               # Logging system
├── requirements.txt        # Python dependencies
├── setup.py                # Installation script
└── README.md               # This file
```

## 🚨 Important Disclaimers

### Trading Risks
- **Past performance does not guarantee future results**
- **All trading involves risk of loss**
- **Never trade with money you cannot afford to lose**
- **This bot is for educational purposes**

### Recommendations
1. **Start with paper trading** (default configuration)
2. **Test thoroughly** before considering live trading
3. **Monitor closely**, especially initially
4. **Start small** if you decide to go live
5. **Understand the code** before trusting it with real money

### Legal Notice
This software is provided "as is" without warranty. The authors are not responsible for any financial losses. Use at your own risk.

## 🤝 Support & Contributing

### Getting Help
- Check the logs in `logs/` directory
- Review configuration in `config.py`
- Test with paper trading first
- Monitor the GUI dashboard for issues

### Contributing
- Fork the repository
- Create a feature branch
- Submit a pull request
- Follow the existing code style

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Happy Trading! 🚀**

*Remember: Start with paper trading, test thoroughly, and never risk more than you can afford to lose.*
