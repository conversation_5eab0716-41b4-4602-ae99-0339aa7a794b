"""
Setup script for the MassiveScan trading bot.
Handles installation, configuration, and initial setup.
"""

import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    return True

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("Error: requirements.txt not found")
        return False
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ All packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing packages: {e}")
        return False

def create_env_file():
    """Create .env file with API key placeholders"""
    env_file = Path(".env")
    
    if env_file.exists():
        print("✓ .env file already exists")
        return True
    
    env_content = """# Financial Modeling Prep API
# Get your free API key at: https://financialmodelingprep.com/developer/docs
FMP_API_KEY=your_fmp_api_key_here

# Alpaca Trading API
# Get your API keys at: https://alpaca.markets/
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here

# Trading Environment
# Use paper trading for testing (recommended)
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# For live trading (use with extreme caution)
# ALPACA_BASE_URL=https://api.alpaca.markets
"""
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_content)
        print("✓ Created .env file with API key placeholders")
        return True
    except Exception as e:
        print(f"Error creating .env file: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        "logs",
        "data",
        "exports"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"✓ Created directory: {directory}")
            except Exception as e:
                print(f"Error creating directory {directory}: {e}")
                return False
    
    return True

def check_api_keys():
    """Check if API keys are configured"""
    env_file = Path(".env")
    if not env_file.exists():
        return False
    
    try:
        with open(env_file, 'r') as f:
            content = f.read()
        
        has_fmp_key = "FMP_API_KEY=" in content and "your_fmp_api_key_here" not in content
        has_alpaca_keys = ("ALPACA_API_KEY=" in content and "your_alpaca_api_key_here" not in content and
                          "ALPACA_SECRET_KEY=" in content and "your_alpaca_secret_key_here" not in content)
        
        return has_fmp_key and has_alpaca_keys
    except Exception:
        return False

def test_installation():
    """Test if the installation is working"""
    print("\nTesting installation...")
    
    try:
        # Test imports
        import pandas  # noqa: F401
        import numpy  # noqa: F401
        import requests  # noqa: F401
        import pandas_ta  # noqa: F401
        print("✓ Core packages imported successfully")

        # Test configuration
        from config import config  # noqa: F401
        print("✓ Configuration loaded successfully")

        # Test database
        from database import db  # noqa: F401
        print("✓ Database initialized successfully")

        return True

    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "="*60)
    print("MASSIVESCAN TRADING BOT - SETUP COMPLETE!")
    print("Target: $50 Daily Profit through Micro-Trading")
    print("="*60)
    
    if not check_api_keys():
        print("\n⚠️  IMPORTANT: You need to configure your API keys!")
        print("\n1. Get a FREE Financial Modeling Prep API key:")
        print("   → Visit: https://financialmodelingprep.com/developer/docs")
        print("   → Sign up for a free account")
        print("   → Copy your API key")
        
        print("\n2. Get Alpaca Trading API keys:")
        print("   → Visit: https://alpaca.markets/")
        print("   → Sign up for a free account")
        print("   → Go to 'Paper Trading' section")
        print("   → Generate API keys")
        
        print("\n3. Edit the .env file and replace the placeholder values:")
        print("   → FMP_API_KEY=your_actual_fmp_key")
        print("   → ALPACA_API_KEY=your_actual_alpaca_key")
        print("   → ALPACA_SECRET_KEY=your_actual_alpaca_secret")
    else:
        print("\n✓ API keys are configured!")
    
    print("\n" + "="*60)
    print("HOW TO RUN THE BOT:")
    print("="*60)
    
    print("\n1. Command Line Interface:")
    print("   python main.py")
    
    print("\n2. Graphical User Interface:")
    print("   python gui.py")
    
    print("\n" + "="*60)
    print("SAFETY REMINDERS:")
    print("="*60)
    print("• Start with PAPER TRADING only (default configuration)")
    print("• Target: $50 daily profit with $25 max daily loss")
    print("• Test thoroughly before considering live trading")
    print("• Never risk more than you can afford to lose")
    print("• Monitor the bot closely, especially initially")
    print("• The bot is for educational purposes - trade at your own risk")

    print("\n" + "="*60)
    print("TRADING STRATEGY:")
    print("="*60)
    print("• Target: $1 profit per trade")
    print("• Stop Loss: $0.50 maximum loss per trade")
    print("• Daily Goal: $50 profit (50-100 trades)")
    print("• Risk Management: $25 maximum daily loss")
    print("• Focus: High-frequency, low-risk micro-profits")

    print("\n" + "="*60)
    print("SUPPORT:")
    print("="*60)
    print("• Check the README.md file for detailed documentation")
    print("• Review the configuration in config.py")
    print("• Monitor logs in the logs/ directory")

    print("\nHappy Trading! 🚀")
    print("Remember: Small profits, managed risk, consistent execution!")

def main():
    """Main setup function"""
    print("="*60)
    print("MassiveScan Trading Bot - Setup")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        return False
    
    print("✓ Python version compatible")
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Create .env file
    if not create_env_file():
        return False
    
    # Create directories
    if not create_directories():
        return False
    
    # Test installation
    if not test_installation():
        print("\n⚠️  Installation test failed. Please check for errors above.")
        return False
    
    print("✓ Installation test passed")
    
    # Print next steps
    print_next_steps()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ Setup failed. Please check the errors above and try again.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\nSetup cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during setup: {e}")
        sys.exit(1)
