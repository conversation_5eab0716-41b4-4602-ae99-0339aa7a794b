"""
Diagnose Alpaca API connection issues
"""

import requests
import base64

def test_alpaca_connection():
    print("🔧 ALPACA API DIAGNOSTICS")
    print("=" * 50)
    
    # Your API credentials
    api_key = "PKGYRBVHNVA0K7M54M0O"
    secret_key = "AviNiUMW8KTx68t8IjFGDMlGveiiHSA6gIuS3fsL"
    
    print(f"API Key: {api_key}")
    print(f"Secret Key: {secret_key[:8]}...")
    
    # Test different endpoints
    endpoints = [
        ("Paper Trading", "https://paper-api.alpaca.markets/v2/account"),
        ("Live Trading", "https://api.alpaca.markets/v2/account")
    ]
    
    for name, url in endpoints:
        print(f"\n🔍 Testing {name}:")
        print(f"URL: {url}")
        
        try:
            headers = {
                'APCA-API-KEY-ID': api_key,
                'APCA-API-SECRET-KEY': secret_key
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ SUCCESS!")
                print(f"Account: {data.get('account_number', 'N/A')}")
                print(f"Status: {data.get('status', 'N/A')}")
                print(f"Buying Power: ${float(data.get('buying_power', 0)):,.2f}")
                print(f"Portfolio: ${float(data.get('portfolio_value', 0)):,.2f}")
                
                # Check if this looks like your $30K account
                portfolio = float(data.get('portfolio_value', 0))
                if portfolio >= 25000:
                    print("🎯 This looks like your $30K account!")
                else:
                    print(f"⚠️ Portfolio is ${portfolio:,.2f} (expected ~$30K)")
                    
            elif response.status_code == 401:
                print("❌ UNAUTHORIZED - API keys may be invalid")
                print("Check:")
                print("  • API keys are correct")
                print("  • Account is active")
                print("  • Keys have proper permissions")
                
            elif response.status_code == 403:
                print("❌ FORBIDDEN - Account may not have trading permissions")
                
            else:
                print(f"❌ ERROR: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error: {error_data}")
                except:
                    print(f"Response: {response.text}")
                    
        except Exception as e:
            print(f"❌ CONNECTION ERROR: {e}")
    
    print("\n" + "=" * 50)
    print("RECOMMENDATIONS:")
    print("1. Verify API keys are correct")
    print("2. Check account status in Alpaca dashboard")
    print("3. Ensure account has trading permissions")
    print("4. Try paper trading first to test connection")

def test_paper_trading():
    """Test with paper trading to verify basic connection"""
    print("\n🧪 TESTING PAPER TRADING CONNECTION")
    print("=" * 50)
    
    # Use your keys with paper trading
    api_key = "PKGYRBVHNVA0K7M54M0O"
    secret_key = "AviNiUMW8KTx68t8IjFGDMlGveiiHSA6gIuS3fsL"
    
    try:
        from alpaca.trading.client import TradingClient
        
        # Test paper trading
        client = TradingClient(
            api_key=api_key,
            secret_key=secret_key,
            paper=True
        )
        
        account = client.get_account()
        print("✅ Paper trading connection successful!")
        print(f"Paper Account: {account.account_number}")
        print(f"Paper Buying Power: ${float(account.buying_power):,.2f}")
        
        # Test live trading
        live_client = TradingClient(
            api_key=api_key,
            secret_key=secret_key,
            paper=False
        )
        
        live_account = live_client.get_account()
        print("✅ Live trading connection successful!")
        print(f"Live Account: {live_account.account_number}")
        print(f"Live Buying Power: ${float(live_account.buying_power):,.2f}")
        print(f"Live Portfolio: ${float(live_account.portfolio_value):,.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Alpaca SDK Error: {e}")
        return False

if __name__ == "__main__":
    test_alpaca_connection()
    test_paper_trading()
