"""
Simple command-line version of MassiveScan that shows scanning activity.
This will let you see exactly what the bot is doing in real-time.
"""

import asyncio
import time
from datetime import datetime
from scanner import scanner
from trade_manager import trade_manager
from risk_manager import risk_manager
from data_provider import data_provider
from broker import broker

async def run_trading_bot():
    """Run the trading bot with visible output"""
    print("🚀 MASSIVESCAN TRADING BOT STARTING...")
    print("=" * 60)
    print("Target: $50 Daily Profit")
    print("Strategy: $1 profit per trade, $0.50 stop loss")
    print("Scanning: Every 30 seconds")
    print("=" * 60)
    
    # Check initial status
    print("\n📊 INITIAL STATUS CHECK:")
    market_open = data_provider.is_market_open()
    print(f"Market Status: {'OPEN' if market_open else 'CLOSED'}")
    print(f"Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check account
    account = broker.get_account_info()
    if account:
        print(f"Account: ${account.get('buying_power', 0):,.2f} buying power")
    else:
        print("❌ Account connection failed")
        return
    
    # Check risk status
    risk_summary = risk_manager.get_risk_summary()
    print(f"Daily Trades: {risk_summary['daily_trades']}/{risk_summary['max_daily_trades']}")
    print(f"Daily P&L: ${risk_summary['daily_pnl']:.2f}")
    
    print("\n🔍 STARTING LIVE SCANNING...")
    print("Press Ctrl+C to stop")
    print("-" * 60)
    
    scan_count = 0
    
    try:
        while True:
            scan_count += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            
            print(f"\n[{current_time}] 🔍 SCAN #{scan_count}")
            print("=" * 40)
            
            # Check if market is open
            if not market_open and not hasattr(scanner, 'demo_mode'):
                print("⏰ Market is closed, waiting...")
                await asyncio.sleep(60)
                continue
            
            # Run the scan
            print("📊 Scanning market...")
            start_time = time.time()
            
            try:
                signals = scanner.run_full_scan()
                scan_duration = time.time() - start_time
                
                print(f"✅ Scan completed in {scan_duration:.1f}s")
                print(f"🎯 Found {len(signals)} signals")
                
                if signals:
                    print("\n📈 SIGNALS FOUND:")
                    for i, signal in enumerate(signals[:5]):  # Show top 5
                        print(f"  {i+1}. {signal.symbol}: {signal.signal_type.value}")
                        print(f"     Confidence: {signal.confidence:.1%}")
                        print(f"     Entry: ${signal.entry_price:.2f} → Target: ${signal.target_price:.2f}")
                    
                    # Try to execute the best signal
                    top_signal = signals[0]
                    print(f"\n💰 EVALUATING TRADE: {top_signal.symbol}")
                    
                    can_trade, reason = risk_manager.can_place_trade(top_signal)
                    if can_trade:
                        print(f"✅ Executing trade...")
                        trade = trade_manager.execute_signal(top_signal)
                        if trade:
                            print(f"🎉 TRADE EXECUTED: {trade.symbol} {trade.side.value} {trade.quantity}")
                        else:
                            print(f"❌ Trade execution failed")
                    else:
                        print(f"⚠️ Cannot trade: {reason}")
                
                else:
                    print("⚪ No signals found this scan")
                
                # Show current status
                risk_summary = risk_manager.get_risk_summary()
                active_trades = trade_manager.get_active_trades_info()
                
                print(f"\n📊 CURRENT STATUS:")
                print(f"  Daily Trades: {risk_summary['daily_trades']}")
                print(f"  Daily P&L: ${risk_summary['daily_pnl']:.2f}")
                print(f"  Active Trades: {len(active_trades)}")
                
                if active_trades:
                    total_unrealized = sum(t['unrealized_pnl'] for t in active_trades)
                    print(f"  Unrealized P&L: ${total_unrealized:.2f}")
                
                # Progress toward goal
                remaining = 50 - risk_summary['daily_pnl']
                if remaining > 0:
                    trades_needed = remaining / 1.0
                    print(f"  🎯 Need ~{trades_needed:.0f} more profitable trades for $50 goal")
                else:
                    print(f"  🎉 DAILY GOAL ACHIEVED!")
                
                print(f"\n⏱️ Next scan in 30 seconds...")
                
            except Exception as e:
                print(f"❌ Scan error: {e}")
            
            # Wait 30 seconds
            await asyncio.sleep(30)
            
    except KeyboardInterrupt:
        print("\n\n🛑 Bot stopped by user")
        
        # Close all positions
        print("🔄 Closing all open positions...")
        trade_manager.close_all_positions("Bot shutdown")
        
        # Final summary
        risk_summary = risk_manager.get_risk_summary()
        print(f"\n📊 FINAL SUMMARY:")
        print(f"  Total scans: {scan_count}")
        print(f"  Daily trades: {risk_summary['daily_trades']}")
        print(f"  Daily P&L: ${risk_summary['daily_pnl']:.2f}")
        
        if risk_summary['daily_pnl'] >= 50:
            print("  🎉 DAILY TARGET ACHIEVED!")
        
        print("\n👋 Goodbye!")

def main():
    """Main entry point"""
    print("Starting MassiveScan Trading Bot...")
    print("This will show real-time scanning activity.")
    
    try:
        asyncio.run(run_trading_bot())
    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
