"""
Live command-line version of the MassiveScan trading bot.
Shows real-time scanning and trading activity.
"""

import asyncio
import time
from datetime import datetime
from scanner import scanner
from trade_manager import trade_manager
from risk_manager import risk_manager
from data_provider import data_provider
from broker import broker

class LiveTradingBot:
    """Live trading bot with real-time feedback"""
    
    def __init__(self):
        self.running = False
        self.scan_count = 0
        self.signals_found = 0
        self.trades_executed = 0
    
    async def initialize(self):
        """Initialize the bot"""
        print("🚀 MASSIVESCAN LIVE TRADING BOT")
        print("=" * 50)
        print("Target: $50 Daily Profit")
        print("Strategy: $1 profit per trade, $0.50 stop loss")
        print("=" * 50)
        
        # Check market status
        market_open = data_provider.is_market_open()
        print(f"Market Status: {'OPEN' if market_open else 'CLOSED'}")
        print(f"Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Check account
        account = broker.get_account_info()
        if account:
            print(f"Account Connected: ${account.get('buying_power', 0):,.2f} buying power")
        else:
            print("❌ Account connection failed")
            return False
        
        # Check risk limits
        risk_summary = risk_manager.get_risk_summary()
        print(f"Daily Trades: {risk_summary['daily_trades']}/{risk_summary['max_daily_trades']}")
        print(f"Daily P&L: ${risk_summary['daily_pnl']:.2f}")
        
        print("\n✅ Bot initialized successfully!")
        return True
    
    async def run_scanning_loop(self):
        """Main scanning and trading loop"""
        print("\n🔍 STARTING LIVE SCANNING...")
        print("Press Ctrl+C to stop\n")
        
        self.running = True
        
        while self.running:
            try:
                self.scan_count += 1
                start_time = time.time()
                
                print(f"[{datetime.now().strftime('%H:%M:%S')}] Scan #{self.scan_count}")
                
                # Run market scan
                signals = scanner.run_full_scan()
                scan_duration = time.time() - start_time
                
                print(f"  📊 Scanned market in {scan_duration:.1f}s")
                print(f"  🎯 Found {len(signals)} signals")
                
                if signals:
                    self.signals_found += len(signals)
                    
                    # Show top signals
                    print("  📈 TOP SIGNALS:")
                    for i, signal in enumerate(signals[:3]):
                        print(f"    {i+1}. {signal.symbol}: {signal.signal_type.value}")
                        print(f"       Confidence: {signal.confidence:.1%}")
                        print(f"       Entry: ${signal.entry_price:.2f} → Target: ${signal.target_price:.2f}")
                    
                    # Execute top signal if conditions are met
                    top_signal = signals[0]
                    can_trade, reason = risk_manager.can_place_trade(top_signal)
                    
                    if can_trade:
                        print(f"  💰 EXECUTING: {top_signal.symbol}")
                        trade = trade_manager.execute_signal(top_signal)
                        if trade:
                            self.trades_executed += 1
                            print(f"     ✅ Trade executed! Total trades: {self.trades_executed}")
                        else:
                            print(f"     ❌ Trade execution failed")
                    else:
                        print(f"  ⚠️  Cannot trade: {reason}")
                
                else:
                    print("  ⚪ No signals found")
                
                # Show active trades
                active_trades = trade_manager.get_active_trades_info()
                if active_trades:
                    print(f"  📊 Active trades: {len(active_trades)}")
                    total_pnl = sum(t['unrealized_pnl'] for t in active_trades)
                    print(f"     Unrealized P&L: ${total_pnl:.2f}")
                
                # Show progress toward $50 goal
                risk_summary = risk_manager.get_risk_summary()
                daily_pnl = risk_summary['daily_pnl']
                remaining = 50 - daily_pnl
                
                print(f"  🎯 Daily Progress: ${daily_pnl:.2f} / $50.00")
                if remaining > 0:
                    trades_needed = remaining / 1.0  # $1 per trade
                    print(f"     Need ~{trades_needed:.0f} more profitable trades")
                else:
                    print(f"     🎉 DAILY TARGET ACHIEVED!")
                
                print(f"  📈 Stats: {self.scan_count} scans, {self.signals_found} signals, {self.trades_executed} trades")
                print()
                
                # Wait before next scan
                await asyncio.sleep(30)  # Scan every 30 seconds
                
            except KeyboardInterrupt:
                print("\n🛑 Stopping bot...")
                self.running = False
                break
            except Exception as e:
                print(f"  ❌ Error: {e}")
                await asyncio.sleep(10)  # Wait 10 seconds on error
    
    async def monitor_trades(self):
        """Monitor active trades"""
        while self.running:
            try:
                # Monitor all active trades
                trade_manager.monitor_all_trades()
                await asyncio.sleep(5)  # Check every 5 seconds
            except Exception as e:
                print(f"Trade monitoring error: {e}")
                await asyncio.sleep(10)
    
    async def run(self):
        """Run the complete bot"""
        try:
            if not await self.initialize():
                return
            
            # Start both scanning and trade monitoring
            await asyncio.gather(
                self.run_scanning_loop(),
                self.monitor_trades()
            )
            
        except KeyboardInterrupt:
            print("\n🛑 Bot stopped by user")
        except Exception as e:
            print(f"❌ Bot error: {e}")
        finally:
            # Close all positions
            print("\n🔄 Closing all positions...")
            trade_manager.close_all_positions("Bot shutdown")
            
            # Final summary
            print("\n📊 FINAL SUMMARY:")
            print(f"  Total scans: {self.scan_count}")
            print(f"  Signals found: {self.signals_found}")
            print(f"  Trades executed: {self.trades_executed}")
            
            risk_summary = risk_manager.get_risk_summary()
            print(f"  Daily P&L: ${risk_summary['daily_pnl']:.2f}")
            
            if risk_summary['daily_pnl'] >= 50:
                print("  🎉 DAILY TARGET ACHIEVED!")
            else:
                remaining = 50 - risk_summary['daily_pnl']
                print(f"  📈 ${remaining:.2f} away from $50 target")
            
            print("\n👋 Goodbye!")

async def main():
    """Main entry point"""
    bot = LiveTradingBot()
    await bot.run()

if __name__ == "__main__":
    print("Starting MassiveScan Live Trading Bot...")
    print("This will show real-time scanning and trading activity.")
    print("Press Ctrl+C to stop at any time.\n")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except Exception as e:
        print(f"Fatal error: {e}")
