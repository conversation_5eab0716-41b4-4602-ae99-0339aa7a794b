"""
Easy profit optimization settings for MassiveScan.
Adjust these settings to ride profitable trades longer and make more money!
"""

from config import config

def set_conservative_profits():
    """Conservative settings - stick to $1 targets"""
    config.trading.target_profit_dollars = 1.00
    config.trading.use_trailing_stop = False
    config.trading.max_profit_target = 2.00
    config.trading.partial_profit_taking = False
    print("✅ Conservative profit settings applied")
    print("   Target: $1 per trade")
    print("   No trailing stops")
    print("   Quick exits for consistent small profits")

def set_moderate_profits():
    """Moderate settings - let some winners run"""
    config.trading.target_profit_dollars = 1.00
    config.trading.use_trailing_stop = True
    config.trading.trailing_stop_dollars = 0.50
    config.trading.max_profit_target = 3.00
    config.trading.partial_profit_taking = False
    print("✅ Moderate profit settings applied")
    print("   Initial target: $1 per trade")
    print("   Trailing stop: $0.50")
    print("   Max target: $3.00")
    print("   Let winners run with protection!")

def set_aggressive_profits():
    """Aggressive settings - ride big winners!"""
    config.trading.target_profit_dollars = 1.50
    config.trading.use_trailing_stop = True
    config.trading.trailing_stop_dollars = 0.75
    config.trading.max_profit_target = 5.00
    config.trading.partial_profit_taking = True
    config.trading.partial_profit_percent = 0.5
    print("✅ Aggressive profit settings applied")
    print("   Initial target: $1.50 per trade")
    print("   Trailing stop: $0.75")
    print("   Max target: $5.00")
    print("   Partial profit taking enabled")
    print("   🚀 RIDE THOSE WINNERS!")

def set_custom_profits(target_profit=1.00, max_profit=3.00, trailing_stop=0.50, use_trailing=True):
    """Custom profit settings"""
    config.trading.target_profit_dollars = target_profit
    config.trading.use_trailing_stop = use_trailing
    config.trading.trailing_stop_dollars = trailing_stop
    config.trading.max_profit_target = max_profit
    print(f"✅ Custom profit settings applied")
    print(f"   Target: ${target_profit}")
    print(f"   Max target: ${max_profit}")
    print(f"   Trailing stop: ${trailing_stop if use_trailing else 'Disabled'}")

def show_current_settings():
    """Show current profit settings"""
    print("📊 CURRENT PROFIT SETTINGS:")
    print("=" * 40)
    print(f"Target profit: ${config.trading.target_profit_dollars}")
    print(f"Stop loss: ${config.trading.stop_loss_dollars}")
    print(f"Use trailing stop: {config.trading.use_trailing_stop}")
    if config.trading.use_trailing_stop:
        print(f"Trailing stop: ${config.trading.trailing_stop_dollars}")
    print(f"Max profit target: ${config.trading.max_profit_target}")
    print(f"Partial profit taking: {config.trading.partial_profit_taking}")
    print("=" * 40)

def estimate_daily_profit(trades_per_day=50, win_rate=0.65):
    """Estimate daily profit with current settings"""
    avg_win = (config.trading.target_profit_dollars + config.trading.max_profit_target) / 2
    avg_loss = config.trading.stop_loss_dollars
    
    winning_trades = trades_per_day * win_rate
    losing_trades = trades_per_day * (1 - win_rate)
    
    gross_profit = winning_trades * avg_win
    gross_loss = losing_trades * avg_loss
    net_profit = gross_profit - gross_loss
    
    print(f"\n💰 DAILY PROFIT ESTIMATE:")
    print(f"Trades per day: {trades_per_day}")
    print(f"Win rate: {win_rate:.1%}")
    print(f"Average win: ${avg_win:.2f}")
    print(f"Average loss: ${avg_loss:.2f}")
    print(f"Estimated daily profit: ${net_profit:.2f}")
    
    if net_profit >= 50:
        print("🎉 TARGET ACHIEVABLE!")
    else:
        needed_trades = 50 / (avg_win * win_rate - avg_loss * (1 - win_rate))
        print(f"📈 Need ~{needed_trades:.0f} trades for $50 target")

if __name__ == "__main__":
    print("🚀 MASSIVESCAN PROFIT OPTIMIZATION")
    print("=" * 50)
    
    show_current_settings()
    
    print("\nChoose profit strategy:")
    print("1. Conservative ($1 targets, quick exits)")
    print("2. Moderate (trailing stops, $3 max)")
    print("3. Aggressive (ride big winners, $5 max)")
    print("4. Show estimates")
    print("5. Current settings")
    
    try:
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == "1":
            set_conservative_profits()
        elif choice == "2":
            set_moderate_profits()
        elif choice == "3":
            set_aggressive_profits()
        elif choice == "4":
            estimate_daily_profit()
        elif choice == "5":
            show_current_settings()
        else:
            print("Invalid choice")
        
        print("\nTo apply changes, restart the trading bot!")
        
    except KeyboardInterrupt:
        print("\nCancelled")
    except Exception as e:
        print(f"Error: {e}")
