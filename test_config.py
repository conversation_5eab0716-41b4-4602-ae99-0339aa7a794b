"""
Test configuration to ensure live trading is enabled
"""

from config import config

def test_config():
    print("🔧 CONFIGURATION TEST")
    print("=" * 50)
    
    print(f"Base URL: {config.api.alpaca_base_url}")
    print(f"API Key: {config.api.alpaca_api_key}")
    print(f"Secret Key: {config.api.alpaca_secret_key[:8]}...")
    
    is_paper = 'paper' in config.api.alpaca_base_url
    print(f"Paper mode: {is_paper}")
    print(f"Live trading: {not is_paper}")
    
    if is_paper:
        print("❌ STILL USING PAPER TRADING!")
        print("Need to fix configuration")
    else:
        print("✅ LIVE TRADING ENABLED!")
    
    print("\nTrading Settings:")
    print(f"Target profit: ${config.trading.target_profit_dollars}")
    print(f"Risk per trade: ${config.trading.risk_per_trade_dollars}")
    print(f"Max position: ${config.trading.max_position_size}")

if __name__ == "__main__":
    test_config()
