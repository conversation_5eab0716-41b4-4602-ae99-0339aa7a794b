"""
Logging configuration for the MassiveScan trading bot.
Provides structured logging with different levels and outputs.
"""

import logging
import logging.handlers
import os
from datetime import datetime
from typing import Optional

class TradingLogger:
    """Custom logger for trading bot with multiple handlers"""
    
    def __init__(self, name: str = "MassiveScan", log_file: str = "trading_bot.log", level: str = "INFO"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        simple_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler with rotation
        if log_file:
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(detailed_formatter)
            self.logger.addHandler(file_handler)
        
        # Trading-specific log file
        trading_handler = logging.handlers.RotatingFileHandler(
            'trades.log',
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10
        )
        trading_handler.setLevel(logging.INFO)
        trading_handler.setFormatter(detailed_formatter)
        
        # Create a filter for trading-related logs
        class TradingFilter(logging.Filter):
            def filter(self, record):
                return any(keyword in record.getMessage().lower() 
                          for keyword in ['trade', 'order', 'position', 'profit', 'loss', 'buy', 'sell'])
        
        trading_handler.addFilter(TradingFilter())
        self.logger.addHandler(trading_handler)
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self.logger.info(message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self.logger.debug(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message"""
        self.logger.critical(message, **kwargs)
    
    def trade_entry(self, symbol: str, side: str, quantity: float, price: float, strategy: str = ""):
        """Log trade entry"""
        message = f"TRADE ENTRY | {symbol} | {side.upper()} {quantity} @ ${price:.4f}"
        if strategy:
            message += f" | Strategy: {strategy}"
        self.logger.info(message)
    
    def trade_exit(self, symbol: str, side: str, quantity: float, price: float, pnl: float, reason: str = ""):
        """Log trade exit"""
        pnl_str = f"+${pnl:.2f}" if pnl >= 0 else f"-${abs(pnl):.2f}"
        message = f"TRADE EXIT | {symbol} | {side.upper()} {quantity} @ ${price:.4f} | PnL: {pnl_str}"
        if reason:
            message += f" | Reason: {reason}"
        self.logger.info(message)
    
    def order_status(self, order_id: str, symbol: str, status: str, details: str = ""):
        """Log order status update"""
        message = f"ORDER {status.upper()} | {order_id} | {symbol}"
        if details:
            message += f" | {details}"
        self.logger.info(message)
    
    def scanner_result(self, symbol: str, signal_type: str, confidence: float, details: dict = None):
        """Log scanner results"""
        message = f"SCANNER HIT | {symbol} | {signal_type} | Confidence: {confidence:.2%}"
        if details:
            detail_str = " | ".join([f"{k}: {v}" for k, v in details.items()])
            message += f" | {detail_str}"
        self.logger.info(message)
    
    def performance_summary(self, trades_today: int, pnl_today: float, win_rate: float, avg_profit: float):
        """Log daily performance summary"""
        message = (f"DAILY SUMMARY | Trades: {trades_today} | "
                  f"PnL: ${pnl_today:.2f} | Win Rate: {win_rate:.1%} | "
                  f"Avg Profit: ${avg_profit:.2f}")
        self.logger.info(message)
    
    def risk_alert(self, alert_type: str, message: str, severity: str = "WARNING"):
        """Log risk management alerts"""
        log_message = f"RISK ALERT [{severity}] | {alert_type} | {message}"
        if severity.upper() == "CRITICAL":
            self.logger.critical(log_message)
        else:
            self.logger.warning(log_message)

# Global logger instance
logger = TradingLogger()

# Convenience functions
def log_info(message: str, **kwargs):
    logger.info(message, **kwargs)

def log_debug(message: str, **kwargs):
    logger.debug(message, **kwargs)

def log_warning(message: str, **kwargs):
    logger.warning(message, **kwargs)

def log_error(message: str, **kwargs):
    logger.error(message, **kwargs)

def log_critical(message: str, **kwargs):
    logger.critical(message, **kwargs)

def log_trade_entry(symbol: str, side: str, quantity: float, price: float, strategy: str = ""):
    logger.trade_entry(symbol, side, quantity, price, strategy)

def log_trade_exit(symbol: str, side: str, quantity: float, price: float, pnl: float, reason: str = ""):
    logger.trade_exit(symbol, side, quantity, price, pnl, reason)

def log_scanner_hit(symbol: str, signal_type: str, confidence: float, details: dict = None):
    logger.scanner_result(symbol, signal_type, confidence, details)

def log_performance(trades_today: int, pnl_today: float, win_rate: float, avg_profit: float):
    logger.performance_summary(trades_today, pnl_today, win_rate, avg_profit)

def log_risk_alert(alert_type: str, message: str, severity: str = "WARNING"):
    logger.risk_alert(alert_type, message, severity)

if __name__ == "__main__":
    # Test logging
    log_info("Trading bot logger initialized")
    log_trade_entry("AAPL", "BUY", 10, 150.25, "Momentum Breakout")
    log_scanner_hit("TSLA", "Volume Surge", 0.85, {"volume_ratio": 3.2, "price_change": 0.015})
    log_trade_exit("AAPL", "SELL", 10, 151.25, 10.00, "Target Profit")
    log_performance(25, 18.50, 0.72, 0.74)
    log_risk_alert("Position Size", "Position exceeds maximum allowed size", "WARNING")
