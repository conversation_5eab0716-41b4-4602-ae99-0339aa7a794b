"""
Database operations for the MassiveScan trading bot.
Handles SQLite database for storing trades, positions, and performance data.
"""

import sqlite3
import json
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from contextlib import contextmanager

from models import Trade, Order, TradingSignal, DailyStats, OrderSide, OrderStatus, TradeStatus, SignalType
from logger import log_info, log_error, log_debug

class TradingDatabase:
    """Database manager for trading bot"""
    
    def __init__(self, db_path: str = "trading_bot.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize database tables"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Trades table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS trades (
                        id TEXT PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        side TEXT NOT NULL,
                        quantity REAL NOT NULL,
                        entry_price REAL NOT NULL,
                        exit_price REAL,
                        entry_time TEXT NOT NULL,
                        exit_time TEXT,
                        pnl REAL DEFAULT 0,
                        commission REAL DEFAULT 0,
                        net_pnl REAL DEFAULT 0,
                        status TEXT DEFAULT 'open',
                        strategy TEXT,
                        exit_reason TEXT,
                        entry_order_id TEXT,
                        exit_order_id TEXT,
                        signal_data TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Orders table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS orders (
                        id TEXT PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        side TEXT NOT NULL,
                        order_type TEXT NOT NULL,
                        quantity REAL NOT NULL,
                        price REAL,
                        stop_price REAL,
                        status TEXT DEFAULT 'pending',
                        created_at TEXT NOT NULL,
                        filled_at TEXT,
                        filled_price REAL,
                        filled_quantity REAL DEFAULT 0,
                        broker_order_id TEXT,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Signals table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS signals (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        signal_type TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        confidence REAL NOT NULL,
                        entry_price REAL NOT NULL,
                        target_price REAL NOT NULL,
                        stop_loss_price REAL NOT NULL,
                        expected_profit REAL NOT NULL,
                        risk_reward_ratio REAL NOT NULL,
                        volume_ratio REAL,
                        technical_data TEXT,
                        acted_upon INTEGER DEFAULT 0,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Daily stats table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS daily_stats (
                        date TEXT PRIMARY KEY,
                        total_trades INTEGER DEFAULT 0,
                        winning_trades INTEGER DEFAULT 0,
                        losing_trades INTEGER DEFAULT 0,
                        total_pnl REAL DEFAULT 0,
                        gross_profit REAL DEFAULT 0,
                        gross_loss REAL DEFAULT 0,
                        largest_win REAL DEFAULT 0,
                        largest_loss REAL DEFAULT 0,
                        avg_win REAL DEFAULT 0,
                        avg_loss REAL DEFAULT 0,
                        win_rate REAL DEFAULT 0,
                        profit_factor REAL DEFAULT 0,
                        total_commission REAL DEFAULT 0,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Performance tracking table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS performance_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        metric_name TEXT NOT NULL,
                        metric_value REAL NOT NULL,
                        additional_data TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create indexes for better performance
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trades(symbol)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_entry_time ON trades(entry_time)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_orders_symbol ON orders(symbol)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_signals_symbol ON signals(symbol)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_signals_timestamp ON signals(timestamp)")
                
                conn.commit()
                log_info("Database initialized successfully")
                
        except Exception as e:
            log_error(f"Failed to initialize database: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get database connection with context manager"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        try:
            yield conn
        finally:
            conn.close()
    
    def save_trade(self, trade: Trade) -> bool:
        """Save trade to database"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO trades (
                        id, symbol, side, quantity, entry_price, exit_price,
                        entry_time, exit_time, pnl, commission, net_pnl,
                        status, strategy, exit_reason, entry_order_id,
                        exit_order_id, signal_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    trade.id, trade.symbol, trade.side.value, trade.quantity,
                    trade.entry_price, trade.exit_price,
                    trade.entry_time.isoformat(),
                    trade.exit_time.isoformat() if trade.exit_time else None,
                    trade.pnl, trade.commission, trade.net_pnl,
                    trade.status.value, trade.strategy, trade.exit_reason,
                    trade.entry_order_id, trade.exit_order_id,
                    json.dumps(trade.signal_data) if trade.signal_data else None
                ))
                
                conn.commit()
                log_debug(f"Trade saved: {trade.symbol} {trade.side.value} {trade.quantity}")
                return True
                
        except Exception as e:
            log_error(f"Failed to save trade: {e}")
            return False
    
    def save_order(self, order: Order) -> bool:
        """Save order to database"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO orders (
                        id, symbol, side, order_type, quantity, price,
                        stop_price, status, created_at, filled_at,
                        filled_price, filled_quantity, broker_order_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    order.id, order.symbol, order.side.value, order.order_type.value,
                    order.quantity, order.price, order.stop_price, order.status.value,
                    order.created_at.isoformat(),
                    order.filled_at.isoformat() if order.filled_at else None,
                    order.filled_price, order.filled_quantity, order.broker_order_id
                ))
                
                conn.commit()
                log_debug(f"Order saved: {order.symbol} {order.side.value} {order.quantity}")
                return True
                
        except Exception as e:
            log_error(f"Failed to save order: {e}")
            return False
    
    def save_signal(self, signal: TradingSignal) -> bool:
        """Save trading signal to database"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO signals (
                        symbol, signal_type, timestamp, confidence,
                        entry_price, target_price, stop_loss_price,
                        expected_profit, risk_reward_ratio, volume_ratio,
                        technical_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    signal.symbol, signal.signal_type.value,
                    signal.timestamp.isoformat(), signal.confidence,
                    signal.entry_price, signal.target_price, signal.stop_loss_price,
                    signal.expected_profit, signal.risk_reward_ratio,
                    signal.volume_ratio,
                    json.dumps(signal.technical_data) if signal.technical_data else None
                ))
                
                conn.commit()
                log_debug(f"Signal saved: {signal.symbol} {signal.signal_type.value}")
                return True
                
        except Exception as e:
            log_error(f"Failed to save signal: {e}")
            return False
    
    def get_open_trades(self) -> List[Trade]:
        """Get all open trades"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM trades WHERE status = 'open'")
                rows = cursor.fetchall()
                
                trades = []
                for row in rows:
                    trade = Trade(
                        id=row['id'],
                        symbol=row['symbol'],
                        side=OrderSide(row['side']),
                        quantity=row['quantity'],
                        entry_price=row['entry_price'],
                        exit_price=row['exit_price'],
                        entry_time=datetime.fromisoformat(row['entry_time']),
                        exit_time=datetime.fromisoformat(row['exit_time']) if row['exit_time'] else None,
                        pnl=row['pnl'],
                        commission=row['commission'],
                        net_pnl=row['net_pnl'],
                        status=TradeStatus(row['status']),
                        strategy=row['strategy'] or "",
                        exit_reason=row['exit_reason'] or "",
                        entry_order_id=row['entry_order_id'],
                        exit_order_id=row['exit_order_id'],
                        signal_data=json.loads(row['signal_data']) if row['signal_data'] else None
                    )
                    trades.append(trade)
                
                return trades
                
        except Exception as e:
            log_error(f"Failed to get open trades: {e}")
            return []
    
    def get_trades_by_date(self, trade_date: date) -> List[Trade]:
        """Get trades for a specific date"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                date_str = trade_date.isoformat()
                cursor.execute("""
                    SELECT * FROM trades 
                    WHERE date(entry_time) = ?
                    ORDER BY entry_time
                """, (date_str,))
                rows = cursor.fetchall()
                
                trades = []
                for row in rows:
                    trade = Trade(
                        id=row['id'],
                        symbol=row['symbol'],
                        side=OrderSide(row['side']),
                        quantity=row['quantity'],
                        entry_price=row['entry_price'],
                        exit_price=row['exit_price'],
                        entry_time=datetime.fromisoformat(row['entry_time']),
                        exit_time=datetime.fromisoformat(row['exit_time']) if row['exit_time'] else None,
                        pnl=row['pnl'],
                        commission=row['commission'],
                        net_pnl=row['net_pnl'],
                        status=TradeStatus(row['status']),
                        strategy=row['strategy'] or "",
                        exit_reason=row['exit_reason'] or "",
                        entry_order_id=row['entry_order_id'],
                        exit_order_id=row['exit_order_id'],
                        signal_data=json.loads(row['signal_data']) if row['signal_data'] else None
                    )
                    trades.append(trade)
                
                return trades
                
        except Exception as e:
            log_error(f"Failed to get trades by date: {e}")
            return []
    
    def update_daily_stats(self, stats: DailyStats) -> bool:
        """Update daily statistics"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO daily_stats (
                        date, total_trades, winning_trades, losing_trades,
                        total_pnl, gross_profit, gross_loss, largest_win,
                        largest_loss, avg_win, avg_loss, win_rate,
                        profit_factor, total_commission
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    stats.date.date().isoformat(), stats.total_trades,
                    stats.winning_trades, stats.losing_trades, stats.total_pnl,
                    stats.gross_profit, stats.gross_loss, stats.largest_win,
                    stats.largest_loss, stats.avg_win, stats.avg_loss,
                    stats.win_rate, stats.profit_factor, stats.total_commission
                ))
                
                conn.commit()
                log_debug(f"Daily stats updated for {stats.date.date()}")
                return True
                
        except Exception as e:
            log_error(f"Failed to update daily stats: {e}")
            return False
    
    def get_performance_summary(self, days: int = 30) -> Dict[str, Any]:
        """Get performance summary for the last N days"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get summary statistics
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_trades,
                        SUM(CASE WHEN net_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                        SUM(net_pnl) as total_pnl,
                        AVG(net_pnl) as avg_pnl,
                        MAX(net_pnl) as max_win,
                        MIN(net_pnl) as max_loss,
                        SUM(commission) as total_commission
                    FROM trades 
                    WHERE entry_time >= date('now', '-{} days')
                    AND status = 'closed'
                """.format(days))
                
                row = cursor.fetchone()
                
                if row and row['total_trades'] > 0:
                    win_rate = row['winning_trades'] / row['total_trades']
                    return {
                        'total_trades': row['total_trades'],
                        'winning_trades': row['winning_trades'],
                        'losing_trades': row['total_trades'] - row['winning_trades'],
                        'win_rate': win_rate,
                        'total_pnl': row['total_pnl'],
                        'avg_pnl': row['avg_pnl'],
                        'max_win': row['max_win'],
                        'max_loss': row['max_loss'],
                        'total_commission': row['total_commission'],
                        'days': days
                    }
                else:
                    return {
                        'total_trades': 0,
                        'winning_trades': 0,
                        'losing_trades': 0,
                        'win_rate': 0.0,
                        'total_pnl': 0.0,
                        'avg_pnl': 0.0,
                        'max_win': 0.0,
                        'max_loss': 0.0,
                        'total_commission': 0.0,
                        'days': days
                    }
                    
        except Exception as e:
            log_error(f"Failed to get performance summary: {e}")
            return {}

# Global database instance
db = TradingDatabase()

if __name__ == "__main__":
    # Test database operations
    from models import TradingSignal, SignalType
    
    # Test signal saving
    signal = TradingSignal(
        symbol="AAPL",
        signal_type=SignalType.MOMENTUM_BREAKOUT,
        timestamp=datetime.now(),
        confidence=0.85,
        entry_price=150.00,
        target_price=151.00,
        stop_loss_price=149.50,
        expected_profit=1.00,
        risk_reward_ratio=2.0
    )
    
    db.save_signal(signal)
    print("Signal saved successfully")
    
    # Test performance summary
    summary = db.get_performance_summary(7)
    print("Performance summary:", summary)
