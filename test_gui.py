"""
Simple test to check if the GUI start button is working.
This will help diagnose the issue.
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time
import asyncio

class TestBot:
    """Simple test bot to verify GUI functionality"""
    
    def __init__(self):
        self.running = False
    
    async def initialize(self):
        """Test initialization"""
        print("Test bot initializing...")
        await asyncio.sleep(2)  # Simulate initialization
        print("Test bot initialized!")
        return True
    
    async def run(self):
        """Test run method"""
        print("Test bot starting...")
        
        if not await self.initialize():
            print("Test bot initialization failed")
            return
        
        self.running = True
        print("Test bot is now running!")
        
        # Simulate scanning
        scan_count = 0
        while self.running:
            scan_count += 1
            print(f"Test scan #{scan_count} - Finding signals...")
            await asyncio.sleep(5)  # Scan every 5 seconds
    
    def stop(self):
        """Stop the test bot"""
        print("Test bot stopping...")
        self.running = False

class TestGUI:
    """Simple test GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("MassiveScan - Test GUI")
        self.root.geometry("600x400")
        
        self.bot = None
        self.bot_thread = None
        self.running = False
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create test widgets"""
        # Title
        title_label = tk.Label(self.root, text="MassiveScan Test GUI", 
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # Status
        self.status_label = tk.Label(self.root, text="Status: Stopped", 
                                    font=('Arial', 12))
        self.status_label.pack(pady=10)
        
        # Buttons
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        self.start_button = tk.Button(button_frame, text="Start Test Bot", 
                                     command=self.start_bot,
                                     bg='#4CAF50', fg='white', 
                                     font=('Arial', 12, 'bold'),
                                     width=15)
        self.start_button.pack(side='left', padx=10)
        
        self.stop_button = tk.Button(button_frame, text="Stop Test Bot", 
                                    command=self.stop_bot,
                                    bg='#F44336', fg='white', 
                                    font=('Arial', 12, 'bold'),
                                    width=15, state='disabled')
        self.stop_button.pack(side='left', padx=10)
        
        # Log area
        log_frame = tk.Frame(self.root)
        log_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        tk.Label(log_frame, text="Activity Log:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        self.log_text = tk.Text(log_frame, height=15, bg='black', fg='white', 
                               font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True)
        
        # Start status updates
        self.update_status()
    
    def log_message(self, message):
        """Add message to log"""
        timestamp = time.strftime("%H:%M:%S")
        log_line = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_line)
        self.log_text.see(tk.END)
        print(log_line.strip())  # Also print to console
    
    def start_bot(self):
        """Start the test bot"""
        try:
            self.log_message("Start button clicked!")
            
            if self.running:
                messagebox.showwarning("Warning", "Test bot is already running!")
                return
            
            self.log_message("Creating test bot...")
            self.bot = TestBot()
            self.running = True
            
            self.log_message("Starting bot in background thread...")
            self.bot_thread = threading.Thread(target=self.run_bot_async, daemon=True)
            self.bot_thread.start()
            
            # Update UI
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')
            self.status_label.config(text="Status: Starting...")
            
            self.log_message("✅ Test bot started successfully!")
            
        except Exception as e:
            error_msg = f"❌ Failed to start test bot: {e}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)
    
    def run_bot_async(self):
        """Run the bot asynchronously"""
        try:
            self.log_message("Setting up async event loop...")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            self.log_message("Running test bot...")
            loop.run_until_complete(self.bot.run())
            
        except Exception as e:
            error_msg = f"❌ Bot execution error: {e}"
            self.log_message(error_msg)
        finally:
            self.running = False
            self.log_message("Bot thread finished")
    
    def stop_bot(self):
        """Stop the test bot"""
        try:
            self.log_message("Stop button clicked!")
            
            if not self.running:
                messagebox.showwarning("Warning", "Test bot is not running!")
                return
            
            if self.bot:
                self.log_message("Stopping test bot...")
                self.bot.stop()
            
            self.running = False
            
            # Update UI
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
            self.status_label.config(text="Status: Stopped")
            
            self.log_message("✅ Test bot stopped successfully!")
            
        except Exception as e:
            error_msg = f"❌ Failed to stop test bot: {e}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)
    
    def update_status(self):
        """Update status display"""
        if self.running:
            self.status_label.config(text="Status: Running", fg='green')
        else:
            self.status_label.config(text="Status: Stopped", fg='red')
        
        # Schedule next update
        self.root.after(1000, self.update_status)
    
    def run(self):
        """Run the test GUI"""
        self.log_message("Test GUI started")
        self.log_message("Click 'Start Test Bot' to test the functionality")
        
        try:
            self.root.mainloop()
        except Exception as e:
            print(f"GUI error: {e}")

if __name__ == "__main__":
    print("Starting MassiveScan Test GUI...")
    app = TestGUI()
    app.run()
