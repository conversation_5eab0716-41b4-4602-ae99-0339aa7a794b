"""
Live Trading Configuration Summary
$30K Account - $100 Daily Target
"""

from config import config

def show_live_trading_config():
    """Display live trading configuration"""
    print("🚀 LIVE TRADING CONFIGURATION")
    print("=" * 60)
    print("REAL MONEY - $30K ACCOUNT - $100 DAILY TARGET")
    print("=" * 60)
    
    print("\n💰 ACCOUNT SETUP:")
    print(f"  API Key: {config.api.alpaca_api_key[:8]}...")
    print(f"  Base URL: {config.api.alpaca_base_url}")
    print(f"  Account Size: $30,000")
    print(f"  Daily Target: $100")
    print("  Status: LIVE TRADING ENABLED")
    
    print("\n🎯 OPTIMIZED SETTINGS:")
    print(f"  Target Profit: ${config.trading.target_profit_dollars:.2f} per trade")
    print(f"  Max Profit: ${config.trading.max_profit_target:.2f} per trade")
    print(f"  Risk Per Trade: ${config.trading.risk_per_trade_dollars:.2f}")
    print(f"  Stop Loss: ${config.trading.stop_loss_dollars:.2f}")
    print(f"  Max Position: ${config.trading.max_position_size:,.0f}")
    
    print("\n📊 TRADING LIMITS:")
    print(f"  Max Daily Trades: {config.trading.max_daily_trades}")
    print(f"  Max Positions: {config.trading.max_concurrent_positions}")
    print(f"  Max Daily Loss: ${config.trading.max_daily_loss:.2f}")
    print(f"  Min Confidence: {config.trading.min_signal_confidence:.0%}")
    
    print("\n🤖 AI ENHANCEMENTS:")
    print(f"  AI Enhancement: {config.trading.use_ai_enhancement}")
    print(f"  Market Regime Detection: {config.trading.ai_market_regime_detection}")
    print(f"  Profit Prediction: {config.trading.ai_profit_prediction}")
    print(f"  ATR Stops: {config.trading.use_atr_stops}")
    
    print("\n⚡ SCANNING SPEED:")
    print(f"  Scan Interval: {config.system.scan_interval_seconds} seconds")
    print(f"  P&L Updates: {config.system.pnl_refresh_rate/1000:.1f} seconds")
    print(f"  GUI Refresh: {config.system.gui_refresh_rate/1000:.1f} seconds")
    
    print("\n📈 PERFORMANCE TARGETS:")
    
    # Calculate performance estimates for $100 target
    trades_per_day = config.trading.max_daily_trades
    avg_profit = (config.trading.target_profit_dollars + config.trading.max_profit_target) / 2
    avg_loss = config.trading.risk_per_trade_dollars
    win_rate = 0.75  # Expected with AI enhancement
    
    winning_trades = trades_per_day * win_rate
    losing_trades = trades_per_day * (1 - win_rate)
    
    gross_profit = winning_trades * avg_profit
    gross_loss = losing_trades * avg_loss
    net_profit = gross_profit - gross_loss
    
    print(f"  Target Trades: {trades_per_day}/day")
    print(f"  Expected Win Rate: {win_rate:.0%}")
    print(f"  Average Win: ${avg_profit:.2f}")
    print(f"  Average Loss: ${avg_loss:.2f}")
    print(f"  Expected Daily Profit: ${net_profit:.2f}")
    
    if net_profit >= 100:
        print("  🎉 $100 TARGET ACHIEVABLE!")
    else:
        needed_trades = 100 / (avg_profit * win_rate - avg_loss * (1 - win_rate))
        print(f"  📈 Need ~{needed_trades:.0f} trades for $100")
    
    print("\n🛡️ RISK MANAGEMENT:")
    account_size = 30000
    max_risk_per_trade = config.trading.risk_per_trade_dollars / account_size * 100
    max_daily_risk = config.trading.max_daily_loss / account_size * 100
    
    print(f"  Risk per trade: {max_risk_per_trade:.2f}% of account")
    print(f"  Max daily risk: {max_daily_risk:.2f}% of account")
    print(f"  Position sizing: ATR-based volatility adjustment")
    print(f"  Stop losses: Always active")
    
    print("\n⚠️ LIVE TRADING WARNINGS:")
    print("  🔴 THIS IS REAL MONEY TRADING")
    print("  🔴 Monitor the bot closely")
    print("  🔴 Stop if unexpected behavior")
    print("  🔴 Losses are real and permanent")
    print("  🔴 Start with small position sizes")
    
    print("\n✅ SAFETY FEATURES:")
    print("  • Daily loss limits enforced")
    print("  • Position size limits active")
    print("  • Stop losses on every trade")
    print("  • AI risk scoring")
    print("  • Real-time monitoring")
    
    print("\n" + "=" * 60)
    print("READY FOR LIVE TRADING! 🚀💰")
    print("Target: $100 daily profit with $30K account")
    print("=" * 60)

def show_trading_plan():
    """Show the trading plan for $100 daily target"""
    print("\n📋 TRADING PLAN FOR $100 DAILY:")
    print("=" * 50)
    
    print("STRATEGY:")
    print("  • AI-enhanced signal detection")
    print("  • ATR-based position sizing")
    print("  • $3-12 profit targets per trade")
    print("  • $15 risk per trade maximum")
    print("  • Trailing stops to ride winners")
    
    print("\nEXECUTION:")
    print("  • Scan every 15 seconds")
    print("  • Execute 25-40 trades per day")
    print("  • Hold 5-10 positions simultaneously")
    print("  • Exit at profit targets or stops")
    
    print("\nMONITORING:")
    print("  • Real-time P&L updates")
    print("  • Live position tracking")
    print("  • AI decision logging")
    print("  • Risk limit enforcement")
    
    print("\nSUCCESS METRICS:")
    print("  • Daily profit: $100+")
    print("  • Win rate: 75%+")
    print("  • Max drawdown: <$75")
    print("  • Sharpe ratio: >2.0")

if __name__ == "__main__":
    show_live_trading_config()
    show_trading_plan()
    
    print("\n🎯 TO START LIVE TRADING:")
    print("  python gui.py        # Live trading GUI")
    print("  python start_bot.py  # Live command line")
    
    print("\n⚠️ FINAL WARNING:")
    print("  This bot will trade with REAL MONEY")
    print("  Monitor closely and be prepared to stop if needed")
    print("  Start conservatively and scale up gradually")
    
    print("\n🚀 Ready to make $100 today with your $30K account!")
