2025-06-11 08:22:05 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:22:05 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:29:48 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:29:48 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:29:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:29:56 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:29:56 | INFO     | MassiveScan | info:70 |   Total trades: 0
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:29:57 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:29:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:04 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:09 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:14 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:19 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:24 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:29 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:34 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:39 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:44 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:49 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:30:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:04 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:10 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:15 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:20 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:25 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:30 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:35 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:40 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:45 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:50 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:31:55 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:00 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:05 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:11 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:16 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:21 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:32:26 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:34:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JOBY | rsi_overbought | Confidence: 64.96% | entry_price: 9.915 | target_price: 9.905 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:34:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:34:08 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GILD | rsi_oversold | Confidence: 59.98% | entry_price: 109.98 | target_price: 109.************** | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:09 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EEM | rsi_overbought | Confidence: 50.34% | entry_price: 47.83 | target_price: 47.82 | expected_profit: 1.0 | risk_reward: 1.***************
2025-06-11 08:34:15 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SBUX | rsi_overbought | Confidence: 70.95% | entry_price: 93.4568 | target_price: 93.4468 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:24 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:31 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SLV | rsi_oversold | Confidence: 63.16% | entry_price: 32.9399 | target_price: 32.9499 | expected_profit: 1.0 | risk_reward: 1.***************
2025-06-11 08:34:33 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BIL | vwap_bounce | Confidence: 80.00% | entry_price: 91.5307 | target_price: 91.52069999999999 | expected_profit: 1.0 | risk_reward: 2.511842290125823
2025-06-11 08:34:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | momentum_breakout | Confidence: 95.00% | entry_price: 17.98 | target_price: 17.97 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | volume_surge | Confidence: 90.00% | entry_price: 17.98 | target_price: 17.97 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MP | rsi_oversold | Confidence: 78.61% | entry_price: 23.91 | target_price: 23.92 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDD | rsi_overbought | Confidence: 57.64% | entry_price: 103.92 | target_price: 103.91 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:39 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | FCX | rsi_oversold | Confidence: 64.42% | entry_price: 40.93 | target_price: 40.94 | expected_profit: 1.0 | risk_reward: 1.***************
2025-06-11 08:34:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:34:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BDX | rsi_overbought | Confidence: 56.05% | entry_price: 175.56 | target_price: 175.55 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 08:34:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PFE | rsi_overbought | Confidence: 60.57% | entry_price: 24.635 | target_price: 24.625 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:44 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SAIL | rsi_overbought | Confidence: 82.02% | entry_price: 22.875 | target_price: 22.865 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:45 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BILI | rsi_overbought | Confidence: 79.20% | entry_price: 21.2 | target_price: 21.189999999999998 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:51 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMSC | momentum_breakout | Confidence: 86.51% | entry_price: 28.03 | target_price: 28.02 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:51 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMSC | rsi_oversold | Confidence: 80.45% | entry_price: 28.03 | target_price: 28.040000000000003 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:34:53 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TIGR | rsi_overbought | Confidence: 74.01% | entry_price: 8.805 | target_price: 8.795 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:34:53 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:34:58 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CLF | rsi_oversold | Confidence: 69.93% | entry_price: 7.335 | target_price: 7.345 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 08:35:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:35:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CSCO | rsi_oversold | Confidence: 56.36% | entry_price: 64.29 | target_price: 64.************** | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:36:27 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:36:27 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:32 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:36:37 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:37 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 |   Total trades: 0
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Trade monitoring stopped
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | Closing all positions: Bot shutdown
2025-06-11 08:36:37 | INFO     | MassiveScan | info:70 | All positions closed
2025-06-11 08:36:42 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:47 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:52 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:36:57 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:02 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:07 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:13 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:23 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:37:25 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:37:26 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:28 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:33 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:38 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:43 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:48 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:53 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:58 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:37:59 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:38:03 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:38:06 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 | Broker connected - Buying Power: $296,867.68
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Target profit per trade: $1.0
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Stop loss per trade: $0.5
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Max daily trades: 100
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Max daily loss limit: $25.0
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Max concurrent positions: 10
2025-06-11 08:38:07 | INFO     | MassiveScan | info:70 |   Expected trades for $50: 50 trades
2025-06-11 08:38:08 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:13 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | Broker connected - Buying Power: $296,867.68
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 | TRADING STRATEGY - Target: $50 Daily Profit
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Target profit per trade: $1.0
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Stop loss per trade: $0.5
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Max daily trades: 100
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Max daily loss limit: $25.0
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Max concurrent positions: 10
2025-06-11 08:38:23 | INFO     | MassiveScan | info:70 |   Expected trades for $50: 50 trades
2025-06-11 08:38:24 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | IREN | vwap_bounce | Confidence: 80.00% | entry_price: 10.245 | target_price: 10.*************** | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:38:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BILI | rsi_overbought | Confidence: 62.42% | entry_price: 21.17 | target_price: 21.16 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:38:29 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:32 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:38:34 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | HBAN | vwap_bounce | Confidence: 80.00% | entry_price: 16.285 | target_price: 16.295 | expected_profit: 1.0 | risk_reward: 4.***************
2025-06-11 08:38:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AAL | vwap_bounce | Confidence: 80.00% | entry_price: 11.8287 | target_price: 11.8187 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:38:38 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CLF | rsi_oversold | Confidence: 62.68% | entry_price: 7.27 | target_price: 7.*************** | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 08:38:39 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:44 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VALE | vwap_bounce | Confidence: 80.00% | entry_price: 9.565 | target_price: 9.555 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:38:49 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:38:59 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:39:04 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:09 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:13 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:39:14 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:19 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:24 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:29 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:35 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:38 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | XPEV | rsi_overbought | Confidence: 51.42% | entry_price: 20.955 | target_price: 20.*************** | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Buying Power: $296,867.68
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.00, Trades: 0, Open: 0
2025-06-11 08:39:39 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 08:39:40 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:42 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:39:45 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:49 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BILI | rsi_overbought | Confidence: 57.71% | entry_price: 21.09 | target_price: 21.08 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:39:50 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:52 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:39:55 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:39:55 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for IREN (risk per share: $0.0059)
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for IREN (risk per share: $0.0059)
2025-06-11 08:39:56 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Market order placed: IREN buy 19
2025-06-11 08:39:56 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:39:56 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | IREN | BUY 19 @ $10.2450 | Strategy: vwap_bounce
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Trade executed: e1972db6-6cdf-4ea0-be9a-ffc7ce41cae2 - IREN buy 19
2025-06-11 08:39:56 | INFO     | MassiveScan | info:70 | Trade executed: IREN - Total trades today: 1
2025-06-11 08:39:57 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:39:58 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:39:58 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:39:58 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:39:58 | INFO     | MassiveScan | info:70 | Position size calculated: 12 shares for HBAN (risk per share: $0.0022)
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Position size calculated: 12 shares for HBAN (risk per share: $0.0022)
2025-06-11 08:39:59 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Market order placed: HBAN buy 12
2025-06-11 08:39:59 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:39:59 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | HBAN | BUY 12 @ $16.2850 | Strategy: vwap_bounce
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Trade executed: 4c7d2f26-9579-4ec7-aff5-dc7d381b67eb - HBAN buy 12
2025-06-11 08:39:59 | INFO     | MassiveScan | info:70 | Trade executed: HBAN - Total trades today: 2
2025-06-11 08:40:00 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:00 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for AAL (risk per share: $0.0044)
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for AAL (risk per share: $0.0044)
2025-06-11 08:40:01 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Market order placed: AAL buy 16
2025-06-11 08:40:01 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:40:01 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AAL | BUY 16 @ $11.8287 | Strategy: vwap_bounce
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Trade executed: 1c724dad-5db4-4430-af23-d8b6a36d91dc - AAL buy 16
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Trade executed: AAL - Total trades today: 3
2025-06-11 08:40:01 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for VALE (risk per share: $0.0060)
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for VALE (risk per share: $0.0060)
2025-06-11 08:40:02 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Market order placed: VALE buy 20
2025-06-11 08:40:02 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:40:02 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | VALE | BUY 20 @ $9.5650 | Strategy: vwap_bounce
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Trade executed: d8c4bdb5-1242-4014-8677-f24de9febc15 - VALE buy 20
2025-06-11 08:40:02 | INFO     | MassiveScan | info:70 | Trade executed: VALE - Total trades today: 4
2025-06-11 08:40:03 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:40:04 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:40:04 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:40:04 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:40:04 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:40:05 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:05 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:40:05 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:40:05 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:40:06 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:40:07 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:40:07 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 08:40:07 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 08:40:07 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CLF (risk per share: $0.0050)
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CLF (risk per share: $0.0050)
2025-06-11 08:40:08 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Market order placed: CLF buy 27
2025-06-11 08:40:08 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CLF | BUY 27 @ $7.2700 | Strategy: rsi_oversold
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Trade executed: 890f00ff-db12-4042-b11e-1f5a7fe66f3b - CLF buy 27
2025-06-11 08:40:08 | INFO     | MassiveScan | info:70 | Trade executed: CLF - Total trades today: 5
2025-06-11 08:40:09 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for BILI (risk per share: $0.0050)
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for BILI (risk per share: $0.0050)
2025-06-11 08:40:10 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Market order placed: BILI sell 9
2025-06-11 08:40:10 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | BILI | SELL 9 @ $21.1700 | Strategy: rsi_overbought
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Trade executed: 58586d0e-5c34-44c4-a682-bceb0a15e2f0 - BILI sell 9
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Trade executed: BILI - Total trades today: 6
2025-06-11 08:40:10 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:10 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for XPEV (risk per share: $0.0050)
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for XPEV (risk per share: $0.0050)
2025-06-11 08:40:11 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Market order placed: XPEV sell 9
2025-06-11 08:40:11 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | XPEV | SELL 9 @ $20.9550 | Strategy: rsi_overbought
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Trade executed: ae7c92da-ae73-4825-a513-101c800cebdc - XPEV sell 9
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Trade executed: XPEV - Total trades today: 7
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Starting trade monitoring...
2025-06-11 08:40:11 | INFO     | MassiveScan | info:70 | Trade monitoring started
2025-06-11 08:40:12 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:12 | INFO     | MassiveScan | info:70 | Market order placed: IREN sell 19
2025-06-11 08:40:12 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:40:12 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:40:12 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | IREN | SELL 19 @ $10.1500 | PnL: -$1.80 | Reason: Stop loss triggered
2025-06-11 08:40:12 | INFO     | MassiveScan | info:70 | Trade closed: e1972db6-6cdf-4ea0-be9a-ffc7ce41cae2 - IREN - P&L: $-1.80
2025-06-11 08:40:12 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:40:15 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:18 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:40:18 | INFO     | MassiveScan | info:70 | Market order placed: BILI buy 9
2025-06-11 08:40:18 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:40:18 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | BILI | BUY 9 @ $20.9550 | PnL: +$1.94 | Reason: Profit target reached
2025-06-11 08:40:18 | INFO     | MassiveScan | info:70 | Trade closed: 58586d0e-5c34-44c4-a682-bceb0a15e2f0 - BILI - P&L: $1.94
2025-06-11 08:40:18 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:40:20 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:25 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.79 | target_price: 46.78 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:40:30 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:35 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:39 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:40:40 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:41 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BAC | vwap_bounce | Confidence: 80.00% | entry_price: 45.1018 | target_price: 45.*************** | expected_profit: 1.0 | risk_reward: 41.**************
2025-06-11 08:40:46 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:51 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:40:56 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:01 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:01 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MRNA | vwap_bounce | Confidence: 80.00% | entry_price: 28.085 | target_price: 28.075 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:06 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:07 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SGOV | vwap_bounce | Confidence: 80.00% | entry_price: 100.47 | target_price: 100.46 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:41:11 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:12 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | WBD | rsi_overbought | Confidence: 56.47% | entry_price: 10.465 | target_price: 10.455 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:16 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:41:21 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:23 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RKT | rsi_overbought | Confidence: 54.77% | entry_price: 13.53 | target_price: 13.52 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:41:26 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:31 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AG | vwap_bounce | Confidence: 80.00% | entry_price: 8.4 | target_price: 8.39 | expected_profit: 1.0 | risk_reward: 5.***************
2025-06-11 08:41:36 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:41 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:46 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:41:51 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:41:56 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:02 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:42:07 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:12 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:17 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:17 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0045)
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0045)
2025-06-11 08:42:18 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Market order placed: SWTX buy 4
2025-06-11 08:42:18 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:18 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SWTX | BUY 4 @ $46.7900 | Strategy: vwap_bounce
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Trade executed: ********-d29e-43e0-afb8-fe4655b9aba6 - SWTX buy 4
2025-06-11 08:42:18 | INFO     | MassiveScan | info:70 | Trade executed: SWTX - Total trades today: 8
2025-06-11 08:42:19 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:42:19 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:42:19 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:42:19 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:42:20 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for BAC (risk per share: $0.0002)
2025-06-11 08:42:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for BAC (risk per share: $0.0002)
2025-06-11 08:42:21 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Market order placed: BAC buy 4
2025-06-11 08:42:21 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:21 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | BAC | BUY 4 @ $45.1018 | Strategy: vwap_bounce
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Trade executed: e038bceb-7257-4c4d-8dc9-11f38d28ba6e - BAC buy 4
2025-06-11 08:42:21 | INFO     | MassiveScan | info:70 | Trade executed: BAC - Total trades today: 9
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for MRNA (risk per share: $0.0057)
2025-06-11 08:42:22 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for MRNA (risk per share: $0.0057)
2025-06-11 08:42:22 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Market order placed: MRNA buy 7
2025-06-11 08:42:22 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:22 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | MRNA | BUY 7 @ $28.0850 | Strategy: vwap_bounce
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Trade executed: 624f7de8-a1b0-43f4-82c8-f6a1bae9fd87 - MRNA buy 7
2025-06-11 08:42:22 | INFO     | MassiveScan | info:70 | Trade executed: MRNA - Total trades today: 10
2025-06-11 08:42:23 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:42:24 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:42:24 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:42:24 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for SGOV (risk per share: $0.0044)
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for SGOV (risk per share: $0.0044)
2025-06-11 08:42:25 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Market order placed: SGOV buy 1
2025-06-11 08:42:25 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:25 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SGOV | BUY 1 @ $100.4700 | Strategy: vwap_bounce
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Trade executed: c30a0e92-da7e-404a-b9e4-73cdd08e598e - SGOV buy 1
2025-06-11 08:42:25 | INFO     | MassiveScan | info:70 | Trade executed: SGOV - Total trades today: 11
2025-06-11 08:42:26 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:42:27 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:42:27 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:27 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:42:27 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:42:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RKT | rsi_overbought | Confidence: 60.16% | entry_price: 13.56 | target_price: 13.55 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Position size calculated: 23 shares for AG (risk per share: $0.0017)
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Position size calculated: 23 shares for AG (risk per share: $0.0017)
2025-06-11 08:42:28 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Market order placed: AG buy 23
2025-06-11 08:42:28 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:28 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AG | BUY 23 @ $8.4000 | Strategy: vwap_bounce
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Trade executed: 052ff125-2ed7-477d-b91e-41e4ec40e545 - AG buy 23
2025-06-11 08:42:28 | INFO     | MassiveScan | info:70 | Trade executed: AG - Total trades today: 12
2025-06-11 08:42:29 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:42:30 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:42:30 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 08:42:30 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 08:42:31 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:42:31 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:42:31 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 08:42:31 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 08:42:32 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:32 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for WBD (risk per share: $0.0050)
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for WBD (risk per share: $0.0050)
2025-06-11 08:42:33 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Market order placed: WBD sell 19
2025-06-11 08:42:33 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | WBD | SELL 19 @ $10.4650 | Strategy: rsi_overbought
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Trade executed: 825074ef-cebf-4c57-81a1-243fdec69b6b - WBD sell 19
2025-06-11 08:42:33 | INFO     | MassiveScan | info:70 | Trade executed: WBD - Total trades today: 13
2025-06-11 08:42:34 | INFO     | MassiveScan | info:70 | Position size calculated: 14 shares for RKT (risk per share: $0.0050)
2025-06-11 08:42:34 | INFO     | MassiveScan | info:70 | Position size calculated: 14 shares for RKT (risk per share: $0.0050)
2025-06-11 08:42:34 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":42210000,"message":"asset \"RKT\" cannot be sold short"}
2025-06-11 08:42:34 | ERROR    | MassiveScan | error:82 | Failed to place entry order for RKT
2025-06-11 08:42:35 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:35 | INFO     | MassiveScan | info:70 | Market order placed: HBAN sell 12
2025-06-11 08:42:35 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:42:35 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:42:35 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | HBAN | SELL 12 @ $16.3850 | PnL: +$1.20 | Reason: Profit target reached
2025-06-11 08:42:35 | INFO     | MassiveScan | info:70 | Trade closed: 4c7d2f26-9579-4ec7-aff5-dc7d381b67eb - HBAN - P&L: $1.20
2025-06-11 08:42:35 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:42:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | volume_surge | Confidence: 73.65% | entry_price: 18.59 | target_price: 18.6 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:42:37 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:42 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:43 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:42:43 | INFO     | MassiveScan | info:70 | Market order placed: WBD buy 19
2025-06-11 08:42:43 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:42:43 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | WBD | BUY 19 @ $10.5300 | PnL: -$1.23 | Reason: Stop loss triggered
2025-06-11 08:42:43 | INFO     | MassiveScan | info:70 | Trade closed: 825074ef-cebf-4c57-81a1-243fdec69b6b - WBD - P&L: $-1.23
2025-06-11 08:42:43 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:42:46 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:42:47 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:52 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:42:57 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:02 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:43:07 | INFO     | MassiveScan | info:70 | Market order placed: MRNA sell 7
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:43:07 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:43:07 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | MRNA | SELL 7 @ $27.9975 | PnL: -$0.61 | Reason: Stop loss triggered
2025-06-11 08:43:07 | INFO     | MassiveScan | info:70 | Trade closed: 624f7de8-a1b0-43f4-82c8-f6a1bae9fd87 - MRNA - P&L: $-0.61
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:43:07 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:12 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:23 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:24 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.78 | target_price: 46.79 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:43:28 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:30 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KGC | vwap_bounce | Confidence: 80.00% | entry_price: 14.99 | target_price: 14.98 | expected_profit: 1.0 | risk_reward: 7.***************
2025-06-11 08:43:33 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:43:33 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:38 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:43 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:44 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AVDX | vwap_bounce | Confidence: 80.00% | entry_price: 9.785 | target_price: 9.795 | expected_profit: 1.0 | risk_reward: 5.***************
2025-06-11 08:43:48 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:53 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:54 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMCR | vwap_bounce | Confidence: 80.00% | entry_price: 9.22 | target_price: 9.23 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:43:57 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:43:57 | INFO     | MassiveScan | info:70 |   Total trades: 2
2025-06-11 08:43:58 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:01 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:44:03 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:07 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | WBD | rsi_overbought | Confidence: 61.40% | entry_price: 10.505 | target_price: 10.*************** | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:44:08 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:13 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:44:13 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:18 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:23 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:28 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:33 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:37 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MSTU | vwap_bounce | Confidence: 80.00% | entry_price: 8.275 | target_price: 8.285 | expected_profit: 1.0 | risk_reward: 4.***************
2025-06-11 08:44:39 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:42 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:44:44 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:49 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:54 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:44:59 | ERROR    | MassiveScan | error:82 | Failed to get account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-11 08:45:01 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:45:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | NCLH | vwap_bounce | Confidence: 80.00% | entry_price: 19.7099 | target_price: 19.*************** | expected_profit: 1.0 | risk_reward: 6.****************
2025-06-11 08:45:12 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0046)
2025-06-11 08:45:13 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0046)
2025-06-11 08:45:13 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:13 | INFO     | MassiveScan | info:70 | Market order placed: SWTX buy 4
2025-06-11 08:45:13 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:13 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SWTX | BUY 4 @ $46.7800 | Strategy: vwap_bounce
2025-06-11 08:45:13 | INFO     | MassiveScan | info:70 | Trade executed: 6de2eb8b-b8ce-464b-9816-9daae14c9439 - SWTX buy 4
2025-06-11 08:45:13 | INFO     | MassiveScan | info:70 | Trade executed: SWTX - Total trades today: 14
2025-06-11 08:45:14 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for KGC (risk per share: $0.0014)
2025-06-11 08:45:15 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for KGC (risk per share: $0.0014)
2025-06-11 08:45:15 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:15 | INFO     | MassiveScan | info:70 | Market order placed: KGC buy 13
2025-06-11 08:45:15 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:15 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | KGC | BUY 13 @ $14.9900 | Strategy: vwap_bounce
2025-06-11 08:45:15 | INFO     | MassiveScan | info:70 | Trade executed: b29305f4-83b4-46cb-bbaf-23af49b347f3 - KGC buy 13
2025-06-11 08:45:15 | INFO     | MassiveScan | info:70 | Trade executed: KGC - Total trades today: 15
2025-06-11 08:45:15 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:45:16 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:45:16 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:45:16 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:45:17 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for AVDX (risk per share: $0.0019)
2025-06-11 08:45:18 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for AVDX (risk per share: $0.0019)
2025-06-11 08:45:18 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:18 | INFO     | MassiveScan | info:70 | Market order placed: AVDX buy 20
2025-06-11 08:45:18 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:18 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AVDX | BUY 20 @ $9.7850 | Strategy: vwap_bounce
2025-06-11 08:45:18 | INFO     | MassiveScan | info:70 | Trade executed: 6556f842-a814-45a0-9db3-fab9218890f7 - AVDX buy 20
2025-06-11 08:45:18 | INFO     | MassiveScan | info:70 | Trade executed: AVDX - Total trades today: 16
2025-06-11 08:45:18 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0049)
2025-06-11 08:45:19 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0049)
2025-06-11 08:45:19 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:19 | INFO     | MassiveScan | info:70 | Market order placed: AMCR buy 21
2025-06-11 08:45:19 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:19 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AMCR | BUY 21 @ $9.2200 | Strategy: vwap_bounce
2025-06-11 08:45:19 | INFO     | MassiveScan | info:70 | Trade executed: 64d7f1b3-0667-4960-bcd5-5f68bed4c275 - AMCR buy 21
2025-06-11 08:45:19 | INFO     | MassiveScan | info:70 | Trade executed: AMCR - Total trades today: 17
2025-06-11 08:45:20 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:45:21 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:45:21 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:45:21 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:45:21 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:45:22 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:45:22 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:45:22 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:45:23 | INFO     | MassiveScan | info:70 | Position size calculated: 24 shares for MSTU (risk per share: $0.0023)
2025-06-11 08:45:24 | INFO     | MassiveScan | info:70 | Position size calculated: 24 shares for MSTU (risk per share: $0.0023)
2025-06-11 08:45:24 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:24 | INFO     | MassiveScan | info:70 | Market order placed: MSTU buy 24
2025-06-11 08:45:24 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:24 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | MSTU | BUY 24 @ $8.2750 | Strategy: vwap_bounce
2025-06-11 08:45:24 | INFO     | MassiveScan | info:70 | Trade executed: ce6ae5ac-282d-42f0-ac77-7d818dd3491f - MSTU buy 24
2025-06-11 08:45:24 | INFO     | MassiveScan | info:70 | Trade executed: MSTU - Total trades today: 18
2025-06-11 08:45:24 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:45:25 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:45:25 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 08:45:25 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 08:45:26 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:45:27 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:45:27 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 08:45:27 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 08:45:27 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for NCLH (risk per share: $0.0016)
2025-06-11 08:45:28 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for NCLH (risk per share: $0.0016)
2025-06-11 08:45:28 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:28 | INFO     | MassiveScan | info:70 | Market order placed: NCLH buy 10
2025-06-11 08:45:28 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:28 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | NCLH | BUY 10 @ $19.7099 | Strategy: vwap_bounce
2025-06-11 08:45:28 | INFO     | MassiveScan | info:70 | Trade executed: 497f571b-92d2-4542-b260-f283a23a08a4 - NCLH buy 10
2025-06-11 08:45:28 | INFO     | MassiveScan | info:70 | Trade executed: NCLH - Total trades today: 19
2025-06-11 08:45:29 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for WBD (risk per share: $0.0050)
2025-06-11 08:45:30 | INFO     | MassiveScan | info:70 | Position size calculated: 19 shares for WBD (risk per share: $0.0050)
2025-06-11 08:45:30 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:30 | INFO     | MassiveScan | info:70 | Market order placed: WBD sell 19
2025-06-11 08:45:30 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | WBD | SELL 19 @ $10.5050 | Strategy: rsi_overbought
2025-06-11 08:45:30 | INFO     | MassiveScan | info:70 | Trade executed: 6c2e08d0-6750-4b6d-82b9-7fb6577413f9 - WBD sell 19
2025-06-11 08:45:30 | INFO     | MassiveScan | info:70 | Trade executed: WBD - Total trades today: 20
2025-06-11 08:45:31 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 | Market order placed: CLF sell 27
2025-06-11 08:45:31 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 3 consecutive losses
2025-06-11 08:45:31 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:45:31 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CLF | SELL 27 @ $7.2500 | PnL: -$0.54 | Reason: Stop loss triggered
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 | Trade closed: 890f00ff-db12-4042-b11e-1f5a7fe66f3b - CLF - P&L: $-0.54
2025-06-11 08:45:31 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:45:31 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 20 | PnL: $-1.06 | Win Rate: 33.3% | Avg Profit: $-0.05
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 |   Trades executed: 20
2025-06-11 08:45:31 | INFO     | MassiveScan | info:70 |   Active trades: 14
2025-06-11 08:45:31 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-0.05
2025-06-11 08:45:37 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:37 | INFO     | MassiveScan | info:70 | Market order placed: XPEV buy 9
2025-06-11 08:45:37 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:45:37 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | XPEV | BUY 9 @ $20.7750 | PnL: +$1.62 | Reason: Profit target reached
2025-06-11 08:45:37 | INFO     | MassiveScan | info:70 | Trade closed: ae7c92da-ae73-4825-a513-101c800cebdc - XPEV - P&L: $1.62
2025-06-11 08:45:37 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:45:43 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:45:43 | INFO     | MassiveScan | info:70 | Market order placed: AG sell 23
2025-06-11 08:45:43 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:45:43 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:45:43 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AG | SELL 23 @ $8.3500 | PnL: -$1.15 | Reason: Stop loss triggered
2025-06-11 08:45:43 | INFO     | MassiveScan | info:70 | Trade closed: 052ff125-2ed7-477d-b91e-41e4ec40e545 - AG - P&L: $-1.15
2025-06-11 08:45:43 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:45:45 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.78 | target_price: 46.77 | expected_profit: 1.0 | risk_reward: 1.9152790844122942
2025-06-11 08:45:52 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KGC | vwap_bounce | Confidence: 80.00% | entry_price: 14.93 | target_price: 14.92 | expected_profit: 1.0 | risk_reward: 2.047027352892517
2025-06-11 08:45:54 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:45:57 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AAL | vwap_bounce | Confidence: 80.00% | entry_price: 11.83 | target_price: 11.82 | expected_profit: 1.0 | risk_reward: 3.179894284366229
2025-06-11 08:46:00 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CMG | rsi_overbought | Confidence: 50.44% | entry_price: 51.51 | target_price: 51.5 | expected_profit: 1.0 | risk_reward: 1.***************
2025-06-11 08:46:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AFRM | rsi_overbought | Confidence: 51.22% | entry_price: 63.33 | target_price: 63.32 | expected_profit: 1.0 | risk_reward: 1.***************
2025-06-11 08:46:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | VALE | vwap_bounce | Confidence: 80.00% | entry_price: 9.575 | target_price: 9.565 | expected_profit: 1.0 | risk_reward: 2.528326772050059
2025-06-11 08:46:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMCR | vwap_bounce | Confidence: 80.00% | entry_price: 9.235 | target_price: 9.225 | expected_profit: 1.0 | risk_reward: 106.95252729566566
2025-06-11 08:46:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | INTR | vwap_bounce | Confidence: 80.00% | entry_price: 6.8201 | target_price: 6.8101 | expected_profit: 1.0 | risk_reward: 2.361959068484969
2025-06-11 08:46:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACHC | vwap_bounce | Confidence: 80.00% | entry_price: 22.37 | target_price: 22.380000000000003 | expected_profit: 1.0 | risk_reward: 1.5041484552749396
2025-06-11 08:46:23 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:46:34 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EMLC | vwap_bounce | Confidence: 80.00% | entry_price: 24.785 | target_price: 24.775 | expected_profit: 1.0 | risk_reward: 4.142198211794595
2025-06-11 08:46:34 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KEY.TO | vwap_bounce | Confidence: 80.00% | entry_price: 42.0 | target_price: 42.01 | expected_profit: 1.0 | risk_reward: 1.50508557802368
2025-06-11 08:46:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:46:53 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GTLB | volume_surge | Confidence: 64.53% | entry_price: 43.43 | target_price: 43.42 | expected_profit: 1.0 | risk_reward: 1.***************
2025-06-11 08:46:59 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CORZ | vwap_bounce | Confidence: 80.00% | entry_price: 12.5864 | target_price: 12.5764 | expected_profit: 1.0 | risk_reward: 1.5502760979693015
2025-06-11 08:47:07 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:47:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RCAT | vwap_bounce | Confidence: 80.00% | entry_price: 8.7046 | target_price: 8.6946 | expected_profit: 1.0 | risk_reward: 1.7691840895337714
2025-06-11 08:47:26 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:47:38 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0052)
2025-06-11 08:47:39 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0052)
2025-06-11 08:47:39 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:39 | INFO     | MassiveScan | info:70 | Market order placed: SWTX buy 4
2025-06-11 08:47:39 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:39 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SWTX | BUY 4 @ $46.7800 | Strategy: vwap_bounce
2025-06-11 08:47:39 | INFO     | MassiveScan | info:70 | Trade executed: 7bd7307b-02c5-4977-b29e-28911231d629 - SWTX buy 4
2025-06-11 08:47:39 | INFO     | MassiveScan | info:70 | Trade executed: SWTX - Total trades today: 21
2025-06-11 08:47:40 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for KGC (risk per share: $0.0049)
2025-06-11 08:47:40 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for KGC (risk per share: $0.0049)
2025-06-11 08:47:40 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:40 | INFO     | MassiveScan | info:70 | Market order placed: KGC buy 13
2025-06-11 08:47:40 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:40 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | KGC | BUY 13 @ $14.9300 | Strategy: vwap_bounce
2025-06-11 08:47:40 | INFO     | MassiveScan | info:70 | Trade executed: 943bdd48-52aa-40fb-aac4-6bed2cc1f3a3 - KGC buy 13
2025-06-11 08:47:40 | INFO     | MassiveScan | info:70 | Trade executed: KGC - Total trades today: 22
2025-06-11 08:47:41 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:47:42 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:47:42 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:47:42 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:47:43 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for AAL (risk per share: $0.0031)
2025-06-11 08:47:43 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for AAL (risk per share: $0.0031)
2025-06-11 08:47:43 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:43 | INFO     | MassiveScan | info:70 | Market order placed: AAL buy 16
2025-06-11 08:47:43 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:43 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AAL | BUY 16 @ $11.8300 | Strategy: vwap_bounce
2025-06-11 08:47:43 | INFO     | MassiveScan | info:70 | Trade executed: 5fc5e926-67cc-4fa0-bbb4-c2316e44ab84 - AAL buy 16
2025-06-11 08:47:43 | INFO     | MassiveScan | info:70 | Trade executed: AAL - Total trades today: 23
2025-06-11 08:47:44 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for VALE (risk per share: $0.0040)
2025-06-11 08:47:45 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for VALE (risk per share: $0.0040)
2025-06-11 08:47:45 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:45 | INFO     | MassiveScan | info:70 | Market order placed: VALE buy 20
2025-06-11 08:47:45 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:45 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | VALE | BUY 20 @ $9.5750 | Strategy: vwap_bounce
2025-06-11 08:47:45 | INFO     | MassiveScan | info:70 | Trade executed: 44e58658-5381-43ce-9526-711c964f50cb - VALE buy 20
2025-06-11 08:47:45 | INFO     | MassiveScan | info:70 | Trade executed: VALE - Total trades today: 24
2025-06-11 08:47:46 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0001)
2025-06-11 08:47:46 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0001)
2025-06-11 08:47:47 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:47 | INFO     | MassiveScan | info:70 | Market order placed: AMCR buy 21
2025-06-11 08:47:47 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:47 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AMCR | BUY 21 @ $9.2350 | Strategy: vwap_bounce
2025-06-11 08:47:47 | INFO     | MassiveScan | info:70 | Trade executed: de5ed427-1bf0-4430-8ff6-03afd35266d9 - AMCR buy 21
2025-06-11 08:47:47 | INFO     | MassiveScan | info:70 | Trade executed: AMCR - Total trades today: 25
2025-06-11 08:47:47 | INFO     | MassiveScan | info:70 | Position size calculated: 29 shares for INTR (risk per share: $0.0042)
2025-06-11 08:47:48 | INFO     | MassiveScan | info:70 | Position size calculated: 29 shares for INTR (risk per share: $0.0042)
2025-06-11 08:47:48 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:48 | INFO     | MassiveScan | info:70 | Market order placed: INTR buy 29
2025-06-11 08:47:48 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:48 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | INTR | BUY 29 @ $6.8201 | Strategy: vwap_bounce
2025-06-11 08:47:48 | INFO     | MassiveScan | info:70 | Trade executed: 4bfa4117-b22b-4488-8258-88bbb82dd11b - INTR buy 29
2025-06-11 08:47:48 | INFO     | MassiveScan | info:70 | Trade executed: INTR - Total trades today: 26
2025-06-11 08:47:49 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for ACHC (risk per share: $0.0066)
2025-06-11 08:47:49 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for ACHC (risk per share: $0.0066)
2025-06-11 08:47:49 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:50 | INFO     | MassiveScan | info:70 | Market order placed: ACHC buy 8
2025-06-11 08:47:50 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:50 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | ACHC | BUY 8 @ $22.3700 | Strategy: vwap_bounce
2025-06-11 08:47:50 | INFO     | MassiveScan | info:70 | Trade executed: ffd86684-acc6-4e7a-b815-53300f28a14c - ACHC buy 8
2025-06-11 08:47:50 | INFO     | MassiveScan | info:70 | Trade executed: ACHC - Total trades today: 27
2025-06-11 08:47:50 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:47:51 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:47:51 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:47:51 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:47:52 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for EMLC (risk per share: $0.0024)
2025-06-11 08:47:52 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for EMLC (risk per share: $0.0024)
2025-06-11 08:47:52 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:53 | INFO     | MassiveScan | info:70 | Market order placed: EMLC buy 8
2025-06-11 08:47:53 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:53 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | EMLC | BUY 8 @ $24.7850 | Strategy: vwap_bounce
2025-06-11 08:47:53 | INFO     | MassiveScan | info:70 | Trade executed: 2b9aaba9-516f-4149-82b7-ccd0209f2a72 - EMLC buy 8
2025-06-11 08:47:53 | INFO     | MassiveScan | info:70 | Trade executed: EMLC - Total trades today: 28
2025-06-11 08:47:53 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for KEY.TO (risk per share: $0.0066)
2025-06-11 08:47:54 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for KEY.TO (risk per share: $0.0066)
2025-06-11 08:47:54 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":42210000,"message":"asset \"KEY.TO\" not found"}
2025-06-11 08:47:54 | ERROR    | MassiveScan | error:82 | Failed to place entry order for KEY.TO
2025-06-11 08:47:55 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:47:55 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:47:56 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:47:56 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:47:56 | INFO     | MassiveScan | info:70 | Position size calculated: 15 shares for CORZ (risk per share: $0.0065)
2025-06-11 08:47:57 | INFO     | MassiveScan | info:70 | Position size calculated: 15 shares for CORZ (risk per share: $0.0065)
2025-06-11 08:47:57 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:47:57 | INFO     | MassiveScan | info:70 | Market order placed: CORZ buy 15
2025-06-11 08:47:57 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:47:57 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CORZ | BUY 15 @ $12.5864 | Strategy: vwap_bounce
2025-06-11 08:47:57 | INFO     | MassiveScan | info:70 | Trade executed: 5824761c-35b1-4d82-bb10-c021d73bb84a - CORZ buy 15
2025-06-11 08:47:57 | INFO     | MassiveScan | info:70 | Trade executed: CORZ - Total trades today: 29
2025-06-11 08:47:58 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:47:58 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 08:47:59 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 08:47:59 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 08:47:59 | INFO     | MassiveScan | info:70 | Position size calculated: 22 shares for RCAT (risk per share: $0.0057)
2025-06-11 08:48:00 | INFO     | MassiveScan | info:70 | Position size calculated: 22 shares for RCAT (risk per share: $0.0057)
2025-06-11 08:48:00 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:48:00 | INFO     | MassiveScan | info:70 | Market order placed: RCAT buy 22
2025-06-11 08:48:00 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:48:00 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | RCAT | BUY 22 @ $8.7046 | Strategy: vwap_bounce
2025-06-11 08:48:00 | INFO     | MassiveScan | info:70 | Trade executed: f2d18c39-0507-4dfe-8210-ca7bf809d3ff - RCAT buy 22
2025-06-11 08:48:00 | INFO     | MassiveScan | info:70 | Trade executed: RCAT - Total trades today: 30
2025-06-11 08:48:01 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:48:01 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:48:02 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 08:48:02 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 08:48:02 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:48:03 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:48:03 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:48:03 | INFO     | MassiveScan | info:70 | Market order placed: GTLB buy 4
2025-06-11 08:48:03 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:48:03 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | GTLB | BUY 4 @ $43.4300 | Strategy: volume_surge
2025-06-11 08:48:03 | INFO     | MassiveScan | info:70 | Trade executed: 02475a17-3b4b-434a-83f4-7ad484314e28 - GTLB buy 4
2025-06-11 08:48:03 | INFO     | MassiveScan | info:70 | Trade executed: GTLB - Total trades today: 31
2025-06-11 08:48:04 | INFO     | MassiveScan | info:70 | Position size calculated: 3 shares for AFRM (risk per share: $0.0050)
2025-06-11 08:48:04 | INFO     | MassiveScan | info:70 | Position size calculated: 3 shares for AFRM (risk per share: $0.0050)
2025-06-11 08:48:05 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:48:05 | INFO     | MassiveScan | info:70 | Market order placed: AFRM sell 3
2025-06-11 08:48:05 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AFRM | SELL 3 @ $63.3300 | Strategy: rsi_overbought
2025-06-11 08:48:05 | INFO     | MassiveScan | info:70 | Trade executed: b77cf085-985e-45e9-9f40-3274dede9ca5 - AFRM sell 3
2025-06-11 08:48:05 | INFO     | MassiveScan | info:70 | Trade executed: AFRM - Total trades today: 32
2025-06-11 08:48:05 | INFO     | MassiveScan | info:70 | Position size calculated: 3 shares for CMG (risk per share: $0.0050)
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Position size calculated: 3 shares for CMG (risk per share: $0.0050)
2025-06-11 08:48:06 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Market order placed: CMG sell 3
2025-06-11 08:48:06 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CMG | SELL 3 @ $51.5100 | Strategy: rsi_overbought
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Trade executed: a11bcda1-13ad-44f0-9c62-6e062e1650d7 - CMG sell 3
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Trade executed: CMG - Total trades today: 33
2025-06-11 08:48:06 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Market order placed: AAL sell 16
2025-06-11 08:48:06 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:48:06 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:48:06 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AAL | SELL 16 @ $11.7900 | PnL: -$0.62 | Reason: Stop loss triggered
2025-06-11 08:48:06 | INFO     | MassiveScan | info:70 | Trade closed: 1c724dad-5db4-4430-af23-d8b6a36d91dc - AAL - P&L: $-0.62
2025-06-11 08:48:06 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:48:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:48:46 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:48:46 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CVE.TO | vwap_bounce | Confidence: 80.00% | entry_price: 19.06 | target_price: 19.049999999999997 | expected_profit: 1.0 | risk_reward: 6.040511336954411
2025-06-11 08:48:55 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EMLC | vwap_bounce | Confidence: 80.00% | entry_price: 24.785 | target_price: 24.775 | expected_profit: 1.0 | risk_reward: 4.193201950158175
2025-06-11 08:48:57 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:48:58 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | B | vwap_bounce | Confidence: 80.00% | entry_price: 20.145 | target_price: 20.155 | expected_profit: 1.0 | risk_reward: 1.6908064614923086
2025-06-11 08:49:00 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:49:01 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:49:01 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:49:01 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:49:02 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:49:02 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:49:03 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:49:03 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:49:03 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for CVE.TO (risk per share: $0.0017)
2025-06-11 08:49:04 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for CVE.TO (risk per share: $0.0017)
2025-06-11 08:49:04 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":42210000,"message":"asset \"CVE.TO\" not found"}
2025-06-11 08:49:04 | ERROR    | MassiveScan | error:82 | Failed to place entry order for CVE.TO
2025-06-11 08:49:05 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for EMLC (risk per share: $0.0024)
2025-06-11 08:49:05 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for EMLC (risk per share: $0.0024)
2025-06-11 08:49:06 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:49:06 | INFO     | MassiveScan | info:70 | Market order placed: EMLC buy 8
2025-06-11 08:49:06 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:49:06 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | EMLC | BUY 8 @ $24.7850 | Strategy: vwap_bounce
2025-06-11 08:49:06 | INFO     | MassiveScan | info:70 | Trade executed: 5af8bb3d-66b7-431c-904f-79972725e871 - EMLC buy 8
2025-06-11 08:49:06 | INFO     | MassiveScan | info:70 | Trade executed: EMLC - Total trades today: 34
2025-06-11 08:49:06 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:49:07 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:49:07 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:49:07 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:49:08 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for B (risk per share: $0.0059)
2025-06-11 08:49:08 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for B (risk per share: $0.0059)
2025-06-11 08:49:09 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:49:09 | INFO     | MassiveScan | info:70 | Market order placed: B buy 9
2025-06-11 08:49:09 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:49:09 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | B | BUY 9 @ $20.1450 | Strategy: vwap_bounce
2025-06-11 08:49:09 | INFO     | MassiveScan | info:70 | Trade executed: 6d0c57e6-bc4f-494e-831b-6d946a84123b - B buy 9
2025-06-11 08:49:09 | INFO     | MassiveScan | info:70 | Trade executed: B - Total trades today: 35
2025-06-11 08:49:11 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:49:11 | INFO     | MassiveScan | info:70 | Market order placed: MSTU sell 24
2025-06-11 08:49:11 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:49:11 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 3 consecutive losses
2025-06-11 08:49:11 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:49:11 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | MSTU | SELL 24 @ $8.2000 | PnL: -$1.80 | Reason: Stop loss triggered
2025-06-11 08:49:11 | INFO     | MassiveScan | info:70 | Trade closed: ce6ae5ac-282d-42f0-ac77-7d818dd3491f - MSTU - P&L: $-1.80
2025-06-11 08:49:11 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:49:19 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:49:19 | INFO     | MassiveScan | info:70 | Market order placed: NCLH sell 10
2025-06-11 08:49:19 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:49:19 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 4 consecutive losses
2025-06-11 08:49:19 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:49:19 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | NCLH | SELL 10 @ $19.6384 | PnL: -$0.72 | Reason: Stop loss triggered
2025-06-11 08:49:19 | INFO     | MassiveScan | info:70 | Trade closed: 497f571b-92d2-4542-b260-f283a23a08a4 - NCLH - P&L: $-0.72
2025-06-11 08:49:19 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:49:27 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:49:27 | INFO     | MassiveScan | info:70 | Market order placed: AAL sell 16
2025-06-11 08:49:27 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:49:27 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 5 consecutive losses
2025-06-11 08:49:27 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:49:27 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AAL | SELL 16 @ $11.7886 | PnL: -$0.66 | Reason: Stop loss triggered
2025-06-11 08:49:27 | INFO     | MassiveScan | info:70 | Trade closed: 5fc5e926-67cc-4fa0-bbb4-c2316e44ab84 - AAL - P&L: $-0.66
2025-06-11 08:49:27 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:49:32 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.78 | target_price: 46.79 | expected_profit: 1.0 | risk_reward: 2.111678215550129
2025-06-11 08:49:42 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:49:44 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | volume_surge | Confidence: 80.25% | entry_price: 18.405 | target_price: 18.415000000000003 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:49:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CMG | rsi_overbought | Confidence: 53.87% | entry_price: 51.525 | target_price: 51.515 | expected_profit: 1.0 | risk_reward: 1.***************
2025-06-11 08:50:14 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:50:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CVE.TO | vwap_bounce | Confidence: 80.00% | entry_price: 19.05 | target_price: 19.060000000000002 | expected_profit: 1.0 | risk_reward: 6.223809274559502
2025-06-11 08:50:23 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EMLC | vwap_bounce | Confidence: 80.00% | entry_price: 24.785 | target_price: 24.775 | expected_profit: 1.0 | risk_reward: 4.217766894956156
2025-06-11 08:50:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:50:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CDE | vwap_bounce | Confidence: 80.00% | entry_price: 9.34 | target_price: 9.35 | expected_profit: 1.0 | risk_reward: 9.588609997171313
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for OUST: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for SWTX: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for PDCO: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for JWN: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for CVE.TO: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for EMLC: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for PTVE: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for CDE: Too many consecutive losses: 5
2025-06-11 08:51:00 | WARNING  | MassiveScan | warning:78 | Trade rejected for CMG: Too many consecutive losses: 5
2025-06-11 08:51:04 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 35 | PnL: $-4.38 | Win Rate: 25.0% | Avg Profit: $-0.13
2025-06-11 08:51:04 | INFO     | MassiveScan | info:70 |   Trades executed: 35
2025-06-11 08:51:04 | INFO     | MassiveScan | info:70 |   Active trades: 23
2025-06-11 08:51:04 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-0.13
2025-06-11 08:51:08 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:51:08 | INFO     | MassiveScan | info:70 | Market order placed: WBD buy 19
2025-06-11 08:51:08 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.80
2025-06-11 08:51:08 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | WBD | BUY 19 @ $10.4500 | PnL: +$1.05 | Reason: Profit target reached
2025-06-11 08:51:08 | INFO     | MassiveScan | info:70 | Trade closed: 6c2e08d0-6750-4b6d-82b9-7fb6577413f9 - WBD - P&L: $1.05
2025-06-11 08:51:08 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:51:18 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:51:18 | INFO     | MassiveScan | info:70 | Market order placed: GTLB sell 4
2025-06-11 08:51:18 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:51:18 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:51:18 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | GTLB | SELL 4 @ $42.6250 | PnL: -$3.22 | Reason: Stop loss triggered
2025-06-11 08:51:18 | INFO     | MassiveScan | info:70 | Trade closed: 02475a17-3b4b-434a-83f4-7ad484314e28 - GTLB - P&L: $-3.22
2025-06-11 08:51:18 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:51:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PINS | vwap_bounce | Confidence: 80.00% | entry_price: 34.73 | target_price: 34.739999999999995 | expected_profit: 1.0 | risk_reward: 2.132202298829007
2025-06-11 08:51:41 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:51:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CONL | vwap_bounce | Confidence: 80.00% | entry_price: 26.155 | target_price: 26.145 | expected_profit: 1.0 | risk_reward: 4.040853066371055
2025-06-11 08:51:55 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AVDX | vwap_bounce | Confidence: 80.00% | entry_price: 9.785 | target_price: 9.795 | expected_profit: 1.0 | risk_reward: 5.361181703698685
2025-06-11 08:52:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:52:18 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SGOV | vwap_bounce | Confidence: 80.00% | entry_price: 100.47 | target_price: 100.46 | expected_profit: 1.0 | risk_reward: 2.3852803159165576
2025-06-11 08:52:31 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:52:32 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | B | vwap_bounce | Confidence: 80.00% | entry_price: 20.165 | target_price: 20.154999999999998 | expected_profit: 1.0 | risk_reward: 2.4261027683130356
2025-06-11 08:52:48 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GTLB | volume_surge | Confidence: 71.79% | entry_price: 42.875 | target_price: 42.885 | expected_profit: 1.0 | risk_reward: 1.***************
2025-06-11 08:53:01 | INFO     | MassiveScan | info:70 | Position size calculated: 5 shares for PINS (risk per share: $0.0047)
2025-06-11 08:53:02 | INFO     | MassiveScan | info:70 | Position size calculated: 5 shares for PINS (risk per share: $0.0047)
2025-06-11 08:53:02 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:02 | INFO     | MassiveScan | info:70 | Market order placed: PINS buy 5
2025-06-11 08:53:02 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:02 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | PINS | BUY 5 @ $34.7300 | Strategy: vwap_bounce
2025-06-11 08:53:02 | INFO     | MassiveScan | info:70 | Trade executed: c698c9aa-f38e-4a02-ba3d-d3380b165313 - PINS buy 5
2025-06-11 08:53:02 | INFO     | MassiveScan | info:70 | Trade executed: PINS - Total trades today: 36
2025-06-11 08:53:03 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:53:03 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:53:04 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:53:04 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:53:04 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for CONL (risk per share: $0.0025)
2025-06-11 08:53:05 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for CONL (risk per share: $0.0025)
2025-06-11 08:53:05 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:05 | INFO     | MassiveScan | info:70 | Market order placed: CONL buy 7
2025-06-11 08:53:05 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:05 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CONL | BUY 7 @ $26.1550 | Strategy: vwap_bounce
2025-06-11 08:53:05 | INFO     | MassiveScan | info:70 | Trade executed: 1989a6d2-f91d-435f-954b-6ee13ed766f9 - CONL buy 7
2025-06-11 08:53:05 | INFO     | MassiveScan | info:70 | Trade executed: CONL - Total trades today: 37
2025-06-11 08:53:06 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for AVDX (risk per share: $0.0019)
2025-06-11 08:53:06 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for AVDX (risk per share: $0.0019)
2025-06-11 08:53:07 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:07 | INFO     | MassiveScan | info:70 | Market order placed: AVDX buy 20
2025-06-11 08:53:07 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:07 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AVDX | BUY 20 @ $9.7850 | Strategy: vwap_bounce
2025-06-11 08:53:07 | INFO     | MassiveScan | info:70 | Trade executed: dd4a969e-c067-4a8b-a253-eecc629e96d1 - AVDX buy 20
2025-06-11 08:53:07 | INFO     | MassiveScan | info:70 | Trade executed: AVDX - Total trades today: 38
2025-06-11 08:53:07 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:53:08 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:53:08 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:53:08 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:53:09 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for SGOV (risk per share: $0.0042)
2025-06-11 08:53:09 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for SGOV (risk per share: $0.0042)
2025-06-11 08:53:10 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:10 | INFO     | MassiveScan | info:70 | Market order placed: SGOV buy 1
2025-06-11 08:53:10 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:10 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SGOV | BUY 1 @ $100.4700 | Strategy: vwap_bounce
2025-06-11 08:53:10 | INFO     | MassiveScan | info:70 | Trade executed: 04dc9b83-6772-4a08-ac2e-2465b2b77323 - SGOV buy 1
2025-06-11 08:53:10 | INFO     | MassiveScan | info:70 | Trade executed: SGOV - Total trades today: 39
2025-06-11 08:53:10 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:53:11 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:53:11 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:53:11 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:53:12 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for B (risk per share: $0.0041)
2025-06-11 08:53:12 | INFO     | MassiveScan | info:70 | Position size calculated: 9 shares for B (risk per share: $0.0041)
2025-06-11 08:53:13 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:13 | INFO     | MassiveScan | info:70 | Market order placed: B buy 9
2025-06-11 08:53:13 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:13 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | B | BUY 9 @ $20.1650 | Strategy: vwap_bounce
2025-06-11 08:53:13 | INFO     | MassiveScan | info:70 | Trade executed: 2e288114-2d3e-4945-a685-12643fa8e4f5 - B buy 9
2025-06-11 08:53:13 | INFO     | MassiveScan | info:70 | Trade executed: B - Total trades today: 40
2025-06-11 08:53:13 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:53:14 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:53:14 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:14 | INFO     | MassiveScan | info:70 | Market order placed: GTLB buy 4
2025-06-11 08:53:14 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:14 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | GTLB | BUY 4 @ $42.8750 | Strategy: volume_surge
2025-06-11 08:53:14 | INFO     | MassiveScan | info:70 | Trade executed: e06007b7-d2aa-42cb-9749-9577838c2dce - GTLB buy 4
2025-06-11 08:53:14 | INFO     | MassiveScan | info:70 | Trade executed: GTLB - Total trades today: 41
2025-06-11 08:53:15 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:15 | INFO     | MassiveScan | info:70 | Market order placed: VALE sell 20
2025-06-11 08:53:15 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:15 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:53:15 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | VALE | SELL 20 @ $9.6150 | PnL: +$1.00 | Reason: Profit target reached
2025-06-11 08:53:15 | INFO     | MassiveScan | info:70 | Trade closed: d8c4bdb5-1242-4014-8677-f24de9febc15 - VALE - P&L: $1.00
2025-06-11 08:53:15 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:53:23 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:23 | INFO     | MassiveScan | info:70 | Market order placed: INTR sell 29
2025-06-11 08:53:23 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:23 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:53:23 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | INTR | SELL 29 @ $6.8550 | PnL: +$1.01 | Reason: Profit target reached
2025-06-11 08:53:23 | INFO     | MassiveScan | info:70 | Trade closed: 4bfa4117-b22b-4488-8258-88bbb82dd11b - INTR - P&L: $1.01
2025-06-11 08:53:23 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:53:32 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:53:32 | INFO     | MassiveScan | info:70 | Market order placed: RCAT sell 22
2025-06-11 08:53:32 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:53:32 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:53:32 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | RCAT | SELL 22 @ $8.8100 | PnL: +$2.32 | Reason: Profit target reached
2025-06-11 08:53:32 | INFO     | MassiveScan | info:70 | Trade closed: f2d18c39-0507-4dfe-8210-ca7bf809d3ff - RCAT - P&L: $2.32
2025-06-11 08:53:32 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:53:34 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.78 | target_price: 46.77 | expected_profit: 1.0 | risk_reward: 1.9157739751647964
2025-06-11 08:53:43 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:54:18 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:54:29 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:54:51 | INFO     | MassiveScan | info:70 | Buying Power: $292,496.86
2025-06-11 08:54:51 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $2.83, Trades: 7, Open: 2
2025-06-11 08:54:51 | INFO     | MassiveScan | info:70 | Loaded 2 open trades
2025-06-11 08:54:59 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0052)
2025-06-11 08:55:00 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for SWTX (risk per share: $0.0052)
2025-06-11 08:55:00 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:55:00 | INFO     | MassiveScan | info:70 | Market order placed: SWTX buy 4
2025-06-11 08:55:00 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:55:00 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SWTX | BUY 4 @ $46.7800 | Strategy: vwap_bounce
2025-06-11 08:55:00 | INFO     | MassiveScan | info:70 | Trade executed: c373d943-fdfa-41f6-8708-af8196afacbd - SWTX buy 4
2025-06-11 08:55:00 | INFO     | MassiveScan | info:70 | Trade executed: SWTX - Total trades today: 42
2025-06-11 08:55:01 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:55:01 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:55:02 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:55:02 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:55:02 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:55:03 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:55:03 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:55:03 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:55:04 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:55:04 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:55:05 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:55:05 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:55:08 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:55:08 | INFO     | MassiveScan | info:70 | Market order placed: ACHC sell 8
2025-06-11 08:55:08 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:55:08 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:55:08 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | ACHC | SELL 8 @ $22.2900 | PnL: -$0.64 | Reason: Stop loss triggered
2025-06-11 08:55:08 | INFO     | MassiveScan | info:70 | Trade closed: ffd86684-acc6-4e7a-b815-53300f28a14c - ACHC - P&L: $-0.64
2025-06-11 08:55:08 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:55:16 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:55:16 | INFO     | MassiveScan | info:70 | Market order placed: AMCR sell 21
2025-06-11 08:55:16 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:55:16 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:55:16 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AMCR | SELL 21 @ $9.2100 | PnL: -$0.52 | Reason: Stop loss triggered
2025-06-11 08:55:16 | INFO     | MassiveScan | info:70 | Trade closed: de5ed427-1bf0-4430-8ff6-03afd35266d9 - AMCR - P&L: $-0.52
2025-06-11 08:55:16 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:55:25 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:55:25 | INFO     | MassiveScan | info:70 | Market order placed: CORZ sell 15
2025-06-11 08:55:25 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:55:25 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 3 consecutive losses
2025-06-11 08:55:25 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:55:25 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CORZ | SELL 15 @ $12.5400 | PnL: -$0.70 | Reason: Stop loss triggered
2025-06-11 08:55:25 | INFO     | MassiveScan | info:70 | Trade closed: 5824761c-35b1-4d82-bb10-c021d73bb84a - CORZ - P&L: $-0.70
2025-06-11 08:55:25 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Buying Power: $292,871.53
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $2.83, Trades: 7, Open: 2
2025-06-11 08:55:37 | INFO     | MassiveScan | info:70 | Loaded 2 open trades
2025-06-11 08:55:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CRWV | vwap_bounce | Confidence: 80.00% | entry_price: 155.615 | target_price: 155.60500000000002 | expected_profit: 1.0 | risk_reward: 2.5351962182541596
2025-06-11 08:55:41 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:55:43 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | YMM | vwap_bounce | Confidence: 80.00% | entry_price: 12.305 | target_price: 12.295 | expected_profit: 1.0 | risk_reward: 3.0807582823837887
2025-06-11 08:55:43 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | momentum_breakout | Confidence: 95.00% | entry_price: 18.805 | target_price: 18.815 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:55:43 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | volume_surge | Confidence: 73.77% | entry_price: 18.805 | target_price: 18.815 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 08:55:45 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:55:46 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BIL | vwap_bounce | Confidence: 80.00% | entry_price: 91.53 | target_price: 91.52 | expected_profit: 1.0 | risk_reward: 3.202570651099495
2025-06-11 08:55:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | BIL | vwap_bounce | Confidence: 80.00% | entry_price: 91.53 | target_price: 91.52 | expected_profit: 1.0 | risk_reward: 3.202570651099495
2025-06-11 08:55:57 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JBLU | vwap_bounce | Confidence: 80.00% | entry_price: 5.035 | target_price: 5.025 | expected_profit: 1.0 | risk_reward: 1.5548293900296364
2025-06-11 08:56:00 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.885 | target_price: 12.895 | expected_profit: 1.0 | risk_reward: 1.8825381703830102
2025-06-11 08:56:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMCR | vwap_bounce | Confidence: 80.00% | entry_price: 9.225 | target_price: 9.235 | expected_profit: 1.0 | risk_reward: 50.665341328784635
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | Broker connected - Buying Power: $292,871.53
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 | TRADING STRATEGY - Target: $50 Daily Profit
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Target profit per trade: $1.0
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Stop loss per trade: $0.5
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Max daily trades: 100
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Max daily loss limit: $25.0
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Max concurrent positions: 10
2025-06-11 08:56:08 | INFO     | MassiveScan | info:70 |   Expected trades for $50: 50 trades
2025-06-11 08:56:08 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.885 | target_price: 12.895 | expected_profit: 1.0 | risk_reward: 1.8825381703830102
2025-06-11 08:56:12 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:56:15 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TIGR | vwap_bounce | Confidence: 80.00% | entry_price: 8.605 | target_price: 8.615 | expected_profit: 1.0 | risk_reward: 2.269937195986822
2025-06-11 08:56:15 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JBLU | vwap_bounce | Confidence: 80.00% | entry_price: 5.0304 | target_price: 5.0204 | expected_profit: 1.0 | risk_reward: 5.459785398209257
2025-06-11 08:56:21 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:56:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AUR | vwap_bounce | Confidence: 80.00% | entry_price: 5.935 | target_price: 5.925 | expected_profit: 1.0 | risk_reward: 5.405701077446651
2025-06-11 08:56:23 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:56:24 | INFO     | MassiveScan | info:70 | Position size calculated: 39 shares for JBLU (risk per share: $0.0018)
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Position size calculated: 39 shares for JBLU (risk per share: $0.0018)
2025-06-11 08:56:25 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Market order placed: JBLU buy 39
2025-06-11 08:56:25 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:25 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | JBLU | BUY 39 @ $5.0304 | Strategy: vwap_bounce
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Trade executed: db64865a-6caa-47a2-87f5-b0a7869c0ebf - JBLU buy 39
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Trade executed: JBLU - Total trades today: 1
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 08:56:25 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Position size calculated: 33 shares for AUR (risk per share: $0.0018)
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 08:56:26 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Market order placed: OUST buy 10
2025-06-11 08:56:26 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:26 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | OUST | BUY 10 @ $18.8050 | Strategy: momentum_breakout
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Trade executed: 28b7405f-dad2-4828-a8e5-33f576b47494 - OUST buy 10
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Trade executed: OUST - Total trades today: 43
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Position size calculated: 33 shares for AUR (risk per share: $0.0018)
2025-06-11 08:56:26 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:56:27 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Market order placed: AUR buy 33
2025-06-11 08:56:27 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:27 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AUR | BUY 33 @ $5.9350 | Strategy: vwap_bounce
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Trade executed: 7d54fcc4-9e4a-422c-9db8-1dd88ca5c994 - AUR buy 33
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Trade executed: AUR - Total trades today: 2
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Starting trade monitoring...
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Trade monitoring started
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for CRWV (risk per share: $0.0039)
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:56:27 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 08:56:27 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 08:56:27 | INFO     | MassiveScan | info:70 | Position size calculated: 1 shares for CRWV (risk per share: $0.0039)
2025-06-11 08:56:28 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:28 | INFO     | MassiveScan | info:70 | Market order placed: CRWV buy 1
2025-06-11 08:56:28 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:28 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CRWV | BUY 1 @ $155.6150 | Strategy: vwap_bounce
2025-06-11 08:56:28 | INFO     | MassiveScan | info:70 | Trade executed: d7a8860f-d42c-4c52-a2df-11f7ce496a41 - CRWV buy 1
2025-06-11 08:56:28 | INFO     | MassiveScan | info:70 | Trade executed: CRWV - Total trades today: 44
2025-06-11 08:56:28 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:56:29 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 08:56:29 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 08:56:29 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 08:56:30 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for YMM (risk per share: $0.0032)
2025-06-11 08:56:30 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for YMM (risk per share: $0.0032)
2025-06-11 08:56:31 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:31 | INFO     | MassiveScan | info:70 | Market order placed: YMM buy 16
2025-06-11 08:56:31 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:31 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | YMM | BUY 16 @ $12.3050 | Strategy: vwap_bounce
2025-06-11 08:56:31 | INFO     | MassiveScan | info:70 | Trade executed: 00faab81-1ba3-4f02-8e28-d88a7601dd21 - YMM buy 16
2025-06-11 08:56:31 | INFO     | MassiveScan | info:70 | Trade executed: YMM - Total trades today: 45
2025-06-11 08:56:31 | INFO     | MassiveScan | info:70 | Position size calculated: 2 shares for BIL (risk per share: $0.0031)
2025-06-11 08:56:32 | INFO     | MassiveScan | info:70 | Position size calculated: 2 shares for BIL (risk per share: $0.0031)
2025-06-11 08:56:32 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:32 | INFO     | MassiveScan | info:70 | Market order placed: BIL buy 2
2025-06-11 08:56:32 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:32 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | BIL | BUY 2 @ $91.5300 | Strategy: vwap_bounce
2025-06-11 08:56:32 | INFO     | MassiveScan | info:70 | Trade executed: 0c118c30-bf4d-4559-9172-8687ca7d6738 - BIL buy 2
2025-06-11 08:56:32 | INFO     | MassiveScan | info:70 | Trade executed: BIL - Total trades today: 46
2025-06-11 08:56:33 | INFO     | MassiveScan | info:70 | Position size calculated: 39 shares for JBLU (risk per share: $0.0064)
2025-06-11 08:56:33 | INFO     | MassiveScan | info:70 | Position size calculated: 39 shares for JBLU (risk per share: $0.0064)
2025-06-11 08:56:34 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:34 | INFO     | MassiveScan | info:70 | Market order placed: JBLU buy 39
2025-06-11 08:56:34 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:34 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | JBLU | BUY 39 @ $5.0350 | Strategy: vwap_bounce
2025-06-11 08:56:34 | INFO     | MassiveScan | info:70 | Trade executed: 114ac2c3-2332-4f9b-8f17-344c2a4d3730 - JBLU buy 39
2025-06-11 08:56:34 | INFO     | MassiveScan | info:70 | Trade executed: JBLU - Total trades today: 47
2025-06-11 08:56:34 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0002)
2025-06-11 08:56:35 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0002)
2025-06-11 08:56:35 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:35 | INFO     | MassiveScan | info:70 | Market order placed: AMCR buy 21
2025-06-11 08:56:35 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:35 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AMCR | BUY 21 @ $9.2250 | Strategy: vwap_bounce
2025-06-11 08:56:35 | INFO     | MassiveScan | info:70 | Trade executed: 935f3dc2-c119-417e-8f19-d2b3dec5c9b0 - AMCR buy 21
2025-06-11 08:56:35 | INFO     | MassiveScan | info:70 | Trade executed: AMCR - Total trades today: 48
2025-06-11 08:56:36 | INFO     | MassiveScan | info:70 | Position size calculated: 15 shares for CNH (risk per share: $0.0053)
2025-06-11 08:56:36 | INFO     | MassiveScan | info:70 | Position size calculated: 15 shares for CNH (risk per share: $0.0053)
2025-06-11 08:56:37 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:37 | INFO     | MassiveScan | info:70 | Market order placed: CNH buy 15
2025-06-11 08:56:37 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:37 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CNH | BUY 15 @ $12.8850 | Strategy: vwap_bounce
2025-06-11 08:56:37 | INFO     | MassiveScan | info:70 | Trade executed: 875fd64a-9eb6-481b-a31a-35a448ce4920 - CNH buy 15
2025-06-11 08:56:37 | INFO     | MassiveScan | info:70 | Trade executed: CNH - Total trades today: 49
2025-06-11 08:56:37 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:56:38 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 08:56:38 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 08:56:38 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 08:56:39 | INFO     | MassiveScan | info:70 | Position size calculated: 23 shares for TIGR (risk per share: $0.0044)
2025-06-11 08:56:39 | INFO     | MassiveScan | info:70 | Position size calculated: 23 shares for TIGR (risk per share: $0.0044)
2025-06-11 08:56:40 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:40 | INFO     | MassiveScan | info:70 | Market order placed: TIGR buy 23
2025-06-11 08:56:40 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:40 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | TIGR | BUY 23 @ $8.6050 | Strategy: vwap_bounce
2025-06-11 08:56:40 | INFO     | MassiveScan | info:70 | Trade executed: f9a5d100-3b1a-480d-9ea8-32ca613f8c4f - TIGR buy 23
2025-06-11 08:56:40 | INFO     | MassiveScan | info:70 | Trade executed: TIGR - Total trades today: 50
2025-06-11 08:56:40 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:56:41 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 08:56:41 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 08:56:41 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 08:56:42 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 08:56:42 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 08:56:43 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:56:43 | INFO     | MassiveScan | info:70 | Market order placed: OUST buy 10
2025-06-11 08:56:43 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:56:43 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | OUST | BUY 10 @ $18.8050 | Strategy: volume_surge
2025-06-11 08:56:43 | INFO     | MassiveScan | info:70 | Trade executed: 255b5f1d-2846-419e-8e1b-2ba734381eb1 - OUST buy 10
2025-06-11 08:56:43 | INFO     | MassiveScan | info:70 | Trade executed: OUST - Total trades today: 51
2025-06-11 08:56:49 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 51 | PnL: $-4.09 | Win Rate: 35.0% | Avg Profit: $-0.08
2025-06-11 08:56:49 | INFO     | MassiveScan | info:70 |   Trades executed: 51
2025-06-11 08:56:49 | INFO     | MassiveScan | info:70 |   Active trades: 31
2025-06-11 08:56:49 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-0.08
2025-06-11 08:57:02 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:02 | INFO     | MassiveScan | info:70 | Market order placed: AFRM buy 3.0
2025-06-11 08:57:02 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 08:57:02 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AFRM | BUY 3.0 @ $63.8800 | PnL: -$1.65 | Reason: Stop loss triggered
2025-06-11 08:57:02 | INFO     | MassiveScan | info:70 | Trade closed: b77cf085-985e-45e9-9f40-3274dede9ca5 - AFRM - P&L: $-1.65
2025-06-11 08:57:02 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:04 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:04 | INFO     | MassiveScan | info:70 | Market order placed: AFRM buy 3.0
2025-06-11 08:57:04 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 08:57:04 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AFRM | BUY 3.0 @ $63.8800 | PnL: -$1.65 | Reason: Stop loss triggered
2025-06-11 08:57:04 | INFO     | MassiveScan | info:70 | Trade closed: b77cf085-985e-45e9-9f40-3274dede9ca5 - AFRM - P&L: $-1.65
2025-06-11 08:57:04 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:05 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:05 | INFO     | MassiveScan | info:70 | Market order placed: BAC sell 4
2025-06-11 08:57:05 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:57:05 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:05 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | BAC | SELL 4 @ $45.3999 | PnL: +$1.19 | Reason: Profit target reached
2025-06-11 08:57:05 | INFO     | MassiveScan | info:70 | Trade closed: e038bceb-7257-4c4d-8dc9-11f38d28ba6e - BAC - P&L: $1.19
2025-06-11 08:57:05 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:08 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:08 | INFO     | MassiveScan | info:70 | Market order placed: CMG buy 3.0
2025-06-11 08:57:08 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 08:57:08 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CMG | BUY 3.0 @ $51.7050 | PnL: -$0.59 | Reason: Stop loss triggered
2025-06-11 08:57:08 | INFO     | MassiveScan | info:70 | Trade closed: a11bcda1-13ad-44f0-9c62-6e062e1650d7 - CMG - P&L: $-0.59
2025-06-11 08:57:08 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:09 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:09 | INFO     | MassiveScan | info:70 | Market order placed: CMG buy 3.0
2025-06-11 08:57:09 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 08:57:09 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CMG | BUY 3.0 @ $51.7100 | PnL: -$0.60 | Reason: Stop loss triggered
2025-06-11 08:57:09 | INFO     | MassiveScan | info:70 | Trade closed: a11bcda1-13ad-44f0-9c62-6e062e1650d7 - CMG - P&L: $-0.60
2025-06-11 08:57:09 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:11 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:11 | INFO     | MassiveScan | info:70 | Market order placed: KGC sell 13
2025-06-11 08:57:11 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:57:11 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:11 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | KGC | SELL 13 @ $14.9350 | PnL: -$0.71 | Reason: Stop loss triggered
2025-06-11 08:57:11 | INFO     | MassiveScan | info:70 | Trade closed: b29305f4-83b4-46cb-bbaf-23af49b347f3 - KGC - P&L: $-0.71
2025-06-11 08:57:11 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:19 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:19 | INFO     | MassiveScan | info:70 | Market order placed: AFRM buy 3
2025-06-11 08:57:19 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:19 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AFRM | BUY 3 @ $63.8300 | PnL: -$1.50 | Reason: Stop loss triggered
2025-06-11 08:57:19 | INFO     | MassiveScan | info:70 | Trade closed: b77cf085-985e-45e9-9f40-3274dede9ca5 - AFRM - P&L: $-1.50
2025-06-11 08:57:19 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:26 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:26 | INFO     | MassiveScan | info:70 | Market order placed: CMG buy 3
2025-06-11 08:57:26 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 3 consecutive losses
2025-06-11 08:57:26 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:26 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CMG | BUY 3 @ $51.7100 | PnL: -$0.60 | Reason: Stop loss triggered
2025-06-11 08:57:26 | INFO     | MassiveScan | info:70 | Trade closed: a11bcda1-13ad-44f0-9c62-6e062e1650d7 - CMG - P&L: $-0.60
2025-06-11 08:57:26 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Buying Power: $290,878.24
2025-06-11 08:57:31 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.73, Trades: 7, Open: 0
2025-06-11 08:57:34 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CORZ | vwap_bounce | Confidence: 80.00% | entry_price: 12.58 | target_price: 12.57 | expected_profit: 1.0 | risk_reward: 45.850652375096075
2025-06-11 08:57:35 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:35 | INFO     | MassiveScan | info:70 | Market order placed: CONL sell 7
2025-06-11 08:57:35 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:57:35 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 4 consecutive losses
2025-06-11 08:57:35 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:35 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CONL | SELL 7 @ $25.9836 | PnL: -$1.20 | Reason: Stop loss triggered
2025-06-11 08:57:35 | INFO     | MassiveScan | info:70 | Trade closed: 1989a6d2-f91d-435f-954b-6ee13ed766f9 - CONL - P&L: $-1.20
2025-06-11 08:57:35 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:57:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GTLB | momentum_breakout | Confidence: 92.62% | entry_price: 43.32 | target_price: 43.31 | expected_profit: 1.0 | risk_reward: 1.***************
2025-06-11 08:57:36 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GTLB | volume_surge | Confidence: 78.81% | entry_price: 43.32 | target_price: 43.31 | expected_profit: 1.0 | risk_reward: 1.***************
2025-06-11 08:57:45 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:57:45 | INFO     | MassiveScan | info:70 | Market order placed: GTLB sell 4
2025-06-11 08:57:45 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:57:45 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:57:45 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | GTLB | SELL 4 @ $43.5700 | PnL: +$2.78 | Reason: Profit target reached
2025-06-11 08:57:45 | INFO     | MassiveScan | info:70 | Trade closed: e06007b7-d2aa-42cb-9749-9577838c2dce - GTLB - P&L: $2.78
2025-06-11 08:57:45 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:57:52 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.8793 | target_price: 12.8893 | expected_profit: 1.0 | risk_reward: 26.188186133005097
2025-06-11 08:57:58 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | KGC | vwap_bounce | Confidence: 80.00% | entry_price: 14.935 | target_price: 14.925 | expected_profit: 1.0 | risk_reward: 77.7897219953648
2025-06-11 08:58:00 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | UBER | vwap_bounce | Confidence: 80.00% | entry_price: 87.08 | target_price: 87.09 | expected_profit: 1.0 | risk_reward: 2.034703809653028
2025-06-11 08:58:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.875 | target_price: 12.885 | expected_profit: 1.0 | risk_reward: 2.1359070978003247
2025-06-11 08:58:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LYFT | vwap_bounce | Confidence: 80.00% | entry_price: 15.6762 | target_price: 15.6862 | expected_profit: 1.0 | risk_reward: 5.858067232158642
2025-06-11 08:58:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | MRVL | vwap_bounce | Confidence: 80.00% | entry_price: 69.51 | target_price: 69.5 | expected_profit: 1.0 | risk_reward: 1.822911045417119
2025-06-11 08:58:07 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:58:07 | INFO     | MassiveScan | info:70 | Market order placed: OUST sell 10
2025-06-11 08:58:07 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:58:07 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:58:07 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | OUST | SELL 10 @ $18.6950 | PnL: -$1.10 | Reason: Stop loss triggered
2025-06-11 08:58:07 | INFO     | MassiveScan | info:70 | Trade closed: 28b7405f-dad2-4828-a8e5-33f576b47494 - OUST - P&L: $-1.10
2025-06-11 08:58:07 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:58:10 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:58:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:58:19 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:58:19 | INFO     | MassiveScan | info:70 | Market order placed: OUST sell 10
2025-06-11 08:58:19 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:58:19 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:58:19 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | OUST | SELL 10 @ $18.7100 | PnL: -$0.95 | Reason: Stop loss triggered
2025-06-11 08:58:19 | INFO     | MassiveScan | info:70 | Trade closed: 255b5f1d-2846-419e-8e1b-2ba734381eb1 - OUST - P&L: $-0.95
2025-06-11 08:58:19 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:58:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 08:58:22 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CORZ | vwap_bounce | Confidence: 80.00% | entry_price: 12.58 | target_price: 12.57 | expected_profit: 1.0 | risk_reward: 45.30035471657295
2025-06-11 08:58:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:58:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 08:58:31 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:58:31 | INFO     | MassiveScan | info:70 | Position size calculated: 15 shares for CORZ (risk per share: $0.0002)
2025-06-11 08:58:32 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:58:32 | INFO     | MassiveScan | info:70 | Position size calculated: 4 shares for GTLB (risk per share: $0.0050)
2025-06-11 08:58:33 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:58:33 | INFO     | MassiveScan | info:70 | Market order placed: GTLB buy 4
2025-06-11 08:58:33 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:58:33 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | GTLB | BUY 4 @ $43.3200 | Strategy: momentum_breakout
2025-06-11 08:58:33 | INFO     | MassiveScan | info:70 | Trade executed: 67f1a3ce-1f83-4005-848d-6c00f6b5e1a1 - GTLB buy 4
2025-06-11 08:59:03 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:59:03 | INFO     | MassiveScan | info:70 | Market order placed: VALE sell 20
2025-06-11 08:59:03 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:59:03 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:59:03 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | VALE | SELL 20 @ $9.6311 | PnL: +$1.12 | Reason: Profit target reached
2025-06-11 08:59:03 | INFO     | MassiveScan | info:70 | Trade closed: 44e58658-5381-43ce-9526-711c964f50cb - VALE - P&L: $1.12
2025-06-11 08:59:03 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:59:13 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 08:59:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GLDM | vwap_bounce | Confidence: 80.00% | entry_price: 65.955 | target_price: 65.945 | expected_profit: 1.0 | risk_reward: 2.8426218852745464
2025-06-11 08:59:29 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.885 | target_price: 12.895 | expected_profit: 1.0 | risk_reward: 1.879002710845123
2025-06-11 08:59:38 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:59:38 | INFO     | MassiveScan | info:70 | Market order placed: AUR sell 33
2025-06-11 08:59:38 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:59:38 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 3 consecutive losses
2025-06-11 08:59:38 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 08:59:38 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AUR | SELL 33 @ $5.9150 | PnL: -$0.66 | Reason: Stop loss triggered
2025-06-11 08:59:38 | INFO     | MassiveScan | info:70 | Trade closed: 7d54fcc4-9e4a-422c-9db8-1dd88ca5c994 - AUR - P&L: $-0.66
2025-06-11 08:59:38 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:59:43 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LYFT | vwap_bounce | Confidence: 80.00% | entry_price: 15.685 | target_price: 15.675 | expected_profit: 1.0 | risk_reward: 20.080686418388996
2025-06-11 08:59:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 08:59:47 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 08:59:47 | INFO     | MassiveScan | info:70 | Market order placed: CRWV sell 1
2025-06-11 08:59:47 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 08:59:47 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 08:59:47 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CRWV | SELL 1 @ $155.0446 | PnL: -$0.57 | Reason: Stop loss triggered
2025-06-11 08:59:47 | INFO     | MassiveScan | info:70 | Trade closed: d7a8860f-d42c-4c52-a2df-11f7ce496a41 - CRWV - P&L: $-0.57
2025-06-11 08:59:47 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 08:59:51 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:59:52 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:59:53 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 08:59:53 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 08:59:53 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 09:00:09 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:00:09 | INFO     | MassiveScan | info:70 | Market order placed: GTLB sell 4
2025-06-11 09:00:09 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:00:09 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 09:00:09 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | GTLB | SELL 4 @ $44.0050 | PnL: +$2.74 | Reason: Profit target reached
2025-06-11 09:00:09 | INFO     | MassiveScan | info:70 | Trade closed: 67f1a3ce-1f83-4005-848d-6c00f6b5e1a1 - GTLB - P&L: $2.74
2025-06-11 09:00:09 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:00:32 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 09:00:47 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CNH | vwap_bounce | Confidence: 80.00% | entry_price: 12.88 | target_price: 12.89 | expected_profit: 1.0 | risk_reward: 31.308781168616708
2025-06-11 09:00:47 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:00:47 | INFO     | MassiveScan | info:70 | Market order placed: TIGR sell 23
2025-06-11 09:00:47 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:00:47 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 09:00:47 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | TIGR | SELL 23 @ $8.6550 | PnL: +$1.15 | Reason: Profit target reached
2025-06-11 09:00:47 | INFO     | MassiveScan | info:70 | Trade closed: f9a5d100-3b1a-480d-9ea8-32ca613f8c4f - TIGR - P&L: $1.15
2025-06-11 09:00:47 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:00:49 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ITUB | vwap_bounce | Confidence: 80.00% | entry_price: 6.5538 | target_price: 6.5638 | expected_profit: 1.0 | risk_reward: 1.7641658449506623
2025-06-11 09:01:08 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 09:01:18 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 09:01:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:01:27 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 2 | PnL: $-2.89 | Win Rate: 0.0% | Avg Profit: $-1.45
2025-06-11 09:01:27 | INFO     | MassiveScan | info:70 |   Trades executed: 2
2025-06-11 09:01:27 | INFO     | MassiveScan | info:70 |   Active trades: 1
2025-06-11 09:01:27 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-1.45
2025-06-11 09:01:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EWZ | rsi_overbought | Confidence: 59.56% | entry_price: 28.0401 | target_price: 28.030099999999997 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 09:01:35 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CLSK | vwap_bounce | Confidence: 80.00% | entry_price: 9.995 | target_price: 10.004999999999999 | expected_profit: 1.0 | risk_reward: 2.158084105978741
2025-06-11 09:01:42 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:01:52 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 51 | PnL: $-4.48 | Win Rate: 35.5% | Avg Profit: $-0.09
2025-06-11 09:01:52 | INFO     | MassiveScan | info:70 |   Trades executed: 51
2025-06-11 09:01:52 | INFO     | MassiveScan | info:70 |   Active trades: 20
2025-06-11 09:01:52 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-0.09
2025-06-11 09:02:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 09:02:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SWTX | vwap_bounce | Confidence: 80.00% | entry_price: 46.775 | target_price: 46.785 | expected_profit: 1.0 | risk_reward: 41.38161823561366
2025-06-11 09:02:09 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EWZ | rsi_overbought | Confidence: 59.56% | entry_price: 28.065 | target_price: 28.055 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | Trade monitoring stopped
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | Closing all positions: Bot shutdown
2025-06-11 09:02:20 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | Market order placed: JBLU sell 39
2025-06-11 09:02:20 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:02:20 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 4 consecutive losses
2025-06-11 09:02:20 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 09:02:20 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | JBLU | SELL 39 @ $5.0250 | PnL: -$0.21 | Reason: Bot shutdown
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | Trade closed: db64865a-6caa-47a2-87f5-b0a7869c0ebf - JBLU - P&L: $-0.21
2025-06-11 09:02:20 | INFO     | MassiveScan | info:70 | All positions closed
2025-06-11 09:02:30 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:02:31 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:02:32 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:02:32 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 09:02:32 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 09:03:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:03:07 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LYFT | vwap_bounce | Confidence: 80.00% | entry_price: 15.6833 | target_price: 15.6733 | expected_profit: 1.0 | risk_reward: 7.942766525849186
2025-06-11 09:03:10 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 09:03:21 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | HBAN | vwap_bounce | Confidence: 80.00% | entry_price: 16.295 | target_price: 16.285 | expected_profit: 1.0 | risk_reward: 1.5834680620990238
2025-06-11 09:03:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 09:03:33 | INFO     | MassiveScan | info:70 | Buying Power: $292,550.04
2025-06-11 09:03:33 | INFO     | MassiveScan | info:70 | Risk metrics loaded - Daily PnL: $0.73, Trades: 7, Open: 0
2025-06-11 09:03:33 | INFO     | MassiveScan | info:70 | Loaded 0 open trades
2025-06-11 09:03:33 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 09:03:34 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 09:03:34 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 09:03:34 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 09:03:35 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 09:03:35 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 09:03:36 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 09:03:36 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 09:03:36 | INFO     | MassiveScan | info:70 | Position size calculated: 12 shares for LYFT (risk per share: $0.0013)
2025-06-11 09:03:37 | INFO     | MassiveScan | info:70 | Position size calculated: 12 shares for LYFT (risk per share: $0.0013)
2025-06-11 09:03:37 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:03:37 | INFO     | MassiveScan | info:70 | Market order placed: LYFT buy 12
2025-06-11 09:03:37 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:03:37 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | LYFT | BUY 12 @ $15.6833 | Strategy: vwap_bounce
2025-06-11 09:03:37 | INFO     | MassiveScan | info:70 | Trade executed: cd78f24a-f3fe-4009-9218-fd23c4b596f5 - LYFT buy 12
2025-06-11 09:03:37 | INFO     | MassiveScan | info:70 | Trade executed: LYFT - Total trades today: 52
2025-06-11 09:03:38 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:03:38 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:03:39 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 09:03:39 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 09:03:39 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for EWZ (risk per share: $0.0050)
2025-06-11 09:03:40 | INFO     | MassiveScan | info:70 | Position size calculated: 7 shares for EWZ (risk per share: $0.0050)
2025-06-11 09:03:40 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:03:40 | INFO     | MassiveScan | info:70 | Market order placed: EWZ sell 7
2025-06-11 09:03:40 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | EWZ | SELL 7 @ $28.0650 | Strategy: rsi_overbought
2025-06-11 09:03:40 | INFO     | MassiveScan | info:70 | Trade executed: 6d55cc06-2aaf-4b5a-9d43-ba54ea735759 - EWZ sell 7
2025-06-11 09:03:40 | INFO     | MassiveScan | info:70 | Trade executed: EWZ - Total trades today: 53
2025-06-11 09:03:41 | INFO     | MassiveScan | info:70 | Broker connected - Buying Power: $292,126.11
2025-06-11 09:03:41 | INFO     | MassiveScan | info:70 | TRADING STRATEGY - Target: $50 Daily Profit
2025-06-11 09:03:41 | INFO     | MassiveScan | info:70 |   Target profit per trade: $1.0
2025-06-11 09:03:41 | INFO     | MassiveScan | info:70 |   Stop loss per trade: $0.5
2025-06-11 09:03:41 | INFO     | MassiveScan | info:70 |   Max daily trades: 100
2025-06-11 09:03:41 | INFO     | MassiveScan | info:70 |   Max daily loss limit: $25.0
2025-06-11 09:03:41 | INFO     | MassiveScan | info:70 |   Max concurrent positions: 10
2025-06-11 09:03:41 | INFO     | MassiveScan | info:70 |   Expected trades for $50: 50 trades
2025-06-11 09:04:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EQX | vwap_bounce | Confidence: 80.00% | entry_price: 6.935 | target_price: 6.925 | expected_profit: 1.0 | risk_reward: 2.821358550641456
2025-06-11 09:04:20 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SAN | vwap_bounce | Confidence: 80.00% | entry_price: 7.985 | target_price: 7.9750000000000005 | expected_profit: 1.0 | risk_reward: 3.1487388958001135
2025-06-11 09:04:24 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | YMM | vwap_bounce | Confidence: 80.00% | entry_price: 12.305 | target_price: 12.315 | expected_profit: 1.0 | risk_reward: 1.5001103268027896
2025-06-11 09:04:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 09:04:26 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | B | vwap_bounce | Confidence: 80.00% | entry_price: 20.16 | target_price: 20.15 | expected_profit: 1.0 | risk_reward: 12.25748874262421
2025-06-11 09:04:27 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | volume_surge | Confidence: 79.10% | entry_price: 19.06 | target_price: 19.07 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 09:04:44 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:04:45 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:04:45 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:04:46 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 09:04:46 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 09:05:06 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTON | vwap_bounce | Confidence: 80.00% | entry_price: 7.655 | target_price: 7.6450000000000005 | expected_profit: 1.0 | risk_reward: 2.7649056413273825
2025-06-11 09:05:10 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PCG | rsi_oversold | Confidence: 51.10% | entry_price: 14.0999 | target_price: 14.1099 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 09:05:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | TDOC | vwap_bounce | Confidence: 80.00% | entry_price: 7.435 | target_price: 7.425 | expected_profit: 1.0 | risk_reward: 1.6087829928047988
2025-06-11 09:05:18 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SAN | vwap_bounce | Confidence: 80.00% | entry_price: 7.98 | target_price: 7.970000000000001 | expected_profit: 1.0 | risk_reward: 5.4724696913226385
2025-06-11 09:05:23 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AEG | vwap_bounce | Confidence: 80.00% | entry_price: 7.26 | target_price: 7.25 | expected_profit: 1.0 | risk_reward: 1.7157597525917567
2025-06-11 09:05:23 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 09:05:49 | INFO     | MassiveScan | info:70 | Position size calculated: 25 shares for SAN (risk per share: $0.0032)
2025-06-11 09:05:49 | INFO     | MassiveScan | info:70 | Position size calculated: 25 shares for SAN (risk per share: $0.0032)
2025-06-11 09:05:50 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:05:50 | INFO     | MassiveScan | info:70 | Market order placed: SAN buy 25
2025-06-11 09:05:50 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:05:50 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SAN | BUY 25 @ $7.9850 | Strategy: vwap_bounce
2025-06-11 09:05:50 | INFO     | MassiveScan | info:70 | Trade executed: 7fb81ff3-6842-4bb5-aa40-55ec0e753d07 - SAN buy 25
2025-06-11 09:05:50 | INFO     | MassiveScan | info:70 | Trade executed: SAN - Total trades today: 54
2025-06-11 09:05:50 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for YMM (risk per share: $0.0067)
2025-06-11 09:05:51 | INFO     | MassiveScan | info:70 | Position size calculated: 16 shares for YMM (risk per share: $0.0067)
2025-06-11 09:05:51 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:05:51 | INFO     | MassiveScan | info:70 | Market order placed: YMM buy 16
2025-06-11 09:05:51 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:05:51 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | YMM | BUY 16 @ $12.3050 | Strategy: vwap_bounce
2025-06-11 09:05:51 | INFO     | MassiveScan | info:70 | Trade executed: 12923e52-4190-420e-aa07-ab89ee6a1724 - YMM buy 16
2025-06-11 09:05:51 | INFO     | MassiveScan | info:70 | Trade executed: YMM - Total trades today: 55
2025-06-11 09:05:52 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 09:05:52 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 09:05:53 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 09:05:53 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 09:05:53 | INFO     | MassiveScan | info:70 | Position size calculated: 26 shares for TDOC (risk per share: $0.0062)
2025-06-11 09:05:53 | INFO     | MassiveScan | info:70 | Position size calculated: 26 shares for PTON (risk per share: $0.0036)
2025-06-11 09:05:54 | INFO     | MassiveScan | info:70 | Position size calculated: 26 shares for TDOC (risk per share: $0.0062)
2025-06-11 09:05:54 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:05:54 | INFO     | MassiveScan | info:70 | Market order placed: TDOC buy 26
2025-06-11 09:05:54 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:05:54 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | TDOC | BUY 26 @ $7.4350 | Strategy: vwap_bounce
2025-06-11 09:05:54 | INFO     | MassiveScan | info:70 | Trade executed: 0505d3c6-02d1-40e3-8fb0-5c99342049b4 - TDOC buy 26
2025-06-11 09:05:54 | INFO     | MassiveScan | info:70 | Position size calculated: 26 shares for PTON (risk per share: $0.0036)
2025-06-11 09:05:54 | INFO     | MassiveScan | info:70 | Trade executed: TDOC - Total trades today: 56
2025-06-11 09:05:55 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 09:05:55 | INFO     | MassiveScan | info:70 | Position size calculated: 26 shares for PTON (risk per share: $0.0036)
2025-06-11 09:05:55 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:05:55 | INFO     | MassiveScan | info:70 | Market order placed: PTON buy 26
2025-06-11 09:05:55 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:05:55 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | PTON | BUY 26 @ $7.6550 | Strategy: vwap_bounce
2025-06-11 09:05:55 | INFO     | MassiveScan | info:70 | Trade executed: 9f545ba4-9f81-4c20-a450-956a634936c7 - PTON buy 26
2025-06-11 09:05:55 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 09:05:56 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:05:56 | INFO     | MassiveScan | info:70 | Market order placed: OUST buy 10
2025-06-11 09:05:56 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:05:56 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | OUST | BUY 10 @ $19.0600 | Strategy: volume_surge
2025-06-11 09:05:56 | INFO     | MassiveScan | info:70 | Trade executed: 29c57d52-80cd-4dc8-be22-1809f3d012d2 - OUST buy 10
2025-06-11 09:05:56 | INFO     | MassiveScan | info:70 | Trade executed: OUST - Total trades today: 57
2025-06-11 09:05:56 | INFO     | MassiveScan | info:70 | Position size calculated: 14 shares for PCG (risk per share: $0.0050)
2025-06-11 09:05:57 | INFO     | MassiveScan | info:70 | Position size calculated: 14 shares for PCG (risk per share: $0.0050)
2025-06-11 09:05:57 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:05:57 | INFO     | MassiveScan | info:70 | Market order placed: PCG buy 14
2025-06-11 09:05:57 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | PCG | BUY 14 @ $14.0999 | Strategy: rsi_oversold
2025-06-11 09:05:57 | INFO     | MassiveScan | info:70 | Trade executed: 724085db-fd49-499e-ae2a-1abae8892d44 - PCG buy 14
2025-06-11 09:05:57 | INFO     | MassiveScan | info:70 | Trade executed: PCG - Total trades today: 58
2025-06-11 09:06:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:06:04 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:06:04 | INFO     | MassiveScan | info:70 | Market order placed: KGC sell 13
2025-06-11 09:06:04 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:06:04 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 09:06:04 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | KGC | SELL 13 @ $15.0200 | PnL: +$1.17 | Reason: Profit target reached
2025-06-11 09:06:04 | INFO     | MassiveScan | info:70 | Trade closed: 943bdd48-52aa-40fb-aac4-6bed2cc1f3a3 - KGC - P&L: $1.17
2025-06-11 09:06:04 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:06:14 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:06:14 | INFO     | MassiveScan | info:70 | Market order placed: JBLU sell 39
2025-06-11 09:06:14 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:06:14 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 09:06:14 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | JBLU | SELL 39 @ $5.0196 | PnL: -$0.60 | Reason: Stop loss triggered
2025-06-11 09:06:14 | INFO     | MassiveScan | info:70 | Trade closed: 114ac2c3-2332-4f9b-8f17-344c2a4d3730 - JBLU - P&L: $-0.60
2025-06-11 09:06:14 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:06:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | EQX | vwap_bounce | Confidence: 80.00% | entry_price: 6.925 | target_price: 6.915 | expected_profit: 1.0 | risk_reward: 1.549024825361349
2025-06-11 09:06:30 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AUR | vwap_bounce | Confidence: 80.00% | entry_price: 5.925 | target_price: 5.935 | expected_profit: 1.0 | risk_reward: 4.9766443093477495
2025-06-11 09:06:30 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SAN | vwap_bounce | Confidence: 80.00% | entry_price: 7.985 | target_price: 7.9750000000000005 | expected_profit: 1.0 | risk_reward: 3.156700120481945
2025-06-11 09:06:33 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 09:06:50 | INFO     | MassiveScan | info:70 | Position size calculated: 25 shares for SAN (risk per share: $0.0018)
2025-06-11 09:06:51 | INFO     | MassiveScan | info:70 | Position size calculated: 25 shares for SAN (risk per share: $0.0018)
2025-06-11 09:06:51 | INFO     | MassiveScan | info:70 | Position size calculated: 25 shares for SAN (risk per share: $0.0018)
2025-06-11 09:06:52 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:06:52 | INFO     | MassiveScan | info:70 | Market order placed: SAN buy 25
2025-06-11 09:06:52 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:06:52 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SAN | BUY 25 @ $7.9800 | Strategy: vwap_bounce
2025-06-11 09:06:52 | INFO     | MassiveScan | info:70 | Trade executed: 9f93dd0e-bed0-4a6c-b8e6-f68f0c6c0d77 - SAN buy 25
2025-06-11 09:07:09 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | RCAT | vwap_bounce | Confidence: 80.00% | entry_price: 8.695 | target_price: 8.705 | expected_profit: 1.0 | risk_reward: 1.7862196679141098
2025-06-11 09:07:56 | INFO     | MassiveScan | info:70 | Position size calculated: 25 shares for SAN (risk per share: $0.0032)
2025-06-11 09:07:56 | INFO     | MassiveScan | info:70 | Position size calculated: 25 shares for SAN (risk per share: $0.0032)
2025-06-11 09:07:57 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:07:57 | INFO     | MassiveScan | info:70 | Market order placed: SAN buy 25
2025-06-11 09:07:57 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:07:57 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SAN | BUY 25 @ $7.9850 | Strategy: vwap_bounce
2025-06-11 09:07:57 | INFO     | MassiveScan | info:70 | Trade executed: b70c2975-e7cf-43b7-bb41-50c0dbc9796c - SAN buy 25
2025-06-11 09:07:57 | INFO     | MassiveScan | info:70 | Trade executed: SAN - Total trades today: 59
2025-06-11 09:07:57 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 09:07:58 | INFO     | MassiveScan | info:70 | Position size calculated: 6 shares for PDCO (risk per share: $0.0001)
2025-06-11 09:07:58 | INFO     | MassiveScan | info:70 | Position size calculated: 33 shares for AUR (risk per share: $0.0020)
2025-06-11 09:07:58 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PDCO is not active"}
2025-06-11 09:07:58 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PDCO
2025-06-11 09:07:59 | INFO     | MassiveScan | info:70 | Position size calculated: 33 shares for AUR (risk per share: $0.0020)
2025-06-11 09:07:59 | INFO     | MassiveScan | info:70 | Position size calculated: 33 shares for AUR (risk per share: $0.0020)
2025-06-11 09:08:00 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:08:00 | INFO     | MassiveScan | info:70 | Market order placed: AUR buy 33
2025-06-11 09:08:00 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:08:00 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AUR | BUY 33 @ $5.9250 | Strategy: vwap_bounce
2025-06-11 09:08:00 | INFO     | MassiveScan | info:70 | Trade executed: 963584b4-7d46-430c-b67f-abc4c7217c59 - AUR buy 33
2025-06-11 09:08:04 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 59 | PnL: $-3.91 | Win Rate: 36.4% | Avg Profit: $-0.07
2025-06-11 09:08:04 | INFO     | MassiveScan | info:70 |   Trades executed: 59
2025-06-11 09:08:04 | INFO     | MassiveScan | info:70 |   Active trades: 26
2025-06-11 09:08:04 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-0.07
2025-06-11 09:08:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:08:11 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:08:11 | INFO     | MassiveScan | info:70 | Market order placed: OUST sell 10
2025-06-11 09:08:11 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:08:11 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 09:08:11 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | OUST | SELL 10 @ $19.2532 | PnL: +$1.93 | Reason: Profit target reached
2025-06-11 09:08:11 | INFO     | MassiveScan | info:70 | Trade closed: 29c57d52-80cd-4dc8-be22-1809f3d012d2 - OUST - P&L: $1.93
2025-06-11 09:08:11 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:08:14 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CX | rsi_overbought | Confidence: 60.35% | entry_price: 7.17 | target_price: 7.16 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 09:08:14 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AEG | vwap_bounce | Confidence: 80.00% | entry_price: 7.26 | target_price: 7.25 | expected_profit: 1.0 | risk_reward: 1.7297616900796118
2025-06-11 09:08:23 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:08:23 | INFO     | MassiveScan | info:70 | Market order placed: PCG sell 14
2025-06-11 09:08:23 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 09:08:23 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | PCG | SELL 14 @ $14.1892 | PnL: +$1.25 | Reason: Profit target reached
2025-06-11 09:08:23 | INFO     | MassiveScan | info:70 | Trade closed: 724085db-fd49-499e-ae2a-1abae8892d44 - PCG - P&L: $1.25
2025-06-11 09:08:23 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:08:29 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CX | rsi_overbought | Confidence: 60.35% | entry_price: 7.175 | target_price: 7.165 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 09:08:58 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 09:08:59 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 09:09:00 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 09:09:00 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 09:09:00 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 09:09:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CX | rsi_overbought | Confidence: 60.35% | entry_price: 7.175 | target_price: 7.165 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 09:09:11 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 09:09:14 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:09:17 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PDCO | vwap_bounce | Confidence: 80.00% | entry_price: 31.33 | target_price: 31.*************** | expected_profit: 1.0 | risk_reward: 177.**************
2025-06-11 09:10:06 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 09:10:06 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 09:10:07 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 09:10:07 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 09:10:07 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CX (risk per share: $0.0050)
2025-06-11 09:10:08 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CX (risk per share: $0.0050)
2025-06-11 09:10:08 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:10:08 | INFO     | MassiveScan | info:70 | Market order placed: CX sell 27
2025-06-11 09:10:08 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CX | SELL 27 @ $7.1750 | Strategy: rsi_overbought
2025-06-11 09:10:08 | INFO     | MassiveScan | info:70 | Trade executed: 219462c7-0292-44b2-90c3-c848a603ae42 - CX sell 27
2025-06-11 09:10:08 | INFO     | MassiveScan | info:70 | Trade executed: CX - Total trades today: 60
2025-06-11 09:10:12 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:10:13 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:10:14 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:10:15 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:10:15 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:10:15 | INFO     | MassiveScan | info:70 | Market order placed: TDOC sell 26
2025-06-11 09:10:15 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:10:15 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 09:10:15 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | TDOC | SELL 26 @ $7.4800 | PnL: +$1.17 | Reason: Profit target reached
2025-06-11 09:10:15 | INFO     | MassiveScan | info:70 | Trade closed: 0505d3c6-02d1-40e3-8fb0-5c99342049b4 - TDOC - P&L: $1.17
2025-06-11 09:10:15 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:10:15 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 09:10:15 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 09:10:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:10:29 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CX | rsi_overbought | Confidence: 54.51% | entry_price: 7.175 | target_price: 7.165 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 09:10:32 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AEG | vwap_bounce | Confidence: 80.00% | entry_price: 7.26 | target_price: 7.25 | expected_profit: 1.0 | risk_reward: 1.7709396316588548
2025-06-11 09:10:40 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMCR | vwap_bounce | Confidence: 80.00% | entry_price: 9.225 | target_price: 9.235 | expected_profit: 1.0 | risk_reward: 23.382236274535128
2025-06-11 09:10:43 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CX | rsi_overbought | Confidence: 54.51% | entry_price: 7.18 | target_price: 7.17 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 09:11:03 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AAAU | vwap_bounce | Confidence: 80.00% | entry_price: 32.895 | target_price: 32.905 | expected_profit: 1.0 | risk_reward: 1.7839966626909947
2025-06-11 09:11:07 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AMCR | vwap_bounce | Confidence: 80.00% | entry_price: 9.22 | target_price: 9.23 | expected_profit: 1.0 | risk_reward: 2.1935763024313455
2025-06-11 09:11:09 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:11:12 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | GLDM | vwap_bounce | Confidence: 80.00% | entry_price: 65.95 | target_price: 65.94 | expected_profit: 1.0 | risk_reward: 5.599421333401586
2025-06-11 09:11:15 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | IOT | vwap_bounce | Confidence: 80.00% | entry_price: 41.99 | target_price: 42.0 | expected_profit: 1.0 | risk_reward: 3.631324559700852
2025-06-11 09:11:18 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 09:11:20 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 09:11:21 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 09:11:21 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 09:11:22 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 09:11:22 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 09:12:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 09:12:12 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 09:12:17 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0046)
2025-06-11 09:12:18 | INFO     | MassiveScan | info:70 | Position size calculated: 21 shares for AMCR (risk per share: $0.0046)
2025-06-11 09:12:18 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:12:18 | INFO     | MassiveScan | info:70 | Market order placed: AMCR buy 21
2025-06-11 09:12:18 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:12:18 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | AMCR | BUY 21 @ $9.2200 | Strategy: vwap_bounce
2025-06-11 09:12:18 | INFO     | MassiveScan | info:70 | Trade executed: 2549e051-be32-4d7e-9873-39261aaf522d - AMCR buy 21
2025-06-11 09:12:18 | INFO     | MassiveScan | info:70 | Trade executed: AMCR - Total trades today: 61
2025-06-11 09:12:19 | INFO     | MassiveScan | info:70 | Position size calculated: 3 shares for GLDM (risk per share: $0.0018)
2025-06-11 09:12:19 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 09:12:19 | INFO     | MassiveScan | info:70 | Position size calculated: 3 shares for GLDM (risk per share: $0.0018)
2025-06-11 09:12:20 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:12:20 | INFO     | MassiveScan | info:70 | Market order placed: GLDM buy 3
2025-06-11 09:12:20 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:12:20 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | GLDM | BUY 3 @ $65.9500 | Strategy: vwap_bounce
2025-06-11 09:12:20 | INFO     | MassiveScan | info:70 | Trade executed: 633769aa-157d-44bf-9218-54fb44ce658d - GLDM buy 3
2025-06-11 09:12:20 | INFO     | MassiveScan | info:70 | Trade executed: GLDM - Total trades today: 62
2025-06-11 09:12:20 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 09:12:20 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 09:12:21 | INFO     | MassiveScan | info:70 | Position size calculated: 28 shares for ACCD (risk per share: $0.0048)
2025-06-11 09:12:21 | INFO     | MassiveScan | info:70 | Position size calculated: 8 shares for JWN (risk per share: $0.0054)
2025-06-11 09:12:21 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset ACCD is not active"}
2025-06-11 09:12:21 | ERROR    | MassiveScan | error:82 | Failed to place entry order for ACCD
2025-06-11 09:12:21 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset JWN is not active"}
2025-06-11 09:12:21 | ERROR    | MassiveScan | error:82 | Failed to place entry order for JWN
2025-06-11 09:12:22 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:12:22 | INFO     | MassiveScan | info:70 | Position size calculated: 13 shares for LGTY (risk per share: $0.0034)
2025-06-11 09:12:23 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset LGTY is not active"}
2025-06-11 09:12:23 | ERROR    | MassiveScan | error:82 | Failed to place entry order for LGTY
2025-06-11 09:12:23 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CX (risk per share: $0.0050)
2025-06-11 09:12:24 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CX (risk per share: $0.0050)
2025-06-11 09:12:24 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:12:24 | INFO     | MassiveScan | info:70 | Market order placed: CX sell 27
2025-06-11 09:12:24 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CX | SELL 27 @ $7.1800 | Strategy: rsi_overbought
2025-06-11 09:12:24 | INFO     | MassiveScan | info:70 | Trade executed: d373730b-c8dc-44e3-933f-4200d06a47f1 - CX sell 27
2025-06-11 09:12:24 | INFO     | MassiveScan | info:70 | Trade executed: CX - Total trades today: 63
2025-06-11 09:12:31 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | LGTY | vwap_bounce | Confidence: 80.00% | entry_price: 14.29 | target_price: 14.28 | expected_profit: 1.0 | risk_reward: 2.**************
2025-06-11 09:12:31 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:12:31 | INFO     | MassiveScan | info:70 | Market order placed: CX buy 27
2025-06-11 09:12:31 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 09:12:31 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CX | BUY 27 @ $7.2050 | PnL: -$0.81 | Reason: Stop loss triggered
2025-06-11 09:12:31 | INFO     | MassiveScan | info:70 | Trade closed: 219462c7-0292-44b2-90c3-c848a603ae42 - CX - P&L: $-0.81
2025-06-11 09:12:31 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:12:42 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:12:42 | INFO     | MassiveScan | info:70 | Market order placed: CNH sell 15
2025-06-11 09:12:42 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:12:42 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 09:12:42 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CNH | SELL 15 @ $12.8450 | PnL: -$0.60 | Reason: Stop loss triggered
2025-06-11 09:12:42 | INFO     | MassiveScan | info:70 | Trade closed: 875fd64a-9eb6-481b-a31a-35a448ce4920 - CNH - P&L: $-0.60
2025-06-11 09:12:42 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:13:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | JWN | vwap_bounce | Confidence: 80.00% | entry_price: 24.66 | target_price: 24.65 | expected_profit: 1.0 | risk_reward: 1.****************
2025-06-11 09:13:05 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | OUST | momentum_breakout | Confidence: 95.00% | entry_price: 19.6 | target_price: 19.610000000000003 | expected_profit: 1.0 | risk_reward: 2.****************
2025-06-11 09:13:09 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:13:09 | INFO     | MassiveScan | info:70 | Market order placed: CX buy 27
2025-06-11 09:13:09 | WARNING  | MassiveScan | risk_alert:131 | RISK ALERT [WARNING] | Consecutive Losses | 3 consecutive losses
2025-06-11 09:13:09 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-3.22
2025-06-11 09:13:09 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | CX | BUY 27 @ $7.2000 | PnL: -$0.54 | Reason: Stop loss triggered
2025-06-11 09:13:09 | INFO     | MassiveScan | info:70 | Trade closed: d373730b-c8dc-44e3-933f-4200d06a47f1 - CX - P&L: $-0.54
2025-06-11 09:13:09 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:13:09 | INFO     | MassiveScan | performance_summary:123 | DAILY SUMMARY | Trades: 63 | PnL: $-1.51 | Win Rate: 38.5% | Avg Profit: $-0.02
2025-06-11 09:13:09 | INFO     | MassiveScan | info:70 |   Trades executed: 63
2025-06-11 09:13:09 | INFO     | MassiveScan | info:70 |   Active trades: 24
2025-06-11 09:13:09 | WARNING  | MassiveScan | warning:78 | Low average profit per trade: $-0.02
2025-06-11 09:13:12 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | ACCD | vwap_bounce | Confidence: 80.00% | entry_price: 7.02 | target_price: 7.01 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:13:21 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AVDX | vwap_bounce | Confidence: 80.00% | entry_price: 9.785 | target_price: 9.795 | expected_profit: 1.0 | risk_reward: 5.442746491953502
2025-06-11 09:13:24 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SOUN | vwap_bounce | Confidence: 80.00% | entry_price: 9.99 | target_price: 10.0 | expected_profit: 1.0 | risk_reward: 13.033028736419343
2025-06-11 09:13:25 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CX | rsi_overbought | Confidence: 53.33% | entry_price: 7.2 | target_price: 7.19 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 09:13:28 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | SOUN | vwap_bounce | Confidence: 80.00% | entry_price: 9.995 | target_price: 10.004999999999999 | expected_profit: 1.0 | risk_reward: 2.3625477731968303
2025-06-11 09:13:29 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | CX | rsi_overbought | Confidence: 53.33% | entry_price: 7.2 | target_price: 7.19 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 09:13:29 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:13:30 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AEG | vwap_bounce | Confidence: 80.00% | entry_price: 7.2601 | target_price: 7.250100000000001 | expected_profit: 1.0 | risk_reward: 1.7412969423731277
2025-06-11 09:14:02 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | PTVE | vwap_bounce | Confidence: 80.00% | entry_price: 18.01 | target_price: 18.0 | expected_profit: 1.0 | risk_reward: 2.***************
2025-06-11 09:14:08 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 09:14:09 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 09:14:09 | INFO     | MassiveScan | info:70 | Position size calculated: 10 shares for OUST (risk per share: $0.0050)
2025-06-11 09:14:10 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:14:10 | INFO     | MassiveScan | info:70 | Market order placed: OUST buy 10
2025-06-11 09:14:10 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:14:10 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | OUST | BUY 10 @ $19.6000 | Strategy: momentum_breakout
2025-06-11 09:14:10 | INFO     | MassiveScan | info:70 | Trade executed: 6db1dc7b-e8f5-4a18-9853-bb9065ef403f - OUST buy 10
2025-06-11 09:14:16 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | NXE | rsi_overbought | Confidence: 52.46% | entry_price: 6.64 | target_price: 6.63 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 09:14:19 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | AAAU | vwap_bounce | Confidence: 80.00% | entry_price: 32.905 | target_price: 32.895 | expected_profit: 1.0 | risk_reward: 1.7952217117655855
2025-06-11 09:14:21 | INFO     | MassiveScan | scanner_result:116 | SCANNER HIT | NXE | rsi_overbought | Confidence: 52.46% | entry_price: 6.64 | target_price: 6.63 | expected_profit: 1.0 | risk_reward: 2.0
2025-06-11 09:14:59 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for SOUN (risk per share: $0.0042)
2025-06-11 09:14:59 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for SOUN (risk per share: $0.0042)
2025-06-11 09:15:00 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:15:00 | INFO     | MassiveScan | info:70 | Market order placed: SOUN buy 20
2025-06-11 09:15:00 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:15:00 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SOUN | BUY 20 @ $9.9950 | Strategy: vwap_bounce
2025-06-11 09:15:00 | INFO     | MassiveScan | info:70 | Trade executed: f29e116d-cc41-4ed6-b139-ec32c7e1acdd - SOUN buy 20
2025-06-11 09:15:00 | INFO     | MassiveScan | info:70 | Trade executed: SOUN - Total trades today: 64
2025-06-11 09:15:00 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 09:15:01 | INFO     | MassiveScan | info:70 | Position size calculated: 11 shares for PTVE (risk per share: $0.0044)
2025-06-11 09:15:01 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for SOUN (risk per share: $0.0008)
2025-06-11 09:15:01 | ERROR    | MassiveScan | error:82 | Alpaca API error placing market order: {"code":********,"message":"asset PTVE is not active"}
2025-06-11 09:15:01 | ERROR    | MassiveScan | error:82 | Failed to place entry order for PTVE
2025-06-11 09:15:02 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CX (risk per share: $0.0050)
2025-06-11 09:15:02 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for SOUN (risk per share: $0.0008)
2025-06-11 09:15:02 | INFO     | MassiveScan | info:70 | Position size calculated: 27 shares for CX (risk per share: $0.0050)
2025-06-11 09:15:02 | INFO     | MassiveScan | info:70 | Position size calculated: 20 shares for SOUN (risk per share: $0.0008)
2025-06-11 09:15:02 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:15:02 | INFO     | MassiveScan | info:70 | Market order placed: OUST sell 10
2025-06-11 09:15:02 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:15:02 | CRITICAL | MassiveScan | risk_alert:129 | RISK ALERT [CRITICAL] | Large Loss | Largest loss: $-1.65
2025-06-11 09:15:02 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | OUST | SELL 10 @ $19.7999 | PnL: +$2.00 | Reason: Profit target reached
2025-06-11 09:15:02 | INFO     | MassiveScan | info:70 | Trade closed: 6db1dc7b-e8f5-4a18-9853-bb9065ef403f - OUST - P&L: $2.00
2025-06-11 09:15:02 | ERROR    | MassiveScan | error:82 | Error monitoring trades: dictionary changed size during iteration
2025-06-11 09:15:02 | INFO     | MassiveScan | info:70 | Trade monitoring stopped
2025-06-11 09:15:02 | INFO     | MassiveScan | info:70 | Closing all positions: Bot shutdown
2025-06-11 09:15:03 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:15:03 | INFO     | MassiveScan | info:70 | Market order placed: CX sell 27
2025-06-11 09:15:03 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | CX | SELL 27 @ $7.2000 | Strategy: rsi_overbought
2025-06-11 09:15:03 | INFO     | MassiveScan | info:70 | Trade executed: 396a30af-e7cc-4389-8f35-5c8e7f5f1a6a - CX sell 27
2025-06-11 09:15:03 | INFO     | MassiveScan | info:70 | Trade executed: CX - Total trades today: 65
2025-06-11 09:15:03 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:15:03 | INFO     | MassiveScan | info:70 | Market order placed: PTON sell 26
2025-06-11 09:15:03 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:15:03 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | PTON | SELL 26 @ $7.7150 | PnL: +$1.56 | Reason: Bot shutdown
2025-06-11 09:15:03 | INFO     | MassiveScan | info:70 | Trade closed: 9f545ba4-9f81-4c20-a450-956a634936c7 - PTON - P&L: $1.56
2025-06-11 09:15:03 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:15:03 | INFO     | MassiveScan | info:70 | Market order placed: SOUN buy 20
2025-06-11 09:15:03 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:15:03 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | SOUN | BUY 20 @ $9.9900 | Strategy: vwap_bounce
2025-06-11 09:15:03 | INFO     | MassiveScan | info:70 | Trade executed: 8267b0bf-f951-4f0e-abf3-066ba87d9ac2 - SOUN buy 20
2025-06-11 09:15:03 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:15:03 | INFO     | MassiveScan | info:70 | Market order placed: AUR sell 33
2025-06-11 09:15:03 | ERROR    | MassiveScan | error:82 | Failed to save trade: Object of type bool is not JSON serializable
2025-06-11 09:15:03 | INFO     | MassiveScan | trade_exit:101 | TRADE EXIT | AUR | SELL 33 @ $5.9500 | PnL: +$0.83 | Reason: Bot shutdown
2025-06-11 09:15:03 | INFO     | MassiveScan | info:70 | Trade closed: 963584b4-7d46-430c-b67f-abc4c7217c59 - AUR - P&L: $0.83
2025-06-11 09:15:03 | INFO     | MassiveScan | info:70 | All positions closed
2025-06-11 09:15:03 | INFO     | MassiveScan | info:70 | Position size calculated: 30 shares for NXE (risk per share: $0.0050)
2025-06-11 09:15:04 | INFO     | MassiveScan | info:70 | Position size calculated: 30 shares for NXE (risk per share: $0.0050)
2025-06-11 09:15:04 | ERROR    | MassiveScan | error:82 | Failed to save order: Error binding parameter 13: type 'UUID' is not supported
2025-06-11 09:15:04 | INFO     | MassiveScan | info:70 | Market order placed: NXE sell 30
2025-06-11 09:15:04 | INFO     | MassiveScan | trade_entry:93 | TRADE ENTRY | NXE | SELL 30 @ $6.6400 | Strategy: rsi_overbought
2025-06-11 09:15:04 | INFO     | MassiveScan | info:70 | Trade executed: 98a43262-3357-4cf8-9f91-6e64657fad9f - NXE sell 30
2025-06-11 09:15:04 | INFO     | MassiveScan | info:70 | Trade executed: NXE - Total trades today: 66
