"""
Main application for the MassiveScan trading bot.
Coordinates all components and provides the main execution loop.
"""

import asyncio
import signal
import sys
from datetime import datetime, time
from typing import List
import threading

from config import config, setup_environment
from scanner import scanner
from trade_manager import trade_manager
from risk_manager import risk_manager
from broker import broker
from data_provider import data_provider
from database import db
from logger import log_info, log_error, log_warning, log_performance
from models import TradingSignal

class MassiveScanBot:
    """Main trading bot application"""
    
    def __init__(self):
        self.running = False
        self.scan_task = None
        self.monitor_task = None
        self.signals_queue = asyncio.Queue()
        
        # Statistics
        self.start_time = None
        self.total_signals_processed = 0
        self.total_trades_executed = 0
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        log_info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
    
    async def initialize(self) -> bool:
        """Initialize the trading bot"""
        try:
            log_info("Initializing MassiveScan Trading Bot...")
            
            # Check environment setup
            if not setup_environment():
                log_error("Environment setup failed. Please configure your API keys.")
                return False
            
            # Verify API connections
            log_info("Verifying API connections...")
            
            # Test data provider
            if not data_provider.is_market_open():
                log_warning("Market appears to be closed")
            else:
                log_info("Market is open")
            
            # Test broker connection
            account_info = broker.get_account_info()
            if not account_info:
                log_error("Failed to connect to broker")
                return False
            
            log_info(f"Broker connected - Buying Power: ${account_info.get('buying_power', 0):,.2f}")
            
            # Initialize risk manager
            risk_manager.reset_daily_stats()
            
            # Load configuration
            log_info(f"Configuration loaded:")
            log_info(f"  Target profit: ${config.trading.target_profit_dollars}")
            log_info(f"  Stop loss: ${config.trading.stop_loss_dollars}")
            log_info(f"  Max daily trades: {config.trading.max_daily_trades}")
            log_info(f"  Max concurrent positions: {config.trading.max_concurrent_positions}")
            log_info(f"  Scan interval: {config.system.scan_interval_seconds}s")
            
            self.start_time = datetime.now()
            log_info("MassiveScan Trading Bot initialized successfully")
            return True
            
        except Exception as e:
            log_error(f"Failed to initialize bot: {e}")
            return False
    
    async def start_scanning(self):
        """Start the scanning process"""
        log_info("Starting market scanning...")
        
        while self.running:
            try:
                # Check if market is open
                if not data_provider.is_market_open():
                    log_info("Market closed, waiting...")
                    await asyncio.sleep(300)  # Wait 5 minutes
                    continue
                
                # Check if we can still trade today
                risk_summary = risk_manager.get_risk_summary()
                if risk_summary['trading_halted']:
                    log_warning(f"Trading halted: {risk_summary['halt_reason']}")
                    await asyncio.sleep(60)  # Wait 1 minute
                    continue
                
                if risk_summary['daily_trades'] >= config.trading.max_daily_trades:
                    log_info("Daily trade limit reached, stopping scanning")
                    break
                
                # Run market scan
                log_info("Running market scan...")
                signals = scanner.run_full_scan()
                
                if signals:
                    log_info(f"Found {len(signals)} trading signals")
                    
                    # Add signals to queue for processing
                    for signal in signals:
                        await self.signals_queue.put(signal)
                        self.total_signals_processed += 1
                
                # Wait before next scan
                await asyncio.sleep(config.system.scan_interval_seconds)
                
            except Exception as e:
                log_error(f"Error in scanning loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    async def process_signals(self):
        """Process trading signals from the queue"""
        log_info("Starting signal processing...")
        
        while self.running:
            try:
                # Get signal from queue (wait up to 10 seconds)
                try:
                    signal = await asyncio.wait_for(self.signals_queue.get(), timeout=10.0)
                except asyncio.TimeoutError:
                    continue
                
                # Check if we can still trade
                risk_summary = risk_manager.get_risk_summary()
                if risk_summary['trading_halted']:
                    log_warning("Trading halted, skipping signal")
                    continue
                
                if risk_summary['daily_trades'] >= config.trading.max_daily_trades:
                    log_info("Daily trade limit reached, skipping signal")
                    continue
                
                # Execute the signal
                log_info(f"Processing signal: {signal.symbol} - {signal.signal_type.value} "
                        f"(confidence: {signal.confidence:.2%})")
                
                trade = trade_manager.execute_signal(signal)
                if trade:
                    self.total_trades_executed += 1
                    log_info(f"Trade executed: {trade.symbol} - Total trades today: {self.total_trades_executed}")
                else:
                    log_warning(f"Failed to execute signal for {signal.symbol}")
                
                # Mark signal as processed
                self.signals_queue.task_done()
                
            except Exception as e:
                log_error(f"Error processing signal: {e}")
                await asyncio.sleep(1)
    
    async def start_monitoring(self):
        """Start trade monitoring"""
        log_info("Starting trade monitoring...")
        await trade_manager.start_monitoring()
    
    async def run(self):
        """Main execution loop"""
        try:
            if not await self.initialize():
                return False
            
            self.running = True
            log_info("Starting MassiveScan Trading Bot...")
            
            # Start all tasks concurrently
            tasks = [
                asyncio.create_task(self.start_scanning(), name="scanner"),
                asyncio.create_task(self.process_signals(), name="signal_processor"),
                asyncio.create_task(self.start_monitoring(), name="trade_monitor"),
                asyncio.create_task(self.status_reporter(), name="status_reporter")
            ]
            
            # Wait for all tasks
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            log_error(f"Error in main execution loop: {e}")
            return False
        finally:
            await self.cleanup()
    
    async def status_reporter(self):
        """Periodically report status and performance"""
        while self.running:
            try:
                await asyncio.sleep(300)  # Report every 5 minutes
                
                # Get current statistics
                trade_summary = trade_manager.get_trade_summary()
                risk_summary = risk_manager.get_risk_summary()
                scan_stats = scanner.get_scan_statistics()
                
                # Calculate runtime
                runtime = datetime.now() - self.start_time if self.start_time else None
                runtime_hours = runtime.total_seconds() / 3600 if runtime else 0
                
                # Log performance summary
                log_performance(
                    trade_summary['trades_today'],
                    risk_summary['daily_pnl'],
                    trade_summary['win_rate'],
                    risk_summary['daily_pnl'] / max(1, trade_summary['trades_today'])
                )
                
                log_info(f"Status Report:")
                log_info(f"  Runtime: {runtime_hours:.1f} hours")
                log_info(f"  Signals processed: {self.total_signals_processed}")
                log_info(f"  Trades executed: {self.total_trades_executed}")
                log_info(f"  Active trades: {trade_summary['active_trades']}")
                log_info(f"  Daily P&L: ${risk_summary['daily_pnl']:.2f}")
                log_info(f"  Win rate: {trade_summary['win_rate']:.1%}")
                log_info(f"  Scans completed: {scan_stats['total_scans']}")
                
                # Check for performance issues
                if trade_summary['trades_today'] > 0:
                    avg_profit = risk_summary['daily_pnl'] / trade_summary['trades_today']
                    if avg_profit < 0.5:
                        log_warning(f"Low average profit per trade: ${avg_profit:.2f}")
                
                if trade_summary['win_rate'] < 0.6 and trade_summary['trades_today'] >= 10:
                    log_warning(f"Low win rate: {trade_summary['win_rate']:.1%}")
                
            except Exception as e:
                log_error(f"Error in status reporting: {e}")
    
    def stop(self):
        """Stop the trading bot"""
        log_info("Stopping MassiveScan Trading Bot...")
        self.running = False
        
        # Stop trade monitoring
        trade_manager.stop_monitoring()
        
        # Close all open positions
        trade_manager.close_all_positions("Bot shutdown")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            log_info("Cleaning up resources...")
            
            # Final status report
            if self.start_time:
                runtime = datetime.now() - self.start_time
                log_info(f"Bot ran for {runtime.total_seconds() / 3600:.1f} hours")
                log_info(f"Total signals processed: {self.total_signals_processed}")
                log_info(f"Total trades executed: {self.total_trades_executed}")
            
            # Get final performance summary
            performance_summary = db.get_performance_summary(1)  # Last 1 day
            if performance_summary:
                log_info(f"Final Performance Summary:")
                log_info(f"  Total trades: {performance_summary['total_trades']}")
                log_info(f"  Win rate: {performance_summary['win_rate']:.1%}")
                log_info(f"  Total P&L: ${performance_summary['total_pnl']:.2f}")
                log_info(f"  Average P&L: ${performance_summary['avg_pnl']:.2f}")
            
            log_info("MassiveScan Trading Bot stopped")
            
        except Exception as e:
            log_error(f"Error during cleanup: {e}")

async def main():
    """Main entry point"""
    bot = MassiveScanBot()
    
    try:
        await bot.run()
    except KeyboardInterrupt:
        log_info("Bot stopped by user")
    except Exception as e:
        log_error(f"Unexpected error: {e}")
    finally:
        bot.stop()

if __name__ == "__main__":
    print("=" * 60)
    print("MassiveScan - Automated Stock Trading Bot")
    print("=" * 60)
    print()
    
    try:
        # Run the bot
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except Exception as e:
        print(f"Fatal error: {e}")
    
    print("\nGoodbye!")
    sys.exit(0)
