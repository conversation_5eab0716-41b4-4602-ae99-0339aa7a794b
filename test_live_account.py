"""
Test live account connection
"""

def test_live_account():
    try:
        from broker import broker
        
        print("🚀 LIVE ACCOUNT CONNECTION TEST")
        print("=" * 50)
        
        account = broker.get_account_info()
        
        if account:
            print("✅ CONNECTED TO LIVE ACCOUNT!")
            print(f"Account Number: {account.get('account_number', 'N/A')}")
            print(f"Buying Power: ${account.get('buying_power', 0):,.2f}")
            print(f"Portfolio Value: ${account.get('portfolio_value', 0):,.2f}")
            print(f"Cash: ${account.get('cash', 0):,.2f}")
            print(f"Pattern Day Trader: {account.get('pattern_day_trader', False)}")
            print(f"Trading Blocked: {account.get('trading_blocked', False)}")
            
            # Check if this is your $30K account
            portfolio_value = account.get('portfolio_value', 0)
            if portfolio_value >= 25000:
                print("✅ This appears to be your live $30K account!")
            else:
                print(f"⚠️ Portfolio value is ${portfolio_value:,.2f} - expected ~$30K")
                
        else:
            print("❌ FAILED TO CONNECT TO ACCOUNT")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    test_live_account()
