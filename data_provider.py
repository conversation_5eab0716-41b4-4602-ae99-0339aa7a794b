"""
Data provider for Financial Modeling Prep API.
Handles market data retrieval, stock screening, and real-time price feeds.
"""

import requests
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor

from config import config
from models import MarketData
from logger import log_info, log_error, log_debug, log_warning

class FMPDataProvider:
    """Financial Modeling Prep API data provider"""
    
    def __init__(self):
        self.api_key = config.api.fmp_api_key
        self.base_url = config.api.fmp_base_url
        self.session = requests.Session()
        self.rate_limit_delay = 60 / config.api.fmp_requests_per_minute  # Seconds between requests
        self.last_request_time = 0
        
        # Cache for market data
        self.price_cache = {}
        self.cache_expiry = 60  # Cache expires after 60 seconds
        
        if not self.api_key:
            raise ValueError("FMP API key is required")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Make API request with rate limiting and error handling"""
        self._rate_limit()
        
        if params is None:
            params = {}
        
        params['apikey'] = self.api_key
        url = f"{self.base_url}/{endpoint}"
        
        try:
            response = self.session.get(url, params=params, timeout=config.system.request_timeout)
            response.raise_for_status()
            
            data = response.json()
            
            # Check for API error messages
            if isinstance(data, dict) and 'Error Message' in data:
                log_error(f"FMP API error: {data['Error Message']}")
                return None
            
            return data
            
        except requests.exceptions.RequestException as e:
            log_error(f"FMP API request failed: {e}")
            return None
        except ValueError as e:
            log_error(f"FMP API JSON decode error: {e}")
            return None
    
    def get_stock_screener(self, 
                          min_market_cap: int = None,
                          min_volume: int = None,
                          min_price: float = None,
                          max_price: float = None,
                          sector: str = None,
                          limit: int = 1000) -> List[Dict[str, Any]]:
        """Get stocks from screener with filters"""
        
        params = {}
        
        if min_market_cap:
            params['marketCapMoreThan'] = min_market_cap
        if min_volume:
            params['volumeMoreThan'] = min_volume
        if min_price:
            params['priceMoreThan'] = min_price
        if max_price:
            params['priceLowerThan'] = max_price
        if sector:
            params['sector'] = sector
        if limit:
            params['limit'] = limit
        
        log_debug(f"Screening stocks with params: {params}")
        data = self._make_request("stock-screener", params)
        
        if data and isinstance(data, list):
            log_info(f"Stock screener returned {len(data)} stocks")
            return data
        
        log_warning("Stock screener returned no data")
        return []
    
    def get_real_time_price(self, symbol: str) -> Optional[float]:
        """Get real-time price for a symbol"""
        # Check cache first
        cache_key = f"{symbol}_price"
        if cache_key in self.price_cache:
            cached_data, timestamp = self.price_cache[cache_key]
            if time.time() - timestamp < self.cache_expiry:
                return cached_data
        
        data = self._make_request(f"quote-short/{symbol}")
        
        if data and isinstance(data, list) and len(data) > 0:
            price = data[0].get('price')
            if price:
                # Cache the price
                self.price_cache[cache_key] = (price, time.time())
                return float(price)
        
        log_warning(f"Could not get real-time price for {symbol}")
        return None
    
    def get_real_time_prices(self, symbols: List[str]) -> Dict[str, float]:
        """Get real-time prices for multiple symbols"""
        if not symbols:
            return {}

        # Split into chunks to avoid URL length limits
        chunk_size = 50
        all_prices = {}

        for i in range(0, len(symbols), chunk_size):
            chunk = symbols[i:i + chunk_size]
            symbol_string = ','.join(chunk)

            data = self._make_request(f"quote-short/{symbol_string}")

            if data and isinstance(data, list):
                for item in data:
                    symbol = item.get('symbol')
                    price = item.get('price')
                    if symbol and price:
                        all_prices[symbol] = float(price)
                        # Cache the price
                        cache_key = f"{symbol}_price"
                        self.price_cache[cache_key] = (float(price), time.time())

        log_debug(f"Retrieved prices for {len(all_prices)} symbols")
        return all_prices

    def get_intraday_data(self, symbol: str, interval: str = "1min") -> List[Dict[str, Any]]:
        """Get intraday price data for technical analysis"""
        data = self._make_request(f"historical-chart/{interval}/{symbol}")

        if data and isinstance(data, list):
            # Sort by date to ensure chronological order
            data.sort(key=lambda x: x.get('date', ''))
            return data

        return []
    
    def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """Get comprehensive market data for a symbol"""
        # Get quote data
        quote_data = self._make_request(f"quote/{symbol}")
        
        if not quote_data or not isinstance(quote_data, list) or len(quote_data) == 0:
            log_warning(f"No quote data for {symbol}")
            return None
        
        quote = quote_data[0]
        
        try:
            market_data = MarketData(
                symbol=quote.get('symbol', symbol),
                timestamp=datetime.now(),
                price=float(quote.get('price', 0)),
                volume=int(quote.get('volume', 0)),
                open_price=float(quote.get('open', 0)),
                high=float(quote.get('dayHigh', 0)),
                low=float(quote.get('dayLow', 0)),
                close=float(quote.get('previousClose', 0)),
                avg_volume=int(quote.get('avgVolume', 0)),
                market_cap=int(quote.get('marketCap', 0)) if quote.get('marketCap') else None
            )
            
            return market_data
            
        except (ValueError, TypeError) as e:
            log_error(f"Error parsing market data for {symbol}: {e}")
            return None
    
    def get_historical_data(self, symbol: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get historical price data"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        params = {
            'from': start_date.isoformat(),
            'to': end_date.isoformat()
        }
        
        data = self._make_request(f"historical-price-full/{symbol}", params)
        
        if data and 'historical' in data:
            return data['historical']
        
        return []
    
    def get_active_stocks(self) -> List[Dict[str, Any]]:
        """Get most active stocks by volume"""
        data = self._make_request("stock_market/actives")
        
        if data and isinstance(data, list):
            log_info(f"Retrieved {len(data)} active stocks")
            return data
        
        return []
    
    def get_gainers(self) -> List[Dict[str, Any]]:
        """Get top gaining stocks"""
        data = self._make_request("stock_market/gainers")
        
        if data and isinstance(data, list):
            log_info(f"Retrieved {len(data)} gaining stocks")
            return data
        
        return []
    
    def get_losers(self) -> List[Dict[str, Any]]:
        """Get top losing stocks"""
        data = self._make_request("stock_market/losers")
        
        if data and isinstance(data, list):
            log_info(f"Retrieved {len(data)} losing stocks")
            return data
        
        return []
    
    def search_stocks(self, query: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Search for stocks by name or symbol"""
        params = {
            'query': query,
            'limit': limit
        }
        
        data = self._make_request("search", params)
        
        if data and isinstance(data, list):
            return data
        
        return []
    
    def get_company_profile(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get company profile information"""
        data = self._make_request(f"profile/{symbol}")
        
        if data and isinstance(data, list) and len(data) > 0:
            return data[0]
        
        return None
    
    def get_market_hours(self) -> Dict[str, Any]:
        """Get market hours and status"""
        data = self._make_request("market-hours")
        
        if data:
            return data
        
        return {}
    
    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        market_hours = self.get_market_hours()

        if market_hours and isinstance(market_hours, dict):
            return market_hours.get('isTheStockMarketOpen', False)

        # Fallback: assume market is open during business hours EST
        now = datetime.now()
        if now.weekday() >= 5:  # Weekend
            return False

        # Rough market hours check (9:30 AM - 4:00 PM EST)
        hour = now.hour
        return 9 <= hour < 16
    
    async def get_real_time_prices_async(self, symbols: List[str]) -> Dict[str, float]:
        """Async version of get_real_time_prices"""
        if not symbols:
            return {}
        
        async with aiohttp.ClientSession() as session:
            tasks = []
            chunk_size = 50
            
            for i in range(0, len(symbols), chunk_size):
                chunk = symbols[i:i + chunk_size]
                symbol_string = ','.join(chunk)
                url = f"{self.base_url}/quote-short/{symbol_string}"
                params = {'apikey': self.api_key}
                
                task = self._fetch_async(session, url, params)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            all_prices = {}
            for result in results:
                if isinstance(result, list):
                    for item in result:
                        symbol = item.get('symbol')
                        price = item.get('price')
                        if symbol and price:
                            all_prices[symbol] = float(price)
            
            return all_prices
    
    async def _fetch_async(self, session: aiohttp.ClientSession, url: str, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Async HTTP request helper"""
        try:
            async with session.get(url, params=params, timeout=config.system.request_timeout) as response:
                if response.status == 200:
                    data = await response.json()
                    return data if isinstance(data, list) else []
                else:
                    log_error(f"Async request failed with status {response.status}")
                    return []
        except Exception as e:
            log_error(f"Async request error: {e}")
            return []

# Global data provider instance
data_provider = FMPDataProvider()

if __name__ == "__main__":
    # Test data provider
    try:
        # Test market status
        is_open = data_provider.is_market_open()
        print(f"Market is open: {is_open}")
        
        # Test stock screener
        stocks = data_provider.get_stock_screener(
            min_market_cap=**********,  # $1B
            min_volume=100000,
            min_price=5.0,
            max_price=500.0,
            limit=10
        )
        print(f"Found {len(stocks)} stocks")
        
        if stocks:
            # Test real-time price
            symbol = stocks[0]['symbol']
            price = data_provider.get_real_time_price(symbol)
            print(f"{symbol} price: ${price}")
            
            # Test market data
            market_data = data_provider.get_market_data(symbol)
            if market_data:
                print(f"Market data for {symbol}: {market_data}")
        
    except Exception as e:
        print(f"Test failed: {e}")
        print("Make sure to set your FMP_API_KEY in the .env file")
