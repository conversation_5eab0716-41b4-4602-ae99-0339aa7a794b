"""
Configuration management for the MassiveScan trading bot.
Handles API keys, trading parameters, and system settings.
"""

import os
from dotenv import load_dotenv
from dataclasses import dataclass
from typing import Dict, Any

# Load environment variables
load_dotenv()

@dataclass
class TradingConfig:
    """Trading strategy configuration"""
    # Profit targets
    target_profit_dollars: float = 1.00  # Target profit per trade
    target_profit_percent: float = 0.002  # 0.2% profit target (alternative)
    
    # Risk management
    stop_loss_dollars: float = 0.50  # Maximum loss per trade
    stop_loss_percent: float = 0.005  # 0.5% stop loss
    max_position_size: float = 500.00  # Maximum position size in dollars
    
    # Trading limits
    max_daily_trades: int = 500  # Maximum trades per day
    max_concurrent_positions: int = 50  # Maximum open positions
    max_daily_loss: float = 100.00  # Maximum daily loss limit
    
    # Scanning parameters
    min_volume: int = 100000  # Minimum daily volume
    min_price: float = 1.00  # Minimum stock price
    max_price: float = 500.00  # Maximum stock price
    min_market_cap: int = 100000000  # Minimum market cap ($100M)
    
    # Technical analysis thresholds
    rsi_oversold: float = 30
    rsi_overbought: float = 70
    volume_surge_multiplier: float = 2.0  # Volume must be 2x average
    momentum_threshold: float = 0.01  # 1% momentum threshold

@dataclass
class APIConfig:
    """API configuration and credentials"""
    # Financial Modeling Prep
    fmp_api_key: str = os.getenv('FMP_API_KEY', '')
    fmp_base_url: str = 'https://financialmodelingprep.com/api/v3'
    
    # Alpaca Trading
    alpaca_api_key: str = os.getenv('ALPACA_API_KEY', '')
    alpaca_secret_key: str = os.getenv('ALPACA_SECRET_KEY', '')
    alpaca_base_url: str = os.getenv('ALPACA_BASE_URL', 'https://paper-api.alpaca.markets')  # Paper trading by default
    
    # Rate limiting
    fmp_requests_per_minute: int = 300
    alpaca_requests_per_minute: int = 200

@dataclass
class SystemConfig:
    """System and application configuration"""
    # Database
    database_path: str = 'trading_bot.db'
    
    # Logging
    log_level: str = 'INFO'
    log_file: str = 'trading_bot.log'
    
    # Scanning intervals
    scan_interval_seconds: int = 30  # Scan every 30 seconds
    data_refresh_seconds: int = 60  # Refresh market data every minute
    
    # GUI settings
    gui_refresh_rate: int = 1000  # Refresh GUI every 1 second (milliseconds)
    
    # Performance
    max_worker_threads: int = 10
    request_timeout: int = 30

class Config:
    """Main configuration class"""
    
    def __init__(self):
        self.trading = TradingConfig()
        self.api = APIConfig()
        self.system = SystemConfig()
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate configuration settings"""
        if not self.api.fmp_api_key:
            raise ValueError("FMP_API_KEY environment variable is required")
        
        if not self.api.alpaca_api_key or not self.api.alpaca_secret_key:
            raise ValueError("Alpaca API credentials are required")
        
        if self.trading.target_profit_dollars <= 0:
            raise ValueError("Target profit must be positive")
        
        if self.trading.stop_loss_dollars <= 0:
            raise ValueError("Stop loss must be positive")
        
        if self.trading.max_daily_trades <= 0:
            raise ValueError("Max daily trades must be positive")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'trading': self.trading.__dict__,
            'api': {k: v for k, v in self.api.__dict__.items() if 'key' not in k.lower()},  # Exclude sensitive keys
            'system': self.system.__dict__
        }
    
    def update_trading_config(self, **kwargs):
        """Update trading configuration parameters"""
        for key, value in kwargs.items():
            if hasattr(self.trading, key):
                setattr(self.trading, key, value)
            else:
                raise ValueError(f"Unknown trading config parameter: {key}")

# Global configuration instance
config = Config()

# Environment setup helper
def setup_environment():
    """Setup environment variables and configuration"""
    env_file = '.env'
    
    if not os.path.exists(env_file):
        # Create sample .env file
        sample_env = """# Financial Modeling Prep API
FMP_API_KEY=your_fmp_api_key_here

# Alpaca Trading API
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# Optional: Production trading (use with caution)
# ALPACA_BASE_URL=https://api.alpaca.markets
"""
        with open(env_file, 'w') as f:
            f.write(sample_env)
        
        print(f"Created {env_file} file. Please add your API keys.")
        return False
    
    return True

if __name__ == "__main__":
    # Test configuration
    try:
        print("Configuration loaded successfully:")
        print(f"Target profit: ${config.trading.target_profit_dollars}")
        print(f"Stop loss: ${config.trading.stop_loss_dollars}")
        print(f"Max daily trades: {config.trading.max_daily_trades}")
        print(f"Scan interval: {config.system.scan_interval_seconds}s")
    except Exception as e:
        print(f"Configuration error: {e}")
        setup_environment()
