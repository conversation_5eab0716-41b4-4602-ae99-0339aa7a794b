"""
Configuration management for the MassiveScan trading bot.
Handles API keys, trading parameters, and system settings.
"""

import os
from dotenv import load_dotenv
from dataclasses import dataclass
from typing import Dict, Any

# Load environment variables
load_dotenv()

@dataclass
class TradingConfig:
    """Trading strategy configuration"""
    # LIVE TRADING STRATEGY - Optimized for $100+ daily profit with $30K account
    target_profit_dollars: float = 3.00  # Higher target for $100 daily goal
    target_profit_percent: float = 0.002  # 0.2% profit target (alternative)

    # ATR-based volatility adjustment
    use_atr_stops: bool = True  # Use ATR-based dynamic stops
    atr_period: int = 14  # Number of periods for ATR calculation
    stop_loss_atr_multiplier: float = 2.0  # Stop loss = entry - (2 * ATR)
    trailing_stop_atr_multiplier: float = 1.5  # Trailing stop = high - (1.5 * ATR)
    risk_per_trade_dollars: float = 15.00  # Higher risk per trade for $30K account

    # Enhanced profit strategies for $100 daily target
    use_trailing_stop: bool = True  # Let winners run with trailing stops
    trailing_stop_dollars: float = 1.50  # Higher fallback for bigger profits
    max_profit_target: float = 12.00  # Ride big winners to $12 for $100 goal
    partial_profit_taking: bool = False  # Keep it simple - full position exits
    partial_profit_percent: float = 0.5  # Not used with partial_profit_taking = False

    # Risk management - Optimized for $30K account and $100 daily target
    stop_loss_dollars: float = 1.50  # Higher stop loss for live trading
    stop_loss_percent: float = 0.005  # 0.5% stop loss
    max_position_size: float = 5000.00  # Bigger positions for $30K account ($5K max)

    # Trading limits - Live Trading for $100 daily target
    max_daily_trades: int = 40  # More trades for $100 target
    max_concurrent_positions: int = 10  # More positions for $30K account
    max_daily_loss: float = 75.00  # Higher loss limit for live trading ($30K account)
    
    # Scanning parameters - More conservative for $50 target
    min_volume: int = 500000  # Minimum daily volume (increased for better liquidity)
    min_price: float = 5.00  # Minimum stock price (avoid penny stocks)
    max_price: float = 200.00  # Maximum stock price (focus on mid-range stocks)
    min_market_cap: int = *********  # Minimum market cap ($500M for stability)
    
    # Technical analysis thresholds - Optimized for quality signals
    rsi_oversold: float = 25  # More extreme oversold for higher confidence
    rsi_overbought: float = 75  # More extreme overbought for higher confidence
    volume_surge_multiplier: float = 2.5  # Higher volume requirement for quality
    momentum_threshold: float = 0.015  # 1.5% minimum movement for stronger signals
    min_signal_confidence: float = 0.70  # Only trade 70%+ confidence signals

    # AI Enhancement settings
    use_ai_enhancement: bool = True  # Enable AI signal enhancement
    ai_confidence_boost_max: float = 0.20  # Maximum AI confidence boost
    ai_profit_prediction: bool = True  # Use AI profit predictions
    ai_market_regime_detection: bool = True  # Use AI market regime detection

@dataclass
class APIConfig:
    """API configuration and credentials"""
    # Financial Modeling Prep
    fmp_api_key: str = os.getenv('FMP_API_KEY', '')
    fmp_base_url: str = 'https://financialmodelingprep.com/api/v3'
    
    # Alpaca Trading - LIVE ACCOUNT
    alpaca_api_key: str = os.getenv('ALPACA_API_KEY', 'PKGYRBVHNVA0K7M54M0O')
    alpaca_secret_key: str = os.getenv('ALPACA_SECRET_KEY', 'AviNiUMW8KTx68t8IjFGDMlGveiiHSA6gIuS3fsL')
    alpaca_base_url: str = os.getenv('ALPACA_BASE_URL', 'https://api.alpaca.markets')  # LIVE TRADING
    
    # Rate limiting
    fmp_requests_per_minute: int = 300
    alpaca_requests_per_minute: int = 200

@dataclass
class SystemConfig:
    """System and application configuration"""
    # Database
    database_path: str = 'trading_bot.db'
    
    # Logging
    log_level: str = 'INFO'
    log_file: str = 'trading_bot.log'
    
    # Scanning intervals - Faster for real-time trading
    scan_interval_seconds: int = 15  # Scan every 15 seconds for faster signals
    data_refresh_seconds: int = 5   # Refresh market data every 5 seconds
    pnl_update_seconds: int = 2     # Update P&L every 2 seconds
    
    # GUI settings - Faster updates for real-time display
    gui_refresh_rate: int = 500   # Refresh GUI every 0.5 seconds (milliseconds)
    pnl_refresh_rate: int = 1000  # Update P&L every 1 second
    
    # Performance
    max_worker_threads: int = 10
    request_timeout: int = 30

class Config:
    """Main configuration class"""
    
    def __init__(self):
        self.trading = TradingConfig()
        self.api = APIConfig()
        self.system = SystemConfig()
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate configuration settings"""
        if not self.api.fmp_api_key:
            raise ValueError("FMP_API_KEY environment variable is required")
        
        if not self.api.alpaca_api_key or not self.api.alpaca_secret_key:
            raise ValueError("Alpaca API credentials are required")
        
        if self.trading.target_profit_dollars <= 0:
            raise ValueError("Target profit must be positive")
        
        if self.trading.stop_loss_dollars <= 0:
            raise ValueError("Stop loss must be positive")
        
        if self.trading.max_daily_trades <= 0:
            raise ValueError("Max daily trades must be positive")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'trading': self.trading.__dict__,
            'api': {k: v for k, v in self.api.__dict__.items() if 'key' not in k.lower()},  # Exclude sensitive keys
            'system': self.system.__dict__
        }
    
    def update_trading_config(self, **kwargs):
        """Update trading configuration parameters"""
        for key, value in kwargs.items():
            if hasattr(self.trading, key):
                setattr(self.trading, key, value)
            else:
                raise ValueError(f"Unknown trading config parameter: {key}")

# Global configuration instance
config = Config()

# Environment setup helper
def setup_environment():
    """Setup environment variables and configuration"""
    env_file = '.env'
    
    if not os.path.exists(env_file):
        # Create sample .env file
        sample_env = """# Financial Modeling Prep API
FMP_API_KEY=your_fmp_api_key_here

# Alpaca Trading API
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# Optional: Production trading (use with caution)
# ALPACA_BASE_URL=https://api.alpaca.markets
"""
        with open(env_file, 'w') as f:
            f.write(sample_env)
        
        print(f"Created {env_file} file. Please add your API keys.")
        return False
    
    return True

if __name__ == "__main__":
    # Test configuration
    try:
        print("Configuration loaded successfully:")
        print(f"Target profit: ${config.trading.target_profit_dollars}")
        print(f"Stop loss: ${config.trading.stop_loss_dollars}")
        print(f"Max daily trades: {config.trading.max_daily_trades}")
        print(f"Scan interval: {config.system.scan_interval_seconds}s")
    except Exception as e:
        print(f"Configuration error: {e}")
        setup_environment()
