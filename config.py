"""
Configuration management for the MassiveScan trading bot.
Handles API keys, trading parameters, and system settings.
"""

import os
from dotenv import load_dotenv
from dataclasses import dataclass
from typing import Dict, Any

# Load environment variables
load_dotenv()

@dataclass
class TradingConfig:
    """Trading strategy configuration"""
    # VOLATILITY-ADJUSTED ATR STRATEGY - Optimized for $75-120 daily profit
    target_profit_dollars: float = 2.00  # Higher initial target for bigger profits
    target_profit_percent: float = 0.002  # 0.2% profit target (alternative)

    # ATR-based volatility adjustment
    use_atr_stops: bool = True  # Use ATR-based dynamic stops
    atr_period: int = 14  # Number of periods for ATR calculation
    stop_loss_atr_multiplier: float = 2.0  # Stop loss = entry - (2 * ATR)
    trailing_stop_atr_multiplier: float = 1.5  # Trailing stop = high - (1.5 * ATR)
    risk_per_trade_dollars: float = 10.00  # Fixed risk per trade

    # Enhanced profit strategies
    use_trailing_stop: bool = True  # Let winners run with trailing stops
    trailing_stop_dollars: float = 1.00  # Fallback if ATR unavailable
    max_profit_target: float = 8.00  # Ride big winners to $8
    partial_profit_taking: bool = False  # Keep it simple - full position exits
    partial_profit_percent: float = 0.5  # Not used with partial_profit_taking = False

    # Risk management - Optimized for bigger profits
    stop_loss_dollars: float = 1.00  # Slightly higher stop loss for bigger positions
    stop_loss_percent: float = 0.005  # 0.5% stop loss
    max_position_size: float = 2000.00  # Bigger positions for bigger profits ($2K max)

    # Trading limits - Smart Momentum Strategy (Quality over Quantity)
    max_daily_trades: int = 30  # Focus on high-quality trades
    max_concurrent_positions: int = 8  # Manageable number of positions
    max_daily_loss: float = 40.00  # Higher loss limit for bigger position strategy
    
    # Scanning parameters - More conservative for $50 target
    min_volume: int = 500000  # Minimum daily volume (increased for better liquidity)
    min_price: float = 5.00  # Minimum stock price (avoid penny stocks)
    max_price: float = 200.00  # Maximum stock price (focus on mid-range stocks)
    min_market_cap: int = 500000000  # Minimum market cap ($500M for stability)
    
    # Technical analysis thresholds - Optimized for quality signals
    rsi_oversold: float = 25  # More extreme oversold for higher confidence
    rsi_overbought: float = 75  # More extreme overbought for higher confidence
    volume_surge_multiplier: float = 2.5  # Higher volume requirement for quality
    momentum_threshold: float = 0.015  # 1.5% minimum movement for stronger signals
    min_signal_confidence: float = 0.70  # Only trade 70%+ confidence signals

    # AI Enhancement settings
    use_ai_enhancement: bool = True  # Enable AI signal enhancement
    ai_confidence_boost_max: float = 0.20  # Maximum AI confidence boost
    ai_profit_prediction: bool = True  # Use AI profit predictions
    ai_market_regime_detection: bool = True  # Use AI market regime detection

@dataclass
class APIConfig:
    """API configuration and credentials"""
    # Financial Modeling Prep
    fmp_api_key: str = os.getenv('FMP_API_KEY', '')
    fmp_base_url: str = 'https://financialmodelingprep.com/api/v3'
    
    # Alpaca Trading
    alpaca_api_key: str = os.getenv('ALPACA_API_KEY', '')
    alpaca_secret_key: str = os.getenv('ALPACA_SECRET_KEY', '')
    alpaca_base_url: str = os.getenv('ALPACA_BASE_URL', 'https://paper-api.alpaca.markets')  # Paper trading by default
    
    # Rate limiting
    fmp_requests_per_minute: int = 300
    alpaca_requests_per_minute: int = 200

@dataclass
class SystemConfig:
    """System and application configuration"""
    # Database
    database_path: str = 'trading_bot.db'
    
    # Logging
    log_level: str = 'INFO'
    log_file: str = 'trading_bot.log'
    
    # Scanning intervals - Faster for real-time trading
    scan_interval_seconds: int = 15  # Scan every 15 seconds for faster signals
    data_refresh_seconds: int = 5   # Refresh market data every 5 seconds
    pnl_update_seconds: int = 2     # Update P&L every 2 seconds
    
    # GUI settings - Faster updates for real-time display
    gui_refresh_rate: int = 500   # Refresh GUI every 0.5 seconds (milliseconds)
    pnl_refresh_rate: int = 1000  # Update P&L every 1 second
    
    # Performance
    max_worker_threads: int = 10
    request_timeout: int = 30

class Config:
    """Main configuration class"""
    
    def __init__(self):
        self.trading = TradingConfig()
        self.api = APIConfig()
        self.system = SystemConfig()
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate configuration settings"""
        if not self.api.fmp_api_key:
            raise ValueError("FMP_API_KEY environment variable is required")
        
        if not self.api.alpaca_api_key or not self.api.alpaca_secret_key:
            raise ValueError("Alpaca API credentials are required")
        
        if self.trading.target_profit_dollars <= 0:
            raise ValueError("Target profit must be positive")
        
        if self.trading.stop_loss_dollars <= 0:
            raise ValueError("Stop loss must be positive")
        
        if self.trading.max_daily_trades <= 0:
            raise ValueError("Max daily trades must be positive")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'trading': self.trading.__dict__,
            'api': {k: v for k, v in self.api.__dict__.items() if 'key' not in k.lower()},  # Exclude sensitive keys
            'system': self.system.__dict__
        }
    
    def update_trading_config(self, **kwargs):
        """Update trading configuration parameters"""
        for key, value in kwargs.items():
            if hasattr(self.trading, key):
                setattr(self.trading, key, value)
            else:
                raise ValueError(f"Unknown trading config parameter: {key}")

# Global configuration instance
config = Config()

# Environment setup helper
def setup_environment():
    """Setup environment variables and configuration"""
    env_file = '.env'
    
    if not os.path.exists(env_file):
        # Create sample .env file
        sample_env = """# Financial Modeling Prep API
FMP_API_KEY=your_fmp_api_key_here

# Alpaca Trading API
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# Optional: Production trading (use with caution)
# ALPACA_BASE_URL=https://api.alpaca.markets
"""
        with open(env_file, 'w') as f:
            f.write(sample_env)
        
        print(f"Created {env_file} file. Please add your API keys.")
        return False
    
    return True

if __name__ == "__main__":
    # Test configuration
    try:
        print("Configuration loaded successfully:")
        print(f"Target profit: ${config.trading.target_profit_dollars}")
        print(f"Stop loss: ${config.trading.stop_loss_dollars}")
        print(f"Max daily trades: {config.trading.max_daily_trades}")
        print(f"Scan interval: {config.system.scan_interval_seconds}s")
    except Exception as e:
        print(f"Configuration error: {e}")
        setup_environment()
