"""
GUI interface for the MassiveScan trading bot.
Provides a desktop application with real-time monitoring and controls.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import asyncio
from datetime import datetime
import json

from main import MassiveScanBot
from trade_manager import trade_manager
from risk_manager import risk_manager
from scanner import scanner
from broker import broker
from database import db
from config import config
from logger import log_info, log_error

class TradingBotGUI:
    """GUI application for the trading bot"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("MassiveScan - Automated Trading Bot")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # Bot instance
        self.bot = None
        self.bot_thread = None
        self.running = False
        
        # GUI update timer
        self.update_timer = None
        
        # Create GUI elements
        self.create_widgets()
        self.setup_styles()
        
        # Start GUI updates
        self.schedule_updates()

        # Add welcome message
        self.add_welcome_message()
    
    def setup_styles(self):
        """Setup custom styles for the GUI"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure colors
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#2b2b2b', foreground='white')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), background='#2b2b2b', foreground='white')
        style.configure('Info.TLabel', font=('Arial', 10), background='#2b2b2b', foreground='white')
        style.configure('Success.TLabel', font=('Arial', 10), background='#2b2b2b', foreground='#4CAF50')
        style.configure('Warning.TLabel', font=('Arial', 10), background='#2b2b2b', foreground='#FF9800')
        style.configure('Error.TLabel', font=('Arial', 10), background='#2b2b2b', foreground='#F44336')
    
    def create_widgets(self):
        """Create all GUI widgets"""
        # Main title
        title_frame = tk.Frame(self.root, bg='#2b2b2b')
        title_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(title_frame, text="MassiveScan Trading Bot", style='Title.TLabel').pack()
        
        # Control buttons
        control_frame = tk.Frame(self.root, bg='#2b2b2b')
        control_frame.pack(fill='x', padx=10, pady=5)
        
        self.start_button = tk.Button(control_frame, text="Start Bot", command=self.start_bot,
                                     bg='#4CAF50', fg='white', font=('Arial', 12, 'bold'))
        self.start_button.pack(side='left', padx=5)
        
        self.stop_button = tk.Button(control_frame, text="Stop Bot", command=self.stop_bot,
                                    bg='#F44336', fg='white', font=('Arial', 12, 'bold'), state='disabled')
        self.stop_button.pack(side='left', padx=5)
        
        self.close_positions_button = tk.Button(control_frame, text="Close All Positions", 
                                               command=self.close_all_positions,
                                               bg='#FF9800', fg='white', font=('Arial', 10))
        self.close_positions_button.pack(side='left', padx=5)
        
        # Status indicator
        self.status_label = ttk.Label(control_frame, text="Status: Stopped", style='Info.TLabel')
        self.status_label.pack(side='right', padx=5)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Dashboard tab
        self.create_dashboard_tab()
        
        # Active trades tab
        self.create_trades_tab()
        
        # Performance tab
        self.create_performance_tab()
        
        # Settings tab
        self.create_settings_tab()
        
        # Logs tab
        self.create_logs_tab()
    
    def create_dashboard_tab(self):
        """Create the main dashboard tab"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="Dashboard")
        
        # Account info section
        account_frame = ttk.LabelFrame(dashboard_frame, text="Account Information")
        account_frame.pack(fill='x', padx=5, pady=5)
        
        self.account_info = {}
        account_labels = ['Buying Power', 'Portfolio Value', 'Cash', 'Day Trade Count']
        
        for label in account_labels:
            row_frame = tk.Frame(account_frame, bg='white')
            row_frame.pack(fill='x', padx=5, pady=2)
            
            tk.Label(row_frame, text=f"{label}:", font=('Arial', 10), bg='white').pack(side='left')
            self.account_info[label] = tk.Label(row_frame, text="$0.00", font=('Arial', 10, 'bold'), bg='white')
            self.account_info[label].pack(side='right')
        
        # Trading statistics section
        stats_frame = ttk.LabelFrame(dashboard_frame, text="Trading Statistics")
        stats_frame.pack(fill='x', padx=5, pady=5)
        
        self.trading_stats = {}
        stats_labels = ['Daily Trades', 'Active Positions', 'Daily P&L', 'Win Rate', 'Signals Found']
        
        for label in stats_labels:
            row_frame = tk.Frame(stats_frame, bg='white')
            row_frame.pack(fill='x', padx=5, pady=2)
            
            tk.Label(row_frame, text=f"{label}:", font=('Arial', 10), bg='white').pack(side='left')
            self.trading_stats[label] = tk.Label(row_frame, text="0", font=('Arial', 10, 'bold'), bg='white')
            self.trading_stats[label].pack(side='right')
        
        # Risk metrics section
        risk_frame = ttk.LabelFrame(dashboard_frame, text="Risk Metrics")
        risk_frame.pack(fill='x', padx=5, pady=5)
        
        self.risk_metrics = {}
        risk_labels = ['Daily Loss Limit Used', 'Trade Limit Used', 'Consecutive Losses', 'Largest Loss']
        
        for label in risk_labels:
            row_frame = tk.Frame(risk_frame, bg='white')
            row_frame.pack(fill='x', padx=5, pady=2)
            
            tk.Label(row_frame, text=f"{label}:", font=('Arial', 10), bg='white').pack(side='left')
            self.risk_metrics[label] = tk.Label(row_frame, text="0", font=('Arial', 10, 'bold'), bg='white')
            self.risk_metrics[label].pack(side='right')
    
    def create_trades_tab(self):
        """Create the active trades tab"""
        trades_frame = ttk.Frame(self.notebook)
        self.notebook.add(trades_frame, text="Active Trades")
        
        # Trades table
        columns = ('Symbol', 'Side', 'Quantity', 'Entry Price', 'Current Price', 'P&L', 'Duration', 'Strategy')
        self.trades_tree = ttk.Treeview(trades_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.trades_tree.heading(col, text=col)
            self.trades_tree.column(col, width=100)
        
        # Scrollbar for trades table
        trades_scrollbar = ttk.Scrollbar(trades_frame, orient='vertical', command=self.trades_tree.yview)
        self.trades_tree.configure(yscrollcommand=trades_scrollbar.set)
        
        self.trades_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        trades_scrollbar.pack(side='right', fill='y')
    
    def create_performance_tab(self):
        """Create the performance tab"""
        performance_frame = ttk.Frame(self.notebook)
        self.notebook.add(performance_frame, text="Performance")
        
        # Performance summary
        summary_frame = ttk.LabelFrame(performance_frame, text="Performance Summary (Last 7 Days)")
        summary_frame.pack(fill='x', padx=5, pady=5)
        
        self.performance_labels = {}
        perf_metrics = ['Total Trades', 'Winning Trades', 'Win Rate', 'Total P&L', 'Average P&L', 'Max Win', 'Max Loss']
        
        for metric in perf_metrics:
            row_frame = tk.Frame(summary_frame, bg='white')
            row_frame.pack(fill='x', padx=5, pady=2)
            
            tk.Label(row_frame, text=f"{metric}:", font=('Arial', 10), bg='white').pack(side='left')
            self.performance_labels[metric] = tk.Label(row_frame, text="0", font=('Arial', 10, 'bold'), bg='white')
            self.performance_labels[metric].pack(side='right')
        
        # Export button
        export_frame = tk.Frame(performance_frame, bg='white')
        export_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(export_frame, text="Export Trade History", command=self.export_trades,
                 bg='#2196F3', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
    
    def create_settings_tab(self):
        """Create the settings tab"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="Settings")
        
        # Trading settings
        trading_frame = ttk.LabelFrame(settings_frame, text="Trading Settings")
        trading_frame.pack(fill='x', padx=5, pady=5)
        
        self.settings_vars = {}
        
        # Target profit
        tk.Label(trading_frame, text="Target Profit ($):").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.settings_vars['target_profit'] = tk.StringVar(value=str(config.trading.target_profit_dollars))
        tk.Entry(trading_frame, textvariable=self.settings_vars['target_profit']).grid(row=0, column=1, padx=5, pady=2)
        
        # Stop loss
        tk.Label(trading_frame, text="Stop Loss ($):").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.settings_vars['stop_loss'] = tk.StringVar(value=str(config.trading.stop_loss_dollars))
        tk.Entry(trading_frame, textvariable=self.settings_vars['stop_loss']).grid(row=1, column=1, padx=5, pady=2)
        
        # Max daily trades
        tk.Label(trading_frame, text="Max Daily Trades:").grid(row=2, column=0, sticky='w', padx=5, pady=2)
        self.settings_vars['max_trades'] = tk.StringVar(value=str(config.trading.max_daily_trades))
        tk.Entry(trading_frame, textvariable=self.settings_vars['max_trades']).grid(row=2, column=1, padx=5, pady=2)
        
        # Max concurrent positions
        tk.Label(trading_frame, text="Max Concurrent Positions:").grid(row=3, column=0, sticky='w', padx=5, pady=2)
        self.settings_vars['max_positions'] = tk.StringVar(value=str(config.trading.max_concurrent_positions))
        tk.Entry(trading_frame, textvariable=self.settings_vars['max_positions']).grid(row=3, column=1, padx=5, pady=2)
        
        # Save settings button
        tk.Button(trading_frame, text="Save Settings", command=self.save_settings,
                 bg='#4CAF50', fg='white', font=('Arial', 10)).grid(row=4, column=0, columnspan=2, pady=10)
    
    def create_logs_tab(self):
        """Create the logs tab"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="Logs")
        
        # Log display
        self.log_text = scrolledtext.ScrolledText(logs_frame, height=30, bg='black', fg='white', font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Clear logs button
        tk.Button(logs_frame, text="Clear Logs", command=self.clear_logs,
                 bg='#FF9800', fg='white', font=('Arial', 10)).pack(pady=5)
    
    def start_bot(self):
        """Start the trading bot"""
        try:
            # Add to log display
            self.add_log_message("🚀 Start button clicked!")

            if self.running:
                messagebox.showwarning("Warning", "Bot is already running!")
                return

            self.add_log_message("Creating MassiveScan bot instance...")
            self.bot = MassiveScanBot()
            self.running = True

            self.add_log_message("Starting bot in background thread...")
            # Start bot in separate thread
            self.bot_thread = threading.Thread(target=self.run_bot_async, daemon=True)
            self.bot_thread.start()

            # Update UI
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')
            self.status_label.config(text="Status: Starting...")

            self.add_log_message("✅ Bot started successfully!")
            log_info("Bot started from GUI")

            # Show success message
            messagebox.showinfo("Success", "Trading bot started! Check the logs tab for activity.")

        except Exception as e:
            error_msg = f"Failed to start bot: {e}"
            self.add_log_message(f"❌ {error_msg}")
            messagebox.showerror("Error", error_msg)
            log_error(f"Failed to start bot from GUI: {e}")
    
    def run_bot_async(self):
        """Run the bot asynchronously"""
        try:
            self.add_log_message("Setting up async event loop...")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            self.add_log_message("Initializing trading bot...")
            self.add_log_message("Bot will start scanning for trading opportunities...")

            loop.run_until_complete(self.bot.run())

        except Exception as e:
            error_msg = f"Bot execution error: {e}"
            self.add_log_message(f"❌ {error_msg}")
            log_error(error_msg)
        finally:
            self.running = False
            self.add_log_message("Bot execution finished")
    
    def stop_bot(self):
        """Stop the trading bot"""
        try:
            self.add_log_message("🛑 Stop button clicked!")

            if not self.running:
                messagebox.showwarning("Warning", "Bot is not running!")
                return

            self.add_log_message("Stopping trading bot...")
            if self.bot:
                self.bot.stop()

            self.running = False

            # Update UI
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
            self.status_label.config(text="Status: Stopped")

            self.add_log_message("✅ Bot stopped successfully!")
            log_info("Bot stopped from GUI")

        except Exception as e:
            error_msg = f"Failed to stop bot: {e}"
            self.add_log_message(f"❌ {error_msg}")
            messagebox.showerror("Error", error_msg)
            log_error(f"Failed to stop bot from GUI: {e}")
    
    def close_all_positions(self):
        """Close all open positions"""
        try:
            result = messagebox.askyesno("Confirm", "Are you sure you want to close all positions?")
            if result:
                trade_manager.close_all_positions("Manual close from GUI")
                messagebox.showinfo("Success", "All positions closed")
                log_info("All positions closed from GUI")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to close positions: {e}")
            log_error(f"Failed to close positions from GUI: {e}")
    
    def save_settings(self):
        """Save trading settings"""
        try:
            # Update configuration
            config.trading.target_profit_dollars = float(self.settings_vars['target_profit'].get())
            config.trading.stop_loss_dollars = float(self.settings_vars['stop_loss'].get())
            config.trading.max_daily_trades = int(self.settings_vars['max_trades'].get())
            config.trading.max_concurrent_positions = int(self.settings_vars['max_positions'].get())
            
            messagebox.showinfo("Success", "Settings saved successfully")
            log_info("Settings updated from GUI")
            
        except ValueError as e:
            messagebox.showerror("Error", "Invalid settings values")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {e}")
    
    def export_trades(self):
        """Export trade history"""
        try:
            from tkinter import filedialog
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )
            
            if filename:
                # Get trade history and export
                performance_summary = db.get_performance_summary(30)  # Last 30 days
                
                with open(filename, 'w') as f:
                    f.write("Trade History Export\n")
                    f.write(f"Export Date: {datetime.now().isoformat()}\n")
                    f.write(f"Performance Summary: {json.dumps(performance_summary, indent=2)}\n")
                
                messagebox.showinfo("Success", f"Trade history exported to {filename}")
                log_info(f"Trade history exported to {filename}")
        
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export trades: {e}")
    
    def clear_logs(self):
        """Clear the log display"""
        self.log_text.delete(1.0, tk.END)

    def add_log_message(self, message):
        """Add a message to the log display"""
        import time
        timestamp = time.strftime("%H:%M:%S")
        log_line = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_line)
        self.log_text.see(tk.END)
        self.root.update_idletasks()  # Force GUI update

    def add_welcome_message(self):
        """Add welcome message to logs"""
        self.add_log_message("=" * 50)
        self.add_log_message("🚀 MASSIVESCAN TRADING BOT")
        self.add_log_message("Target: $50 Daily Profit")
        self.add_log_message("Strategy: $1 profit per trade, $0.50 stop loss")
        self.add_log_message("=" * 50)
        self.add_log_message("Ready to start! Click 'Start Bot' to begin trading.")
        self.add_log_message("")
    
    def update_gui(self):
        """Update GUI with current data"""
        try:
            # Update status
            if self.running:
                self.status_label.config(text="Status: Running")
            else:
                self.status_label.config(text="Status: Stopped")
            
            # Update account info
            try:
                account_info = broker.get_account_info()
                if account_info:
                    self.account_info['Buying Power'].config(text=f"${account_info.get('buying_power', 0):,.2f}")
                    self.account_info['Portfolio Value'].config(text=f"${account_info.get('portfolio_value', 0):,.2f}")
                    self.account_info['Cash'].config(text=f"${account_info.get('cash', 0):,.2f}")
                    self.account_info['Day Trade Count'].config(text=str(account_info.get('day_trade_count', 0)))
            except:
                pass
            
            # Update trading statistics
            try:
                trade_summary = trade_manager.get_trade_summary()
                risk_summary = risk_manager.get_risk_summary()
                scan_stats = scanner.get_scan_statistics()
                
                self.trading_stats['Daily Trades'].config(text=str(trade_summary['trades_today']))
                self.trading_stats['Active Positions'].config(text=str(trade_summary['active_trades']))
                self.trading_stats['Daily P&L'].config(text=f"${risk_summary['daily_pnl']:.2f}")
                self.trading_stats['Win Rate'].config(text=f"{trade_summary['win_rate']:.1%}")
                self.trading_stats['Signals Found'].config(text=str(scan_stats['signals_found']))
                
                # Update risk metrics
                self.risk_metrics['Daily Loss Limit Used'].config(text=f"{risk_summary['loss_limit_used_pct']:.1f}%")
                self.risk_metrics['Trade Limit Used'].config(text=f"{risk_summary['trade_limit_used_pct']:.1f}%")
                self.risk_metrics['Consecutive Losses'].config(text=str(risk_summary['consecutive_losses']))
                self.risk_metrics['Largest Loss'].config(text=f"${risk_summary['largest_loss']:.2f}")
            except:
                pass
            
            # Update active trades
            try:
                self.trades_tree.delete(*self.trades_tree.get_children())
                active_trades = trade_manager.get_active_trades_info()
                
                for trade in active_trades:
                    pnl_color = 'green' if trade['unrealized_pnl'] >= 0 else 'red'
                    self.trades_tree.insert('', 'end', values=(
                        trade['symbol'],
                        trade['side'],
                        trade['quantity'],
                        f"${trade['entry_price']:.4f}",
                        f"${trade['current_price']:.4f}",
                        f"${trade['unrealized_pnl']:.2f}",
                        f"{trade['duration_minutes']:.1f}m",
                        trade['strategy']
                    ), tags=(pnl_color,))
                
                self.trades_tree.tag_configure('green', foreground='green')
                self.trades_tree.tag_configure('red', foreground='red')
            except:
                pass
            
            # Update performance
            try:
                performance_summary = db.get_performance_summary(7)  # Last 7 days
                if performance_summary:
                    self.performance_labels['Total Trades'].config(text=str(performance_summary['total_trades']))
                    self.performance_labels['Winning Trades'].config(text=str(performance_summary['winning_trades']))
                    self.performance_labels['Win Rate'].config(text=f"{performance_summary['win_rate']:.1%}")
                    self.performance_labels['Total P&L'].config(text=f"${performance_summary['total_pnl']:.2f}")
                    self.performance_labels['Average P&L'].config(text=f"${performance_summary['avg_pnl']:.2f}")
                    self.performance_labels['Max Win'].config(text=f"${performance_summary['max_win']:.2f}")
                    self.performance_labels['Max Loss'].config(text=f"${performance_summary['max_loss']:.2f}")
            except:
                pass
            
        except Exception as e:
            log_error(f"Error updating GUI: {e}")
    
    def schedule_updates(self):
        """Schedule periodic GUI updates"""
        self.update_gui()
        self.update_timer = self.root.after(5000, self.schedule_updates)  # Update every 5 seconds
    
    def run(self):
        """Run the GUI application"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            log_error(f"GUI error: {e}")
    
    def on_closing(self):
        """Handle window closing"""
        if self.running:
            result = messagebox.askyesno("Confirm Exit", "Bot is still running. Stop bot and exit?")
            if result:
                self.stop_bot()
                self.root.destroy()
        else:
            self.root.destroy()

if __name__ == "__main__":
    app = TradingBotGUI()
    app.run()
